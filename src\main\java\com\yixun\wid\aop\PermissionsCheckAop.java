package com.yixun.wid.aop;

import com.alibaba.fastjson.JSON;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Permission;
import com.yixun.wid.exception.PermissionErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.RolePermissionService;
import com.yixun.wid.utils.AdminUserHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;

@Aspect
@Component
public class PermissionsCheckAop {

    @Resource
    private AdministratorService administratorService;

    @Resource
    private RolePermissionService rolePermissionService;

    @Around("@annotation(RequiredPermission)")
    public Object postExec(ProceedingJoinPoint invocation) throws Throwable {
        return checkPermissions(invocation);
    }

    private Object checkPermissions(ProceedingJoinPoint pjp) throws Throwable {
        // 获取切入的 Method
        MethodSignature joinPointObject = (MethodSignature) pjp.getSignature();
        Method method = joinPointObject.getMethod();
        RequiredPermission requiredPermission = method.getAnnotation(RequiredPermission.class);
        String required = requiredPermission.value();

        Administrator administrator = administratorService.getAdministratorById(AdminUserHelper.getCurrentUserId());
        List<String> rolesList = JSON.parseArray(administrator.getRoles(), String.class);
        if (rolesList.isEmpty()){
            throw new PermissionErrorException("权限认证未通过");
        }
        if (rolesList.contains("Admin")){
            return pjp.proceed();
        }
        List<Permission> permissionsList = rolePermissionService.getPermissionListByRoleList(rolesList);
        List<String> permissions = permissionsList.stream().map(Permission::getName).collect(Collectors.toList());
        if (!permissions.contains(required)){
            throw new PermissionErrorException("权限认证未通过");
        }

        return pjp.proceed();
    }

}
