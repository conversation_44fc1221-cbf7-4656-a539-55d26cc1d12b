package com.yixun.wid.entity;

import java.util.List;

public class CorpInfo {

    /**
     * total : 26
     * items : [{"regStatus":"存续","estiblishTime":"2011-07-12 00:00:00.0","regCapital":"1000万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"黄晓","regNumber":"510107000389230","creditCode":"915101075773520003","name":"成都易训企业管理咨询有限公司","id":*********,"orgNumber":"57735200-0","base":"四川"},{"regStatus":"存续","estiblishTime":"2014-08-01 00:00:00.0","regCapital":"100万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"李菊昌","regNumber":"510105000417161","creditCode":"91510105395132955A","name":"成都易训教育咨询有限公司","id":*********,"orgNumber":"39513295-5","base":"四川"},{"regStatus":"存续","estiblishTime":"2021-02-08 00:00:00.0","regCapital":"100万人民币","companyType":1,"matchType":"历史名称匹配","type":1,"legalPersonName":"卢伟杰","regNumber":"***************","creditCode":"91440300MA5GLRG784","name":"易训管理咨询（深圳）有限公司","id":4311002508,"orgNumber":"MA5GLRG7-8","base":"广东"},{"regStatus":"存续","estiblishTime":"2016-04-08 00:00:00.0","regCapital":"500万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"曹传丽","regNumber":"***************","creditCode":"91370100MA3C8Q8Y7U","name":"济南联合易训企业管理咨询有限公司","id":2372266023,"orgNumber":"MA3C8Q8Y7","base":"山东"},{"regStatus":"存续","estiblishTime":"2010-01-27 00:00:00.0","regCapital":"500万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"李谦","regNumber":"110108012596046","creditCode":"911101086963068242","name":"北京易训天下咨询服务有限公司","id":36656471,"orgNumber":"69630682-4","base":"北京"},{"regStatus":"存续","estiblishTime":"2016-11-10 00:00:00.0","regCapital":"200万人民币","companyType":1,"matchType":"历史名称匹配","type":1,"legalPersonName":"胡海","regNumber":"360106210057088","creditCode":"91360106MA35L77C3R","name":"江西耀启电子商务有限公司","id":2962194025,"orgNumber":"MA35L77C-3","base":"江西"},{"regStatus":"注销","estiblishTime":"2021-06-30 00:00:00.0","regCapital":"500万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"李韬","regNumber":"***************","creditCode":"91441402MA56NXPL8A","name":"易训（广东）教育咨询有限公司","id":5020014922,"orgNumber":"MA56NXPL-8","base":"广东"},{"regStatus":"存续","estiblishTime":"2015-10-28 00:00:00.0","regCapital":"300万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"田素芹","regNumber":"***************","creditCode":"91370303MA3BY0JF1J","name":"济南易迅咨询服务有限公司","id":3386510131,"orgNumber":"MA3BY0JF-1","base":"山东"},{"regStatus":"存续","estiblishTime":"2023-02-27 00:00:00.0","regCapital":"-","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"陈明霞","regNumber":"***************","creditCode":"91440106MAC8EKRJ4F","name":"成都易训企业管理咨询有限公司广东分公司","id":5934398664,"orgNumber":"MAC8EKRJ-4","base":""},{"regStatus":"存续","estiblishTime":"2023-04-12 00:00:00.0","regCapital":"-","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"曾晓溪","regNumber":"320191000703552","creditCode":"91320191MACDW0160J","name":"成都易训企业管理咨询有限公司南京分公司","id":6092476391,"orgNumber":"MACDW016-0","base":""},{"regStatus":"存续","estiblishTime":"2021-03-16 00:00:00.0","regCapital":"100万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"郑碧辉","regNumber":"***************","creditCode":"91460000MA5TWRT48A","name":"海南易训启航企业管理咨询有限公司","id":4350400824,"orgNumber":"MA5TWRT4-8","base":"海南"},{"regStatus":"存续","estiblishTime":"2020-12-22 00:00:00.0","regCapital":"-","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"郭安凯","regNumber":"530121300147178","creditCode":"91530114MA6Q1FYN2H","name":"成都易训企业管理咨询有限公司云南分公司","id":4115180679,"orgNumber":"MA6Q1FYN-2","base":"云南"},{"regStatus":"存续","estiblishTime":"2023-05-22 00:00:00.0","regCapital":"-","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"李兹果","regNumber":"***************","creditCode":"92460000MACJPUY78F","name":"海口龙华区易训驾陪信息咨询工作室","id":6188059273,"orgNumber":"MACJPUY7-8","base":""},{"regStatus":"存续","estiblishTime":"2020-12-22 00:00:00.0","regCapital":"-","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"吴伊宁","regNumber":"330184602482613","creditCode":"92330110MA2KCQLA0C","name":"杭州余杭易训财务咨询工作室","id":4118925072,"orgNumber":"MA2KCQLA-0","base":"浙江"},{"regStatus":"存续","estiblishTime":"2020-07-06 00:00:00.0","regCapital":"100万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"冉松松","regNumber":"110111029064872","creditCode":"91110111MA01TCKKXG","name":"北京易训启航管理咨询有限公司","id":3453252266,"orgNumber":"MA01TCKK-X","base":"北京"},{"regStatus":"吊销","estiblishTime":"2000-10-31 00:00:00.0","regCapital":"10万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"周铨","regNumber":"3101102003172","creditCode":"","name":"上海易训企业管理咨询有限公司","id":2315418888,"orgNumber":"","base":"上海"},{"regStatus":"存续","estiblishTime":"2013-08-05 00:00:00.0","regCapital":"30万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"吴保亭","regNumber":"310110000639752","creditCode":"913101100748218568","name":"上海易训通企业管理咨询有限公司","id":1090921329,"orgNumber":"07482185-6","base":"上海"},{"regStatus":"注销","estiblishTime":"2014-05-23 00:00:00.0","regCapital":"10万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"何敬武","regNumber":"***************","creditCode":"91370203399230675U","name":"青岛联合易训管理咨询有限公司","id":1629711591,"orgNumber":"39923067-5","base":"山东"},{"regStatus":"注销","estiblishTime":"2013-05-29 00:00:00.0","regCapital":"500万人民币","companyType":1,"matchType":"历史名称匹配","type":1,"legalPersonName":"符文亮","regNumber":"110102015951506","creditCode":"911101020695832757","name":"北京易训科技有限公司","id":3373585410,"orgNumber":"06958327-5","base":"北京"},{"regStatus":"注销","estiblishTime":"2014-07-22 00:00:00.0","regCapital":"393万人民币","companyType":1,"matchType":"经营范围匹配","type":1,"legalPersonName":"李谦","regNumber":"120222000265702","creditCode":"911202223007147776","name":"易训天下（天津）企业管理咨询有限公司","id":*********,"orgNumber":"30071477-7","base":"天津"}]
     */

    private Integer total;
    private List<ItemsBean> items;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<ItemsBean> getItems() {
        return items;
    }

    public void setItems(List<ItemsBean> items) {
        this.items = items;
    }

    public static class ItemsBean {
        /**
         * regStatus : 存续
         * estiblishTime : 2011-07-12 00:00:00.0
         * regCapital : 1000万人民币
         * companyType : 1
         * matchType : 经营范围匹配
         * type : 1
         * legalPersonName : 黄晓
         * regNumber : 510107000389230
         * creditCode : 915101075773520003
         * name : 成都易训企业管理咨询有限公司
         * id : *********
         * orgNumber : 57735200-0
         * base : 四川
         */

        private String regStatus;
        private String estiblishTime;
        private String regCapital;
        private Integer companyType;
        private String matchType;
        private String legalPersonName;
        private String regNumber;
        private String creditCode;
        private String name;
        private String orgNumber;
        private String base;

        public String getRegStatus() {
            return regStatus;
        }

        public void setRegStatus(String regStatus) {
            this.regStatus = regStatus;
        }

        public String getEstiblishTime() {
            return estiblishTime;
        }

        public void setEstiblishTime(String estiblishTime) {
            this.estiblishTime = estiblishTime;
        }

        public String getRegCapital() {
            return regCapital;
        }

        public void setRegCapital(String regCapital) {
            this.regCapital = regCapital;
        }

        public Integer getCompanyType() {
            return companyType;
        }

        public void setCompanyType(Integer companyType) {
            this.companyType = companyType;
        }

        public String getMatchType() {
            return matchType;
        }

        public void setMatchType(String matchType) {
            this.matchType = matchType;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }

        public String getRegNumber() {
            return regNumber;
        }

        public void setRegNumber(String regNumber) {
            this.regNumber = regNumber;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOrgNumber() {
            return orgNumber;
        }

        public void setOrgNumber(String orgNumber) {
            this.orgNumber = orgNumber;
        }

        public String getBase() {
            return base;
        }

        public void setBase(String base) {
            this.base = base;
        }
    }
}
