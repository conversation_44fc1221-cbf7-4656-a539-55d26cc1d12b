package com.yixun.wid.v2.vo;

import lombok.Data;

import java.util.List;

/**
 * 业务情况统计响应数据
 */
@Data
public class BizInfoStatisticResp {

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份（可选，1-12）
     */
    private Integer month;

    /**
     * 办结数量和平均处理时长统计数据
     * 当只有year参数时，表示按月统计；当同时有year和month参数时，表示按天统计
     */
    private List<CountPoint> doneCountPoints;

    /**
     * 待办业务数据
     */
    private MedicalCasesStatusStatisticResp pendingBizData;
    
    /**
     * 金额统计数据
     */
    private AmountStatisticData amountData;
} 