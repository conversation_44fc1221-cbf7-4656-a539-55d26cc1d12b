package com.yixun.wid.controller.admin;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.aop.RequiredPermission;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.AdminUserInfoIn;
import com.yixun.wid.bean.in.AdministratorGetIn;
import com.yixun.wid.bean.in.AdministratorIn;
import com.yixun.wid.bean.out.AdminUserOut;
import com.yixun.wid.bean.out.AdministratorOut;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Permission;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupAdministrator;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.RolePermissionService;
import com.yixun.wid.service.StaffGroupService;
import com.yixun.wid.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "admin管理员信息")
@RestController
@RequestMapping("/admin/user")
public class AdminInfoController {

    @Resource
    private AdministratorService administratorService;

    @Resource
    private RolePermissionService rolePermissionService;

    @Resource
    private StaffGroupService staffGroupService;

    @GetMapping("/userInfo")
    @ApiOperation(value = "获取登录用户信息")
    public CommonResult<AdminUserOut> userInfo() {

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        AdminUserOut out = new AdminUserOut();
        BeanUtils.copyProperties(administrator, out, true);

        StaffGroup groupNameByUser = staffGroupService.getGroupNameByUser(userId);
        if(groupNameByUser!=null){
            out.setGroupName(groupNameByUser.getGroupName());
            out.setGroupId(groupNameByUser.getId());
        }

        if (!administrator.getRoles().contains("Admin")) {
            List<String> rolesList = JSON.parseArray(administrator.getRoles(), String.class);
            List<Permission> permissionsList = rolePermissionService.getPermissionListByRoleList(rolesList);
            if (permissionsList.isEmpty() || permissionsList.contains(null)) {
                throw new DataErrorException("权限信息错误");
            }
            List<String> permissions = permissionsList.stream().map(Permission::getName).distinct().collect(Collectors.toList());
            out.setPermissions(permissions);
        }

        return CommonResult.successData(out);
    }

    @ApiOperation(value = "修改登录用户信息")
    @PostMapping(value = "/doEdit")
    public CommonResult doEdit(@RequestBody AdminUserInfoIn administratorIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAnyFieldNotNull(administratorIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        BeanUtils.copyProperties(administratorIn, administrator, true);

        administratorService.update(administrator);

        return CommonResult.successResult("修改成功");
    }

    @RequiredPermission("get:getAdministratorList:adminuser")
    @GetMapping("/administratorList")
    @ApiOperation(value = "获取管理员列表")
    public CommonResult<List<AdministratorOut>> getAdministratorList(AdministratorGetIn administratorGetIn, CommonPage page) {

        Page<AdministratorOut> outPageList = administratorService.getAdministratorPageList(administratorGetIn, page);

        return CommonResult.successData(outPageList);
    }

    @RequiredPermission("save:doSave:adminuser")
    @PostMapping("/doSave")
    @ApiOperation(value = "新建管理员")
    public CommonResult<Void> doSave(@RequestBody AdministratorIn administratorIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(administratorIn, Collections.singletonList("government"))) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Long administratorId = SnGeneratorUtil.getId();
        Administrator administrator = new Administrator();
        administrator.setId(administratorId);
        administrator.setCreateDate(new Date());
        administrator.setIsDisable(false);
        administrator.setToken(RandomStringUtil.getByLength(32));
        BeanUtils.copyProperties(administratorIn, administrator);
        administrator.setRoles(JSON.toJSONString(administratorIn.getRoles()));
        administrator.setType(administratorIn.getType().name());

        try {
            administratorService.insert(administrator);
        } catch (Exception e) {
            if (e.getMessage().contains("Duplicate entry")) {
                throw new DataErrorException("用户名或手机号已被使用");
            } else {
                throw new DataErrorException(e.getMessage());
            }
        }

        StaffGroupAdministrator staffGroupAdministrator = new StaffGroupAdministrator();
        staffGroupAdministrator.setStaffGroupId(administratorIn.getStaffGroupId());
        staffGroupAdministrator.setAdministratorId(administratorId);
        staffGroupService.insertStaffGroup(staffGroupAdministrator);

        return CommonResult.successResult("保存成功");
    }

    @RequiredPermission("update:doEdit:adminuser")
    @ApiOperation(value = "修改管理员信息")
    @PostMapping(value = "/doEdit/{administratorId}")
    public CommonResult doEdit(@PathVariable("administratorId") Long administratorId,
                               @RequestBody AdministratorIn administratorIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAnyFieldNotNull(administratorIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Administrator administrator = administratorService.getAdministratorById(administratorId);
        if (administrator == null) {
            throw new DataErrorException("未找到该管理员");
        }
        BeanUtils.copyProperties(administratorIn, administrator, true);
        if (administratorIn.getRoles() != null) {
            administrator.setRoles(JSON.toJSONString(administratorIn.getRoles()));
        }
        if (administratorIn.getType() != null) {
            administrator.setType(administratorIn.getType().name());
        }

        try {
            administratorService.update(administrator);
        } catch (Exception e) {
            if (e.getMessage().contains("Duplicate entry")) {
                throw new DataErrorException("用户名或手机号已被使用");
            } else {
                throw new DataErrorException(e.getMessage());
            }
        }

        if (administratorIn.getStaffGroupId() != null) {
            StaffGroupAdministrator staffGroupAdministrator = staffGroupService.getGroupByAdminId(administratorId);
            if (staffGroupAdministrator != null) {
                staffGroupAdministrator.setStaffGroupId(administratorIn.getStaffGroupId());
                staffGroupService.updateGroupForAdmin(staffGroupAdministrator);
            } else {
                staffGroupAdministrator = new StaffGroupAdministrator();
                staffGroupAdministrator.setStaffGroupId(administratorIn.getStaffGroupId());
                staffGroupAdministrator.setAdministratorId(administratorId);
                staffGroupService.insertStaffGroup(staffGroupAdministrator);
            }
        }

        return CommonResult.successResult("修改成功");
    }

    @RequiredPermission("delete:doDelete:adminuser")
    @ApiOperation(value = "删除管理员信息")
    @PostMapping(value = "/doDelete/{administratorId}")
    public CommonResult doDelete(@PathVariable("administratorId") Long administratorId) {

        Administrator administrator = administratorService.getAdministratorById(administratorId);
        if (administrator == null) {
            throw new DataErrorException("未找到该管理员");
        }

        administratorService.delete(administratorId);

        return CommonResult.successResult("删除成功");
    }

}
