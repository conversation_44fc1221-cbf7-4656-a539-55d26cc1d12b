package com.yixun.wid.controller.admin;

import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.yixun.bean.CommonResult;
import com.yixun.wid.service.AliyunStsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "获取阿里云STS token")
@RestController
public class AliyunSTSController {

    @Resource
    private AliyunStsService aliyunStsService;

    @GetMapping("/admin/get_oss_sts_token")
    @ApiOperation("获取oss_sts_token")
    public CommonResult<AssumeRoleResponse.Credentials> getApiOssStsToken(String userName) {

        AssumeRoleResponse.Credentials credentials = aliyunStsService.getOssStsToken(userName, "admin");

        return CommonResult.successData(credentials);
    }

}
