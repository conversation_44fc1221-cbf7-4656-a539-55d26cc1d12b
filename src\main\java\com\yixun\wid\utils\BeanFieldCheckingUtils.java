package com.yixun.wid.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * bean 所有字段检测  工具
 *
 * <AUTHOR>
 */
public class BeanFieldCheckingUtils {

    /**
     * 检查一个bean除了排除字段以外，是否所有字段都不为空
     *
     * @param bean          需要检查的bean
     * @param excludeFields 需要排除的字段
     * @return 有效 true ，无效false
     */
    public static boolean forAllFieldNotNull(Object bean, List<String> excludeFields) {

        if (check(bean, excludeFields, true)) return false;

        return true;
    }

    /**
     * 检查一个bean是否所有字段都不为空
     *
     * @param bean
     * @return
     */
    public static boolean forAllFieldNotNull(Object bean) {
        return forAllFieldNotNull(bean, new ArrayList<>());
    }

    /**
     * 默认需要排除的字段
     */
    private static List<String> defaultExcludeFields = new ArrayList<>();

    static {
        defaultExcludeFields.add("id");
        defaultExcludeFields.add("isDel");
        defaultExcludeFields.add("createDate");
        defaultExcludeFields.add("modifyDate");
    }

    /**
     * 检查一个bean除了基本字段以外，是否还有其他字段不为空
     *
     * @param bean                 需要检查的bean
     * @param excludeFields        需要排除的字段
     * @param excludeDefaultFields 是否排除 默认的字段  {@link #defaultExcludeFields }
     * @return 有效 true ，无效false
     */
    public static boolean forAnyFieldNotNull(Object bean, List<String> excludeFields, Boolean excludeDefaultFields) {

        if (excludeDefaultFields) {
            excludeFields.addAll(defaultExcludeFields);
        }

        if (check(bean, excludeFields, false)) return true;

        return false;
    }

    /**
     * 检查一个bean除了基本字段以外，是否还有其他字段不为空
     *
     * @param bean
     * @return
     */
    public static boolean forAnyFieldNotNull(Object bean) {
        return forAnyFieldNotNull(bean, new ArrayList<>());
    }

    /**
     * 检查一个bean除了基本字段以外，是否还有其他字段不为空
     *
     * @param bean
     * @param excludeFields
     * @return
     */
    public static boolean forAnyFieldNotNull(Object bean, List<String> excludeFields) {
        return forAnyFieldNotNull(bean, excludeFields, true);
    }

    private static boolean check(Object bean, List<String> excludeFields, boolean isForAll) {
        Class<?> cls = bean.getClass();
        Field[] fields = cls.getDeclaredFields();

        for (Field field : fields) {

            boolean flag = false;
            for (String excludeField : excludeFields) {
                if (field.getName().equals(excludeField)) {
                    flag = true;
                    break;
                }
            }

            if (flag) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object value = field.get(bean);

                if (isForAll) {
                    if (value == null) {
                        return true;
                    }
                } else {
                    if (value != null) {
                        return true;
                    }
                }

            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

}
