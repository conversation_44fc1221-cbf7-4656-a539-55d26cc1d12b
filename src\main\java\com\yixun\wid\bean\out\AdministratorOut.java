package com.yixun.wid.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdministratorOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty(value = "是否禁用")
    private Boolean isDisable;

    @ApiModelProperty(value = "登录名")
    private String username;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "政府机构名称")
    private String government;

    @ApiModelProperty(value = "角色")
    private List<String> roles;

    @ApiModelProperty(value = "是否禁用")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long staffGroupId;

    @ApiModelProperty(value = "是否禁用")
    private String groupName;

    @ApiModelProperty(value = "是否禁用")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date lastLoginTime;

    @ApiModelProperty(value = "是否禁用")
    private String lastLoginIp;

    public void setRoles(String roles) {
        this.roles = JSON.parseArray(roles, String.class);
    }

}