package com.yixun.wid.v2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 办事地点
 */
@Data
public class OfficeLocationVO {

	/**
	 * 统筹地
	 */
	private List areaInfo;

	/**
	 * 受理机构
	 */
	private String bizAgent;

	/**
	 * 办事地点
	 */
	private String officeLocation;

	/**
	 * 窗口详情补充
	 */
	private String windowDetail;

//	/**
//	 * 办理时间 上午
//	 */
//	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_TIME_PATTERN)
//	@DateTimeFormat(pattern = DatePattern.NORM_TIME_PATTERN)
//	private Date bizMorningTime;

	/**
	 * 办理时间 上午开始
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_TIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_TIME_PATTERN)
	private Date bizMorningTimeStart;

	/**
	 * 办理时间 上午结束
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_TIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_TIME_PATTERN)
	private Date bizMorningTimeEnd;

	/**
	 * 办理时间 下午开始
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_TIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_TIME_PATTERN)
	private Date bizAfternoonTimeStart;

	/**
	 * 办理时间 下午结束
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_TIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_TIME_PATTERN)
	private Date bizAfternoonTimeEnd;

	/**
	 * 咨询电话
	 */
	private List<String> hotlines;

	/**
	 * 详细地址
	 */
	private String officeLocationDetail;

	/**
	 * 经度
	 */
	private Double longitude;

	/**
	 * 纬度
	 */
	private Double latitude;

}
