package com.yixun.wid.v2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.MedicalCases;
import com.yixun.wid.v2.entity.MedicalCasesLog;
import com.yixun.wid.v2.service.MedicalCasesLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工伤待遇业务操作日志服务实现类
 */
@Slf4j
@Service
public class MedicalCasesLogServiceImpl implements MedicalCasesLogService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public MedicalCasesLog add(MedicalCasesLog medicalCasesLog) {
        // 验证工伤待遇业务是否存在
        if (medicalCasesLog.getMedicalCasesId() != null) {
            MedicalCases medicalCases = mongoTemplate.findById(medicalCasesLog.getMedicalCasesId(), MedicalCases.class);
            if (medicalCases == null) {
                throw new RuntimeException("关联的工伤待遇业务不存在");
            }
        }
        
        // 生成唯一ID
        medicalCasesLog.setId(SnGeneratorUtil.getId());
        
        // 设置创建时间
        if (medicalCasesLog.getCreateTime() == null) {
            medicalCasesLog.setCreateTime(new Date());
        }
        
        // 保存到数据库
        mongoTemplate.save(medicalCasesLog);
        
        return medicalCasesLog;
    }

    @Override
    public List<MedicalCasesLog> batchAdd(List<MedicalCasesLog> medicalCasesLogs) {
        if (medicalCasesLogs == null || medicalCasesLogs.isEmpty()) {
            throw new RuntimeException("操作日志列表不能为空");
        }
        
        Date now = new Date();
        
        // 为每个日志条目设置ID和创建时间
        for (MedicalCasesLog log : medicalCasesLogs) {
            log.setId(SnGeneratorUtil.getId());
            if (log.getCreateTime() == null) {
                log.setCreateTime(now);
            }
        }
        
        // 批量保存
        mongoTemplate.insertAll(medicalCasesLogs);
        
        return medicalCasesLogs;
    }

    @Override
    public MedicalCasesLog update(MedicalCasesLog medicalCasesLog) {
        // 检查ID是否存在
        if (medicalCasesLog.getId() == null) {
            throw new RuntimeException("操作日志ID不能为空");
        }

        // 查询原有数据
        MedicalCasesLog existingLog = mongoTemplate.findById(medicalCasesLog.getId(), MedicalCasesLog.class);
        if (existingLog == null) {
            throw new RuntimeException("操作日志不存在");
        }

        // 保留创建时间
        medicalCasesLog.setCreateTime(existingLog.getCreateTime());
        
        // 更新到数据库
        mongoTemplate.save(medicalCasesLog);

        return medicalCasesLog;
    }

    @Override
    public Long delete(Long id) {
        if (id == null) {
            throw new RuntimeException("操作日志ID不能为空");
        }
        
        Query query = Query.query(Criteria.where("_id").is(id));
        DeleteResult result = mongoTemplate.remove(query, MedicalCasesLog.class);
        
        return result.getDeletedCount();
    }

    @Override
    public Long batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("操作日志ID列表不能为空");
        }
        
        Query query = Query.query(Criteria.where("_id").in(ids));
        DeleteResult result = mongoTemplate.remove(query, MedicalCasesLog.class);
        
        return result.getDeletedCount();
    }

    @Override
    public MedicalCasesLog getById(Long id) {
        if (id == null) {
            throw new RuntimeException("操作日志ID不能为空");
        }
        
        MedicalCasesLog medicalCasesLog = mongoTemplate.findById(id, MedicalCasesLog.class);
        if (medicalCasesLog == null) {
            throw new RuntimeException("操作日志不存在");
        }
        
        return medicalCasesLog;
    }

    @Override
    public List<MedicalCasesLog> listByMedicalCasesId(Long medicalCasesId, CommonPage commonPage) {
        if (medicalCasesId == null) {
            throw new RuntimeException("工伤待遇业务ID不能为空");
        }
        
        // 验证工伤待遇业务是否存在
        MedicalCases medicalCases = mongoTemplate.findById(medicalCasesId, MedicalCases.class);
        if (medicalCases == null) {
            throw new RuntimeException("工伤待遇业务不存在");
        }
        
        // 构建查询条件
        Query query = Query.query(Criteria.where("medicalCasesId").is(medicalCasesId));
        
        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        
        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, MedicalCasesLog.class, query, commonPage);
        
        // 执行查询
        return mongoTemplate.find(query, MedicalCasesLog.class);
    }

    @Override
    public List<MedicalCasesLog> list(Long medicalCasesId, String status, String userId, CommonPage commonPage) {
        Query query = new Query();
        
        // 添加筛选条件
        if (medicalCasesId != null) {
            query.addCriteria(Criteria.where("medicalCasesId").is(medicalCasesId));
        }
        
        if (StrUtil.isNotBlank(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        
        if (StrUtil.isNotBlank(userId)) {
            query.addCriteria(Criteria.where("userId").is(userId));
        }
        
        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        
        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, MedicalCasesLog.class, query, commonPage);
        
        // 执行查询
        return mongoTemplate.find(query, MedicalCasesLog.class);
    }

    @Override
    public List<MedicalCasesLog> listAllByMedicalCasesId(Long medicalCasesId) {
        if (medicalCasesId == null) {
            throw new RuntimeException("工伤待遇业务ID不能为空");
        }
        
        // 验证工伤待遇业务是否存在
        MedicalCases medicalCases = mongoTemplate.findById(medicalCasesId, MedicalCases.class);
        if (medicalCases == null) {
            throw new RuntimeException("工伤待遇业务不存在");
        }
        
        // 构建查询条件
        Query query = Query.query(Criteria.where("medicalCasesId").is(medicalCasesId));
        
        // 添加排序：按创建时间升序
        query.with(Sort.by(Sort.Direction.ASC, "createTime"));
        
        // 执行查询
        return mongoTemplate.find(query, MedicalCasesLog.class);
    }
} 