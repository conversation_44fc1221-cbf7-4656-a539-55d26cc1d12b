package com.yixun.wid.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yixun.wid.bean.in.StaffGroupSearchIn;
import com.yixun.wid.bean.out.GroupStaffOut;
import com.yixun.wid.mapper.StaffGroupAdministratorMapper;
import com.yixun.wid.mapper.StaffGroupMapper;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupAdministrator;
import com.yixun.wid.service.StaffGroupService;
import com.yixun.wid.utils.TreeListUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class StaffGroupServiceImpl implements StaffGroupService {

    @Resource
    private StaffGroupMapper staffGroupMapper;

    @Resource
    private StaffGroupAdministratorMapper staffGroupAdministratorMapper;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    private String groupRedisKey = "AllGroupList";

    @Override
    public void insert(StaffGroup staffGroup) {
        staffGroupMapper.insert(staffGroup);
        redisTemplate.delete(groupRedisKey);
    }

    @Override
    public void update(StaffGroup staffGroup) {
        staffGroup.setUpdateDate(new Date());
        staffGroupMapper.updateById(staffGroup);
        redisTemplate.delete(groupRedisKey);
    }

    @Override
    public StaffGroup getById(Long staffGroupId) {
        return staffGroupMapper.selectById(staffGroupId);
    }

    @Override
    public StaffGroup getGroupNameByUser(Long administratorId) {

        return staffGroupAdministratorMapper.getGroupNameByUser(administratorId);
    }

    @Override
    public List<StaffGroup> getListByParentId(Long parentId) {
        QueryWrapper<StaffGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.eq("is_del", false);
        queryWrapper.orderByAsc("show_order");

        return staffGroupMapper.selectList(queryWrapper);
    }

    @Override
    public StaffGroupAdministrator getGroupByAdminId(Long administratorId) {
        QueryWrapper<StaffGroupAdministrator> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("administrator_id", administratorId);
        return staffGroupAdministratorMapper.selectOne(queryWrapper);
    }

    @Override
    public void insertStaffGroup(StaffGroupAdministrator staffGroupAdministrator) {
        staffGroupAdministratorMapper.insert(staffGroupAdministrator);
    }

    @Override
    public void updateGroupForAdmin(StaffGroupAdministrator staffGroupAdministrator) {
        QueryWrapper<StaffGroupAdministrator> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("administrator_id", staffGroupAdministrator.getAdministratorId());
        staffGroupAdministratorMapper.update(staffGroupAdministrator, queryWrapper);
    }

    @Override
    public void addStaffGroup(Long staffGroupId, List<Long> administratorIdList) {
        administratorIdList.forEach(administratorId -> {
            StaffGroupAdministrator staffGroupAdministrator = new StaffGroupAdministrator();
            staffGroupAdministrator.setStaffGroupId(staffGroupId);
            staffGroupAdministrator.setAdministratorId(administratorId);
            insertStaffGroup(staffGroupAdministrator);
        });
    }

    @Override
    public void deleteStaffGroup(Long staffGroupId, List<Long> administratorIdList) {
        administratorIdList.forEach(administratorId -> {
            QueryWrapper<StaffGroupAdministrator> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("staff_group_id", staffGroupId);
            queryWrapper.eq("administrator_id", administratorId);
            staffGroupAdministratorMapper.delete(queryWrapper);
        });
    }

    @Override
    public List<StaffGroupAdministrator> getStaffInGroupList(Long staffGroupId) {
        QueryWrapper<StaffGroupAdministrator> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_group_id", staffGroupId);

        return staffGroupAdministratorMapper.selectList(queryWrapper);
    }

    @Override
    public void delete(StaffGroup staffGroup) {
        staffGroup.setIsDel(true);
        update(staffGroup);
    }

    @Override
    public List<GroupStaffOut> getGroupStaffList(Long staffGroupId) {

        return staffGroupAdministratorMapper.getGroupStaffList(staffGroupId);
    }

    @Override
    public List<GroupStaffOut> getAllGroupStaffList() {

        return staffGroupAdministratorMapper.getAllGroupStaffList();
    }

    @Override
    public List<StaffGroup> getAllGroupList() {
        List<StaffGroup> staffGroupList = redisTemplate.opsForList().range(groupRedisKey, 0, -1);
        if (staffGroupList == null || staffGroupList.isEmpty()) {
            QueryWrapper<StaffGroup> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", false);
            queryWrapper.orderByAsc("show_order");
            staffGroupList = staffGroupMapper.selectList(queryWrapper);
            if (!staffGroupList.isEmpty())
                redisTemplate.opsForList().rightPushAll(groupRedisKey, staffGroupList);
        }

        return staffGroupList;
    }

    @Override
    public List<Long> getOwnAndSubGroupList(Long administratorId) {
        List<Long> groupList = new ArrayList<>();

        StaffGroupAdministrator staffGroupAdministrator = getGroupByAdminId(administratorId);
        if (staffGroupAdministrator == null) {
            return groupList;
        }
        groupList.add(staffGroupAdministrator.getStaffGroupId());

        List<StaffGroup> allGroupList = getAllGroupList();
        TreeListUtil.subGroupTreeList(groupList, allGroupList, staffGroupAdministrator.getStaffGroupId());

        return groupList;
    }

    @Override
    public List<StaffGroup> getAllGroupListBySearch(StaffGroupSearchIn staffGroupSearchIn) {
        QueryWrapper<StaffGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", false);
        if (staffGroupSearchIn.getType() != null) {
            queryWrapper.eq("type", staffGroupSearchIn.getType());
        }
        queryWrapper.orderByAsc("show_order");
        List<StaffGroup> staffGroupList = staffGroupMapper.selectList(queryWrapper);

        return staffGroupList;
    }

}
