package com.yixun.wid.v2.utils;

import com.yixun.wid.v2.entity.ClaimsData;
import com.yixun.wid.v2.entity.MedicalCases;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 理算信息工具类
 */
@Component
public class ClaimsDataUtil {

    @Resource
    private ResponsibilityTreeUtil responsibilityTreeUtil;

    /**
     * 为ClaimsData对象的responsibilityItem字段赋值
     * 根据responsibilityTreeCode获取责任树路径，并将路径中的各节点name用/拼接赋值给responsibilityItem
     *
     * @param claimsInformation 理算条信息对象
     */
    public void setResponsibilityItem(MedicalCases.ClaimsInformation claimsInformation) {
        if (claimsInformation == null || claimsInformation.getClaimsData() == null) {
            return;
        }
        
        String code = claimsInformation.getResponsibilityTreeCode();
        if (!StringUtils.hasText(code)) {
            return;
        }
        
        // 获取责任树路径并赋值给responsibilityItem
        String pathName = responsibilityTreeUtil.getPathNameByCode(code);
        if (StringUtils.hasText(pathName)) {
            claimsInformation.getClaimsData().setResponsibilityItem(pathName);
        }
    }
    
    /**
     * 批量设置责任项目名称
     * 
     * @param medicalCases 工伤待遇业务对象
     */
    public void setAllResponsibilityItems(MedicalCases medicalCases) {
        if (medicalCases == null || medicalCases.getClaimsInformations() == null) {
            return;
        }
        
        // 遍历所有理算条信息，设置责任项目名称
        medicalCases.getClaimsInformations().values().forEach(this::setResponsibilityItem);
    }
} 