package com.yixun.wid.v2.task;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yixun.wid.v2.entity.MaterialsListPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class DailyTask {

	@Resource
	private MongoTemplate mongoTemplate;

	@Value("${filePath}")
	private String filePath;

	// 每天凌晨0点执行一次
//	@Scheduled(fixedRate = 10 * 1000)
	@Scheduled(cron = "0 0 0 * * ?")
	public void runAtMidnight() {
		// 你的业务逻辑
		log.info("开始执行导出清单清理任务");

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, -3);
		Date threeDaysAgo = calendar.getTime();

		Query query = new Query();
		query.addCriteria(Criteria.where("createTime").lt(threeDaysAgo));
			// .and("fileExpired").is(false));

		List<MaterialsListPO> oldMaterials = mongoTemplate.find(query, MaterialsListPO.class);
		// process oldMaterials as needed

		if (ObjectUtil.isEmpty(oldMaterials)) {
			log.warn("没有需要清除的文件，跳过任务");
			return;
		}

		List<Long> deletedIds = new ArrayList<>();

		for (MaterialsListPO oldMaterial : oldMaterials) {
			try {
				log.info("清除文件：{}", oldMaterial.getFile());
				String file = oldMaterial.getFile();
				File f = new File(filePath, file);
				boolean exist = FileUtil.exist(f);
				if (exist) {
					FileUtil.del(f);
					deletedIds.add(oldMaterial.getId());
				} else {
					log.info("文件不存在：{}", file);
					deletedIds.add(oldMaterial.getId());
				}
			} catch (Exception e) {
				log.error("文件清除失败：", e);
			}
		}

		if (ObjectUtil.isNotEmpty(deletedIds)) {
			log.warn("删除清单ID列表：{}", deletedIds);
			Query deleteQuery = new Query(Criteria.where("id").in(deletedIds));
			mongoTemplate.remove(deleteQuery, MaterialsListPO.class);
		}

		log.info("结束执行导出清单清理任务");
	}
}
