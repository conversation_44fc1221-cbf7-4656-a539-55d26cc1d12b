package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(value = "批量保存消息输入对象")
@Data
public class SaveBatchMessageIn {

    @ApiModelProperty(value = "所属应用程序", required = true)
    @NotBlank(message = "所属应用程序不能为空")
    private String appId;

    @ApiModelProperty(value = "消息集合", required = true)
    @NotEmpty(message = "消息集合不能为空")
    private List<MessageSaveIn> messages;
}
