package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CorpAgencyOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @ApiModelProperty("代办公司id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long agencyCorpId;

    @ApiModelProperty("代办公司名称")
    private String agencyCorpName;

    @ApiModelProperty("目标公司id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("目标公司名称")
    private String corpName;

    @ApiModelProperty("认证状态")
    private String agencyStatus;

    @ApiModelProperty("失败原因")
    private String reason;

    @ApiModelProperty("营业执照地址")
    private String businessLicense;

    @ApiModelProperty("单位信用代码")
    private String creditCode;

    @ApiModelProperty("公司地址")
    private String businessAddress;

    @ApiModelProperty("单位法人")
    private String legalPerson;

    @ApiModelProperty("注册资本金额")
    private String registeredCapital;

    @ApiModelProperty("注册日期")
    private String registrationDate;

    @ApiModelProperty("有效期")
    private String validPeriod;

    @ApiModelProperty("经营范围")
    private String businessScope;

    @ApiModelProperty("单位地址")
    private String companyAddress;

    @ApiModelProperty("法人身份证正面")
    private String legalPersonIdCardFront;

    @ApiModelProperty("法人身份证反面")
    private String legalPersonIdCardBack;

    @ApiModelProperty("法人电话")
    private String legalPersonPhone;

    @ApiModelProperty("是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty("参保地")
    private String insuranceAddress;

    @ApiModelProperty("是否工会组织")
    private Boolean isLaborUnion;

    @ApiModelProperty("是否需要邮寄材料")
    private Boolean isMailable;

    @ApiModelProperty("是否被托管")
    private Boolean isManaged;
}
