package com.yixun.wid.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "认定申请材料类型")
public class DeclarationFileType {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("盖章/手印（非单位申报）")
    private String sealDetail;

    @ApiModelProperty("盖章/手印（单位申报）")
    private String sealDetailFlag;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否是该项有内容时才展示 1是 0否")
    private Integer showFlag;

    @ApiModelProperty("序号")
    private Integer orderNum;

    @ApiModelProperty("业务字段名称")
    private String columnName;

}