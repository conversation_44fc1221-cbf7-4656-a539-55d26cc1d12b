package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.entity.CorpInsuranceInfo;
import com.yixun.wid.entity.SafetyAccidentFactors;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CorpInvestigateOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("单位名称")
    private String companyName;

    @ApiModelProperty("单位法人")
    private String legalPerson;

    @ApiModelProperty(value = "安全事故调查四不放过")
    private SafetyAccidentFactors safetyAccidentFactors;

    @ApiModelProperty(value = "企业参保情况")
    private CorpInsuranceInfo corpInsuranceInfo;
}
