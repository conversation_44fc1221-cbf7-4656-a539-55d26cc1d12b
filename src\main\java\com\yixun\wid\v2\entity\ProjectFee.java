package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目费用实体
 */
@Data
public class ProjectFee {
    
    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 费用类型
     */
    private String feeType;
    
    /**
     * 机构id，关联HospitalV2的主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long hospitalId;
    
    /**
     * 单价(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer priceInCent;
    
    /**
     * 单价(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal price;
    
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
    
    /**
     * 获取单价(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getPrice() {
        if (priceInCent == null) {
            return null;
        }
        return new BigDecimal(priceInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置单价(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setPrice(BigDecimal price) {
        if (price == null) {
            this.priceInCent = null;
            return;
        }
        this.priceInCent = price.multiply(new BigDecimal(100)).intValue();
    }
} 