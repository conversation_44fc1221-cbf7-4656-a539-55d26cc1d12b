package com.yixun.wid.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.ReceiveFileGetIn;
import com.yixun.wid.bean.in.ReceiveFileIn;
import com.yixun.wid.bean.other.TaskType;
import com.yixun.wid.bean.out.ReceiveFileOut;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.ReceiveFile;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.entity.em.DeclarationSubStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.service.MessageService;
import com.yixun.wid.service.ReceiveFileService;
import com.yixun.wid.utils.AdminUserHelper;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "admin收件列表/补充资料列表")
@RestController
@RequestMapping(value = "/admin/receiveFile")
public class AdminReceiveFileController {

    @Resource
    private DeclarationService declarationService;

    @Resource
    private ReceiveFileService receiveFileService;

    @Resource
    private AdministratorService administratorService;

    @Resource
    private MessageService messageService;

    @GetMapping("/getList")
    @ApiOperation("获取收件列表")
    public CommonResult<List<ReceiveFileOut>> getList(ReceiveFileGetIn receiveFileGetIn, CommonPage commonPage) {

        List<ReceiveFile> receiveFileList = receiveFileService.getList(receiveFileGetIn, commonPage);
        List<ReceiveFileOut> outList = BeanUtils.copyToOutList(receiveFileList, ReceiveFileOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{declarationId}")
    @ApiOperation("按申报id获取资料详情")
    public CommonResult<ReceiveFileOut> getDetail(@PathVariable("declarationId") Long declarationId, String type) {

        ReceiveFile receiveFile = receiveFileService.getByDeclaration(declarationId, type);
        ReceiveFileOut out = new ReceiveFileOut();
        BeanUtils.copyProperties(receiveFile, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/doSave")
    @ApiOperation("保存补充资料")
    public CommonResult<Void> doSave(@RequestBody ReceiveFileIn receiveFileIn) {

        Declaration declaration = declarationService.getById(receiveFileIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        ReceiveFile receiveFile = receiveFileService.getByDeclaration(receiveFileIn.getDeclarationId(), receiveFileIn.getType());
        if (receiveFile == null) {
            Long userId = AdminUserHelper.getCurrentUserId();
            Administrator administrator = administratorService.getAdministratorById(userId);
            receiveFile = new ReceiveFile();
            receiveFile.setId(SnGeneratorUtil.getId());
            receiveFile.setCreateTime(new Date());
            receiveFile.setDeclarationId(receiveFileIn.getDeclarationId());
            receiveFile.setAccidentTime(declaration.getAccidentTime());
            receiveFile.setApplicantName(administrator.getRealName());
            receiveFile.setUserId(declaration.getUserId());
            receiveFile.setName(declaration.getName());
            receiveFile.setStep(receiveFileIn.getStep());
            receiveFile.setType(receiveFileIn.getType());
            receiveFile.setOrganization(declaration.getOrganization());
            receiveFile.setCaseSn(declaration.getCaseSn());
            receiveFile.setWritSn(receiveFileIn.getWritSn());
            receiveFile.setFileList(receiveFileIn.getFileList());

            receiveFileService.save(receiveFile);
        } else {
            BeanUtils.copyProperties(receiveFileIn, receiveFile, true);
            receiveFileService.update(receiveFile);
        }

        if (receiveFileIn.getIsConfirmed() != null && receiveFileIn.getIsConfirmed()) {
            receiveFile.setConfirmedTime(new Date());
            receiveFileService.update(receiveFile);
			if (receiveFileIn.getType().equals("material") &&
				(   // 当状态为Done 子状态为WaitMaterialSubmit ，isConfirmed = true
					ObjectUtil.isNotNull(declaration.getStatus()) && DeclarationStatus.Done.name().equals(declaration.getStatus())
					&& ObjectUtil.isNotNull(declaration.getSubStatus()) && DeclarationSubStatus.WaitMaterialSubmit.name().equals(declaration.getSubStatus())
				)
			) {
				if (
					// 当受理结论为 不予受理
					ObjectUtil.isNotNull(declaration.getAcceptConclusion()) && ObjectUtil.isNotEmpty(declaration.getAcceptConclusion().getConclusion())
						&& "不予受理".equals(declaration.getAcceptConclusion().getConclusion())
				) {
					// 修改子状态为NotAccept
					declaration.setSubStatus(DeclarationSubStatus.NotAccept.name());
					declarationService.update(declaration);
				}

				if (// 当认定结论不为空
					ObjectUtil.isNotNull(declaration.getIdentifyConclusion()) && ObjectUtil.isNotEmpty(declaration.getIdentifyConclusion().getConclusion())) {

					// 当认定结论为 不予认定
					if ("不予认定".equals(declaration.getIdentifyConclusion().getConclusion())) {
						// 修改子状态为NotAccept
						declaration.setSubStatus(DeclarationSubStatus.NotIdentify.name());
						declarationService.update(declaration);
					}
					// 其他结论
					else {
						declaration.setSubStatus(null);
						declarationService.update(declaration);
					}
				}
			}
            if (receiveFileIn.getType().equals("virtual")) {
                declaration.setSubStatus(null);
                declarationService.update(declaration);
            }
        } else {
            if (receiveFileIn.getType().equals("virtual")) {
                declaration.setSubStatus(DeclarationSubStatus.FurtherInfo.name());
                declarationService.update(declaration);
            }
        }
        if (receiveFileIn.getType().equals("virtual")) {
            //添加待办
            String title = declaration.getName() + "的工伤认定申请需补充材料！";
            messageService.sendAppTask(TaskType.FURTHER_INFO, title, declaration);
            //发送订阅消息
            String message = "请根据详情内容及时补充材料，如有新增材料，请一并准备申请材料提交。";
            messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "认定申请", "进入资料补充",
                    message);
        }
        return CommonResult.successResult("保存成功");
    }

}
