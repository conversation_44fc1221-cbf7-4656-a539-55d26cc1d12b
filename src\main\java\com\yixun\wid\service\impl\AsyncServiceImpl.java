package com.yixun.wid.service.impl;

import com.alibaba.fastjson.JSON;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.WidAdminPostLogs;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.AsyncService;
import io.swagger.annotations.ApiOperation;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class AsyncServiceImpl implements AsyncService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AdministratorService administratorService;

    @Override
    @Async
    public void recordLogAdmin(ProceedingJoinPoint invocation, String servletPath, Long userId) {

        Administrator administrator = administratorService.getAdministratorById(userId);

        WidAdminPostLogs postLogs = new WidAdminPostLogs();
        postLogs.setOptTime(new Date());
        postLogs.setOperator(administrator.getUsername());
        postLogs.setOperatorId(userId);
        postLogs.setPath(servletPath);

        MethodSignature method = (MethodSignature) invocation.getSignature();
        ApiOperation apiOperation = AnnotationUtils.findAnnotation(method.getMethod(), ApiOperation.class);
        if (apiOperation != null) {
            String label = apiOperation.value();
            postLogs.setFuncName(label);
        }
        postLogs.setArgs(JSON.toJSONString(invocation.getArgs()));

        mongoTemplate.save(postLogs);
    }

    @Override
    @Async
    public void recordLogLogin(String path, String username, Long userId, String mobileCode, String realIP) {

        WidAdminPostLogs postLogs = new WidAdminPostLogs();
        postLogs.setOptTime(new Date());
        postLogs.setOperator(username);
        postLogs.setOperatorId(userId);
        postLogs.setPath(path);
        postLogs.setFuncName("系统登录");
        Map map = new HashMap();
        map.put("userId", userId);
        map.put("username", username);
        map.put("mobileCode", mobileCode);
        map.put("ip", realIP);
        postLogs.setArgs(JSON.toJSONString(Collections.singletonList(map)));

        mongoTemplate.save(postLogs);
    }

	@Override
	public void recordLogLogin(String path, String username, Long userId, String mobileCode, String realIP, String funcName) {
		WidAdminPostLogs postLogs = new WidAdminPostLogs();
		postLogs.setOptTime(new Date());
		postLogs.setOperator(username);
		postLogs.setOperatorId(userId);
		postLogs.setPath(path);
		postLogs.setFuncName(funcName);
		Map map = new HashMap();
		map.put("userId", userId);
		map.put("username", username);
		map.put("mobileCode", mobileCode);
		map.put("ip", realIP);
		postLogs.setArgs(JSON.toJSONString(Collections.singletonList(map)));

		mongoTemplate.save(postLogs);
	}
}
