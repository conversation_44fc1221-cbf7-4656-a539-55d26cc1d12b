package com.yixun.wid.entity;

import lombok.Data;

import java.util.List;

@Data
public class CorpInfoDetail {

    /**
     * historyNames : 贵州力源液压股份有限公司;
     * regStatus : 存续
     * regCapital : 147204.909万人民币
     * city : 贵阳市
     * staffNumRange : 5000-9999人
     * bondNum : 600765
     * historyNameList : ["贵州力源液压股份有限公司"]
     * industry : 汽车制造业
     * bondName : 中航重机
     * type : 1
     * updateTimes : 1708968394000
     * legalPersonName : 冉兴
     * regNumber : 520000000005018
     * creditCode : 91520000214434146R
     * property3 : AVIC Heavy Machinery Co.,Ltd.
     * usedBondName : 力源液压->G力源->力源液压
     * approvedTime : 1687622400000
     * fromTime : 847900800000
     * socialStaffNum : 6652
     * actualCapitalCurrency : 人民币
     * alias : 中航重机
     * companyOrgType : 其他股份有限公司(上市)
     * id : 11684584
     * orgNumber : 21443414-6
     * actualCapital : 147204.909万人民币
     * estiblishTime : 847900800000
     * regInstitute : 贵阳市市场监督管理局双龙分局
     * businessScope : 法律、法规、国务院决定规定禁止的不得经营；法律、法规、国务院决定规定应当许可（审批）的，经审批机关批准后凭许可（审批）文件经营;法律、法规、国务院决定规定无需许可（审批）的，市场主体自主选择经营。（股权投资及经营管理；军民共用液压件、液压系统、锻件、铸件、换热器、飞机及航空发动机附件，汽车零备件的研制、开发、制造、修理及销售；经营本企业自产机电产品、成套设备及相关技术的出口业务；经营本企业生产、科研所需的原辅材料、机械设备、仪器仪表、备品备件、零配件及技术的进口业务；开展本企业进料加工和“三来一补”业务。液压、锻件、铸件、换热器技术开发、转让和咨询服务；物流；机械冷热加工、修理修配服务。）
     * taxNumber : 91520000214434146R
     * regLocation : 贵州省贵阳市贵州双龙航空港经济区航空总部基地1号楼5层
     * regCapitalCurrency : 人民币
     * tags : 存续;曾用名;定向增发;A股 | 中航重机 600765 | 正常上市;项目品牌:中航重机;投资机构:中航重机;企业集团
     * district : 南明区
     * bondType : A股
     * name : 中航重机股份有限公司
     * percentileScore : 9827
     * industryAll : {"categoryMiddle":"改装汽车制造","categoryBig":"汽车制造业","category":"制造业","categorySmall":""}
     * isMicroEnt : 0
     * base : gz
     */

    private String historyNames;
    private String regStatus;
    private String regCapital;
    private String city;
    private String staffNumRange;
    private String bondNum;
    private String industry;
    private String bondName;
    private String type;
    private String updateTimes;
    private String legalPersonName;
    private String regNumber;
    private String creditCode;
    private String property3;
    private String usedBondName;
    private String approvedTime;
    private String fromTime;
    private String socialStaffNum;
    private String actualCapitalCurrency;
    private String alias;
    private String companyOrgType;
    private String id;
    private String orgNumber;
    private String actualCapital;
    private String estiblishTime;
    private String regInstitute;
    private String businessScope;
    private String taxNumber;
    private String regLocation;
    private String regCapitalCurrency;
    private String tags;
    private String district;
    private String bondType;
    private String name;
    private String percentileScore;
    private IndustryAllBean industryAll;
    private String isMicroEnt;
    private String base;
    private List<String> historyNameList;
    // 邮箱
    private String email;
    // 网站
    private String websiteList;
    // 电话
    private String phoneNumber;

    public String getHistoryNames() {
        return historyNames;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    public String getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public String getRegCapital() {
        return regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStaffNumRange() {
        return staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public String getBondNum() {
        return bondNum;
    }

    public void setBondNum(String bondNum) {
        this.bondNum = bondNum;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getBondName() {
        return bondName;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUpdateTimes() {
        return updateTimes;
    }

    public void setUpdateTimes(String updateTimes) {
        this.updateTimes = updateTimes;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getRegNumber() {
        return regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getUsedBondName() {
        return usedBondName;
    }

    public void setUsedBondName(String usedBondName) {
        this.usedBondName = usedBondName;
    }

    public String getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(String approvedTime) {
        this.approvedTime = approvedTime;
    }

    public String getFromTime() {
        return fromTime;
    }

    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    public String getSocialStaffNum() {
        return socialStaffNum;
    }

    public void setSocialStaffNum(String socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public String getActualCapitalCurrency() {
        return actualCapitalCurrency;
    }

    public void setActualCapitalCurrency(String actualCapitalCurrency) {
        this.actualCapitalCurrency = actualCapitalCurrency;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getCompanyOrgType() {
        return companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgNumber() {
        return orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public String getActualCapital() {
        return actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public String getEstiblishTime() {
        return estiblishTime;
    }

    public void setEstiblishTime(String estiblishTime) {
        this.estiblishTime = estiblishTime;
    }

    public String getRegInstitute() {
        return regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getRegCapitalCurrency() {
        return regCapitalCurrency;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getBondType() {
        return bondType;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPercentileScore() {
        return percentileScore;
    }

    public void setPercentileScore(String percentileScore) {
        this.percentileScore = percentileScore;
    }

    public IndustryAllBean getIndustryAll() {
        return industryAll;
    }

    public void setIndustryAll(IndustryAllBean industryAll) {
        this.industryAll = industryAll;
    }

    public String getIsMicroEnt() {
        return isMicroEnt;
    }

    public void setIsMicroEnt(String isMicroEnt) {
        this.isMicroEnt = isMicroEnt;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public List<String> getHistoryNameList() {
        return historyNameList;
    }

    public void setHistoryNameList(List<String> historyNameList) {
        this.historyNameList = historyNameList;
    }

    public static class IndustryAllBean {
        /**
         * categoryMiddle : 改装汽车制造
         * categoryBig : 汽车制造业
         * category : 制造业
         * categorySmall :
         */

        private String categoryMiddle;
        private String categoryBig;
        private String category;
        private String categorySmall;

        public String getCategoryMiddle() {
            return categoryMiddle;
        }

        public void setCategoryMiddle(String categoryMiddle) {
            this.categoryMiddle = categoryMiddle;
        }

        public String getCategoryBig() {
            return categoryBig;
        }

        public void setCategoryBig(String categoryBig) {
            this.categoryBig = categoryBig;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getCategorySmall() {
            return categorySmall;
        }

        public void setCategorySmall(String categorySmall) {
            this.categorySmall = categorySmall;
        }
    }
}
