package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.DeclarationFileList;
import com.yixun.wid.service.DeclarationFileListService;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "admin认定申请材料清单类型")
@RestController
@RequestMapping(value = "/admin/declarationFileList")
public class AdminDeclarationFileListController {

    @Resource
    private DeclarationFileListService declarationFileListService;

    @PostMapping("/save")
    @ApiOperation("保存认定申请材料清单类型")
    public CommonResult<Long> save(@RequestBody DeclarationFileList declarationFileList) {

        declarationFileList.setId(SnGeneratorUtil.getId());
        declarationFileListService.save(declarationFileList);

        return CommonResult.successResult("操作成功");
    }

}
