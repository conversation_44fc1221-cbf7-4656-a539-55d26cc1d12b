package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.entity.MedicalCasesLog;
import com.yixun.wid.v2.service.MedicalCasesLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 待遇 工伤待遇业务操作日志接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/medical/cases/log")
public class MedicalCasesLogController {

    @Resource
    private MedicalCasesLogService medicalCasesLogService;

    /**
     * 新增操作日志
     *
     * @param medicalCasesLog 操作日志信息
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<MedicalCasesLog> add(@RequestBody MedicalCasesLog medicalCasesLog) {
        MedicalCasesLog result = medicalCasesLogService.add(medicalCasesLog);
        return CommonResult.successData(result);
    }

    /**
     * 批量新增操作日志
     *
     * @param medicalCasesLogs 操作日志列表
     * @return 新增结果
     */
    @PostMapping("/batchAdd")
    public CommonResult<List<MedicalCasesLog>> batchAdd(@RequestBody List<MedicalCasesLog> medicalCasesLogs) {
        List<MedicalCasesLog> result = medicalCasesLogService.batchAdd(medicalCasesLogs);
        return CommonResult.successData(result);
    }

    /**
     * 更新操作日志
     *
     * @param medicalCasesLog 操作日志信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<MedicalCasesLog> update(@RequestBody MedicalCasesLog medicalCasesLog) {
        MedicalCasesLog result = medicalCasesLogService.update(medicalCasesLog);
        return CommonResult.successData(result);
    }

    /**
     * 根据ID删除操作日志
     *
     * @param id 操作日志ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    public CommonResult<Long> delete(@RequestParam Long id) {
        Long result = medicalCasesLogService.delete(id);
        return CommonResult.successData(result);
    }

    /**
     * 批量删除操作日志
     *
     * @param ids 操作日志ID列表
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody List<Long> ids) {
        Long result = medicalCasesLogService.batchDelete(ids);
        return CommonResult.successData(result);
    }

    /**
     * 根据ID获取操作日志
     *
     * @param id 操作日志ID
     * @return 操作日志详情
     */
    @GetMapping("/detail")
    public CommonResult<MedicalCasesLog> getById(@RequestParam Long id) {
        MedicalCasesLog result = medicalCasesLogService.getById(id);
        return CommonResult.successData(result);
    }

    /**
     * 根据业务ID查询操作日志列表
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @param commonPage 分页参数
     * @return 操作日志列表（分页）
     */
    @GetMapping("/listByMedicalCasesId")
    public CommonResult<List<MedicalCasesLog>> listByMedicalCasesId(
            @RequestParam Long medicalCasesId,
            CommonPage commonPage) {
        List<MedicalCasesLog> result = medicalCasesLogService.listByMedicalCasesId(medicalCasesId, commonPage);
        return CommonResult.successPageData(result, commonPage);
    }

    /**
     * 根据条件查询操作日志列表
     *
     * @param medicalCasesId 工伤待遇业务ID（可选）
     * @param status 状态（可选）
     * @param userId 录入用户ID（可选）
     * @param commonPage 分页参数
     * @return 操作日志列表（分页）
     */
    @GetMapping("/list")
    public CommonResult<List<MedicalCasesLog>> list(
            @RequestParam(required = false) Long medicalCasesId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String userId,
            CommonPage commonPage) {
        List<MedicalCasesLog> result = medicalCasesLogService.list(medicalCasesId, status, userId, commonPage);
        return CommonResult.successPageData(result, commonPage);
    }

    /**
     * 根据业务ID查询操作日志列表（不分页）
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @return 操作日志列表（全部，不分页）
     */
    @GetMapping("/listAllByMedicalCasesId")
    public CommonResult<List<MedicalCasesLog>> listAllByMedicalCasesId(@RequestParam Long medicalCasesId) {
        List<MedicalCasesLog> result = medicalCasesLogService.listAllByMedicalCasesId(medicalCasesId);
        return CommonResult.successData(result);
    }
}
