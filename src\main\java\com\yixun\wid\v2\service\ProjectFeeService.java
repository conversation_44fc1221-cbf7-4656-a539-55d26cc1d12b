package com.yixun.wid.v2.service;

import com.yixun.wid.v2.bean.in.ProjectFeeSimilarSearchIn;
import com.yixun.wid.v2.entity.ProjectFee;

/**
 * 项目费用服务接口
 */
public interface ProjectFeeService {
    
    /**
     * 项目费用相似搜索
     * 基于AiUtils中的相似查找功能，实现项目费用的智能匹配
     *
     * @param searchIn 相似搜索参数
     * @return 相似度最高的一条项目费用数据的完整详情，未找到返回null
     */
    ProjectFee similarSearch(ProjectFeeSimilarSearchIn searchIn);
}
