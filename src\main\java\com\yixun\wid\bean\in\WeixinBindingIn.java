package com.yixun.wid.bean.in;

import com.yixun.wid.entity.User;
import com.yixun.wid.entity.em.LoginType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WeixinBindingIn {

    @ApiModelProperty(value="昵称", required = true)
    private String nickName;
    @ApiModelProperty(value="性别", required = true)
    private String gender;
    @ApiModelProperty(value="头像", required = true)
    private String avatarUrl;
    @ApiModelProperty(value="微信openId", required = true)
    private String openId;
    @ApiModelProperty(value="微信iv", required = true)
    private String iv;
    @ApiModelProperty(value="微信encryptedData", required = true)
    private String encryptedData;
    @ApiModelProperty(value="验证码", required = true)
    private String verifyCode;
    @ApiModelProperty(value="机器码", required = true)
    private String clientId;
    @ApiModelProperty(value="登录类型", required = true)
    private LoginType loginType;

    public static User getInstance(WeixinBindingIn bindingIn, long id, String phoneNumber) {
        User hdmUser = new User();
        hdmUser.setId(id);
        hdmUser.setCreateTime(new Date());
        hdmUser.setIsDisable(false);

        hdmUser.setPhone(phoneNumber);
        hdmUser.setAvatar(bindingIn.getAvatarUrl());
        hdmUser.setWeixinMiniOpenId(bindingIn.getOpenId());

        return hdmUser;
    }
}
