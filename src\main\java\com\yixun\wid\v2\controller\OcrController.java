package com.yixun.wid.v2.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.v2.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * ocr相关接口
 */
@Slf4j
@RequestMapping("/v2/ocr")
@RestController
public class OcrController {

	private static final ObjectMapper objectMapper = new ObjectMapper();

	@Value("${api.ocrApi}")
	private String ocrApi;

	@Value("${api.generalOcrApi}")
	private String generalOcrApi;

	/**
	 * 事故信息/就诊信息ocr识别
	 *
	 * @return 事故信息/就诊信息
	 */
	@PostMapping
	public CommonResult<List<DiagnosticInfoV2>> ocr(@Validated @RequestBody OcrReq ocrReq) {
		try {
			String body = objectMapper.writeValueAsString(ocrReq);
			String result = HttpUtil.post(ocrApi, body);
			OcrResp ocrResp = objectMapper.readValue(result, OcrResp.class);
			log.info("ocrResp: {}", ocrResp);
			String successStatus = ocrResp.getSuccessStatus();
			if ("1".equals(successStatus)) {
				List<OcrResp.Result> result1 = ocrResp.getResult();
				List<DiagnosticInfoV2> diagnosticInfoV2List = new ArrayList<>();
				for (OcrResp.Result result2 : result1) {
					DiagnosticInfoV2 diagnosticInfoV2 = new DiagnosticInfoV2();
					diagnosticInfoV2.setHospital(result2.getHospital());
					diagnosticInfoV2.setFirstClinicDate(result2.getConsultationTime());
					diagnosticInfoV2.setDiagnoses(result2.getDiagnosticConclusion());
					diagnosticInfoV2List.add(diagnosticInfoV2);
				}
				return CommonResult.successData(diagnosticInfoV2List);
			}
		} catch (Exception e) {
			log.error("OCR识别失败", e);
		}
		return CommonResult.failResult(CommonErrorInfo.code_9001, "识别失败");
	}

	/**
	 * ocr文字识别
	 *
	 * @return 识别结果
	 */
	@PostMapping("/general")
	public CommonResult<GeneralInfo> generalOcr(@RequestParam("file") MultipartFile file) {
		File tempFile = null;
		try {
			String extension = FilenameUtils.getExtension(file.getOriginalFilename());
			tempFile = FileUtil.createTempFile("."+ extension, true);
			file.transferTo(tempFile);
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("image", tempFile);
			String result = HttpUtil.post(generalOcrApi, paramMap);
			GeneralOcrResp generalOcrResp = objectMapper.readValue(result, GeneralOcrResp.class);
			log.info("generalOcrResp: {}", generalOcrResp);
			String status = generalOcrResp.getStatus();
			if ("success".equals(status)) {
				List<String> texts = generalOcrResp.getData().getTexts();
				GeneralInfo generalInfo = new GeneralInfo();
				generalInfo.setResults(texts);
				return CommonResult.successData(generalInfo);
			}
		} catch (Exception e) {
			log.error("OCR识别失败", e);
		} finally {
			if (ObjectUtil.isNotNull(tempFile)) {
				tempFile.delete();
			}
		}
		return CommonResult.failResult(CommonErrorInfo.code_9001, "识别失败");
	}

}
