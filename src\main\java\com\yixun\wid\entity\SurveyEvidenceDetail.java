package com.yixun.wid.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "SurveyEvidenceDetail对象", description = "调查任务证据详细表")
public class SurveyEvidenceDetail {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("证据id")
    private Long evidenceId;

    @ApiModelProperty("证据类型")
    private String type;

    @ApiModelProperty("证据内容")
    private String content;

    @ApiModelProperty("证据内容说明（选其他时有值）")
    private String contentDesc;

    @ApiModelProperty("证据要素说明")
    private String description;

    @ApiModelProperty("证据保存形式")
    private String saveType;

    @ApiModelProperty("是否异常")
    private Boolean isUnusual;

    @ApiModelProperty("异常说明")
    private String unusualDesc;

    @ApiModelProperty("证据链接")
    private String urls;
}
