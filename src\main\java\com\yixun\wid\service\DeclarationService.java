package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.DeclarationGetIn;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.DeclarationLog;

import java.util.Date;
import java.util.List;

public interface DeclarationService{

    List<Declaration> getDeclarationList(DeclarationGetIn declarationGetIn, CommonPage commonPage);

    void save(Declaration declaration);

    Declaration getById(Long declarationId);

    void update(Declaration declaration);

    List<Declaration> getDeclarationStatistic(Date startTime);

    void setDeclarationLog(Long declarationId, String type);

    List<DeclarationLog> getDeclarationLogList(Long declarationId, CommonPage commonPage);
}
