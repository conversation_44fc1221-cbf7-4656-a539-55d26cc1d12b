package com.yixun.wid.v2.vo;

import cn.idev.excel.annotation.write.style.ContentFontStyle;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MaterialsVO {

	/**
	 * 申请人
	 */
	@ContentFontStyle(fontHeightInPoints = 9)
	private String apply;

	/**
	 * 受伤职工姓名
	 */
	@ContentFontStyle(fontHeightInPoints = 9)
	private String name;

	/**
	 * 流水号
	 */
	@NotNull(message = "流水号不能为空")
	private String serialNum;

	/**
	 * 材料
	 */
	@NotNull(message = "材料不能为空")
	private List<MaterialsFileVO> materialsFileList;

}
