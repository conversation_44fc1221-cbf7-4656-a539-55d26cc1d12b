package com.yixun.wid;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@ServletComponentScan
@MapperScan({"com.yixun.wid.mapper", "com.yixun.wid.v2.dao"})
@ComponentScan(basePackages = {"com.yixun"})
@EnableFeignClients(basePackages = {"com.yixun"})
public class WidServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(WidServiceApplication.class, args);
	}

}
