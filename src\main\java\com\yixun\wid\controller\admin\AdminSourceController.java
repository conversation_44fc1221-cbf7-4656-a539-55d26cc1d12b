package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.out.SourceOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "admin来源机构接口")
@RestController
@RequestMapping(value = "/admin/source")
public class AdminSourceController {

    @Value("${source.orgId}")
    private String sourceOrgId;

    @Value("${source.orgName}")
    private String sourceOrgName;

    @GetMapping("/getSource")
    @ApiOperation("获取来源机构信息")
    public CommonResult<SourceOut> getSource() {
        SourceOut sourceOut = new SourceOut();
        sourceOut.setSourceOrgId(sourceOrgId);
        sourceOut.setSourceOrgName(sourceOrgName);
        return CommonResult.successData(sourceOut);
    }

}
