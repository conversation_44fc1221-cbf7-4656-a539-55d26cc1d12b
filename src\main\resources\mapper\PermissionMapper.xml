<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.wid.mapper.PermissionMapper" >
    <insert id="initPermission" parameterType="java.util.List" >
        insert into permission (name, label, type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.name}, #{item.label}, #{item.type})
        </foreach>
    </insert>

    <select id="getPermissionListByRole" resultType="com.yixun.wid.entity.Permission">
        SELECT p.name, p.label, p.type
            FROM role_permission as rp left join role as r on r.name=rp.role
                left join permission as p on rp.permission=p.name
        WHERE r.name = #{role}
    </select>

    <select id="getPermissionListByRoleList" resultType="com.yixun.wid.entity.Permission">
        SELECT p.name, p.label, p.type
            FROM role_permission as rp left join role as r on r.name=rp.role
                left join permission as p on rp.permission=p.name
        WHERE r.name in
            <foreach  item="item" index="index" collection="roleList" open="(" separator="," close=" )">
                #{item}
            </foreach>
    </select>
</mapper>