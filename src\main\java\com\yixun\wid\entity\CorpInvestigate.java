package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CorpInvestigate {

    private Long id;
    private Date createTime;
    private Date updateTime;
    private Long userId;
    private Long corporationId;
    private String companyName;
    private String legalPerson;

    @ApiModelProperty(value = "安全事故调查四不放过")
    private SafetyAccidentFactors safetyAccidentFactors;

    @ApiModelProperty(value = "企业参保情况")
    private CorpInsuranceInfo corpInsuranceInfo;

}
