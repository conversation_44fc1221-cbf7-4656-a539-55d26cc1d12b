package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.bean.in.BillingInfoBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.entity.BillingInfo;
import com.yixun.wid.v2.service.BillingInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 待遇 账单信息相关接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/billingInfo")
public class BillingInfoController {

    @Resource
    private BillingInfoService billingInfoService;

    /**
     * 分页查询账单信息
     *
     * @param medicalCasesId 关联的工伤待遇业务ID
     * @param hospital 医院名称（模糊查询）
     * @param treatmentType 治疗类型
     * @param commonPage 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<BillingInfo>> list(
            @RequestParam(required = false) Long medicalCasesId,
            @RequestParam(required = false) String hospital,
            @RequestParam(required = false) String treatmentType,
            CommonPage commonPage) {
        
        List<BillingInfo> billingInfos = billingInfoService.list(medicalCasesId, hospital, treatmentType, commonPage);
        
        return CommonResult.successPageData(billingInfos, commonPage);
    }
    
    /**
     * 根据ID获取账单信息详情
     *
     * @param id 账单信息ID
     * @return 账单信息详情
     */
    @GetMapping("/detail")
    public CommonResult<BillingInfo> getDetail(@RequestParam Long id) {
        BillingInfo billingInfo = billingInfoService.getDetail(id);
        return CommonResult.successData(billingInfo);
    }

    /**
     * 新增账单信息
     *
     * @param billingInfoIn 账单信息输入参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<BillingInfo> add(@RequestBody BillingInfoIn billingInfoIn) {
        BillingInfo billingInfo = billingInfoService.add(billingInfoIn);
        return CommonResult.successData(billingInfo);
    }

    /**
     * 更新账单信息
     *
     * @param billingInfo 账单信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<BillingInfo> update(@RequestBody BillingInfo billingInfo) {
        BillingInfo updatedBillingInfo = billingInfoService.update(billingInfo);
        return CommonResult.successData(updatedBillingInfo);
    }

    /**
     * 批量删除账单信息
     *
     * @param batchDeleteIn 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody BillingInfoBatchDeleteIn batchDeleteIn) {
        Long deletedCount = billingInfoService.batchDelete(batchDeleteIn);
        return CommonResult.successData(deletedCount);
    }

    /**
     * 根据工伤待遇业务ID查询关联的账单信息列表
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @return 账单信息列表
     */
    @GetMapping("/listByMedicalCases")
    public CommonResult<List<BillingInfo>> listByMedicalCases(@RequestParam Long medicalCasesId) {
        List<BillingInfo> billingInfos = billingInfoService.listByMedicalCases(medicalCasesId);
        return CommonResult.successData(billingInfos);
    }
}