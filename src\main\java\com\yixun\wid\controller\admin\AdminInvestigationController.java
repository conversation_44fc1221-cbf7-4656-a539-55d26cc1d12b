package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.InvestigateIn;
import com.yixun.wid.bean.in.InvestigationGetIn;
import com.yixun.wid.bean.out.InvestigationOut;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.Investigation;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.entity.em.InvestigationStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.service.InvestigationService;
import com.yixun.wid.utils.AdminUserHelper;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "admin第三方辅助调查")
@RestController
@RequestMapping(value = "/admin/investigation")
public class AdminInvestigationController {

    @Resource
    private DeclarationService declarationService;

    @Resource
    private InvestigationService investigationService;

    @Resource
    private AdministratorService administratorService;

    @GetMapping("/getList")
    @ApiOperation("获取辅助调查列表")
    public CommonResult<List<InvestigationOut>> getList(InvestigationGetIn investigationGetIn, CommonPage commonPage) {

        List<Investigation> investigationList = investigationService.getList(investigationGetIn, commonPage);
        List<InvestigationOut> outList = BeanUtils.copyToOutList(investigationList, InvestigationOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{investigationId}")
    @ApiOperation("获取辅助调查详情")
    public CommonResult<InvestigationOut> getDetail(@PathVariable("investigationId") Long investigationId) {

        Investigation investigation = investigationService.getById(investigationId);
        if (investigation==null){
            throw new DataErrorException("该辅助调查不存在");
        }
        InvestigationOut out = new InvestigationOut();
        BeanUtils.copyProperties(investigation, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/issueInvestigate")
    @ApiOperation("发起辅助调查(派单)")
    public CommonResult<Void> setAccept(@RequestBody InvestigateIn acceptIn) {

        Declaration declaration = declarationService.getById(acceptIn.getDeclarationId());
        if (declaration==null){
            throw new DataErrorException("申报信息不存在");
        }

        if ( !declaration.getStatus().equals(DeclarationStatus.Identifying.name())
                &&  !declaration.getStatus().equals(DeclarationStatus.Classifying.name()) ){
            throw new DataErrorException("申报状态错误");
        }

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);

        Investigation investigation = new Investigation();
        investigation.setId(SnGeneratorUtil.getId());
        investigation.setCreateTime(new Date());
        investigation.setDeclarationId(acceptIn.getDeclarationId());
        investigation.setIssueOfficer(administrator.getRealName());
        investigation.setGovernment(administrator.getGovernment());
        investigation.setStatus(InvestigationStatus.Start.name());
        investigation.setName(declaration.getName());
        investigation.setIdCard(declaration.getIdCard());
        investigation.setOrganizationId(declaration.getOrganizationId());
        investigation.setOrganization(declaration.getOrganization());
        investigation.setInjuredPart(declaration.getInjuredPart());
        investigation.setAccidentTime(declaration.getAccidentTime());

        investigationService.save(investigation);

        declaration.setHasIssuedInvestigate(true);
        declarationService.update(declaration);

        return CommonResult.successResult("派单成功");
    }

}
