package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeliveryFileOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @ApiModelProperty(value = "申报id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long declarationId;

    @ApiModelProperty(value = "案件号")
    private String caseSn;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "环节 受理，认定")
    private String step;

    @ApiModelProperty(value = "送达类别 邮寄，自领")
    private String type;

    @ApiModelProperty(value = "事故时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date accidentTime;

    @ApiModelProperty(value = "上报人的用户id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "是否已送达")
    private Boolean isConfirmed;

    @ApiModelProperty(value = "送达时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date confirmedTime;

    @ApiModelProperty(value = "送达材料")
    private List fileList;

    @ApiModelProperty(value = "邮寄送达")
    private List mailList;

    @ApiModelProperty(value = "材料自领")
    private List selfList;
}
