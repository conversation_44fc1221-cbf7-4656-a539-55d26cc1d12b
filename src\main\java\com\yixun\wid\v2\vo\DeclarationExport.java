package com.yixun.wid.v2.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.bean.out.UserAddressOut;
import com.yixun.wid.entity.*;
import com.yixun.wid.v2.utils.BooleanStringConverter;
import com.yixun.wid.v2.utils.DictListStringConverter;
import com.yixun.wid.v2.utils.ListStringConverter;
import com.yixun.wid.v2.utils.ObjStringConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeclarationExport {

	private Long id;

	private Date createTime;

	private Date updateTime;

	@ApiModelProperty("是否锁定")
	private Boolean isLocked;

	@ApiModelProperty("是否admin参与了")
	private Boolean isAdminInvolved;

	@ApiModelProperty("撤销前状态")
	private String statusBeforeCancel;

	@ApiModelProperty("撤销前子状态")
	private String subStatusBeforeCancel;

	@ApiModelProperty("案件号")
	private String caseSn;

	@ApiModelProperty("上报方式")
	private String submitWay;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("原因")
	private String reason;

	//1、职工信息
	//报案人信息
	@ApiModelProperty("上报人的用户id")
	private Long userId;

	@ApiModelProperty("与当事人关系")
	private String relationship;

	@ApiModelProperty("与当事人关系详情")
	private String relationshipDetail;

	@ApiModelProperty("提交人的用户id")
	private Long submitUserId;

	@ApiModelProperty("申报人/经办人姓名")
	private String applicantName;

	@ApiModelProperty("申报人/经办人手机")
	private String applicantPhone;

	@ApiModelProperty("报案状态")
	private String status;

	@ApiModelProperty("申报子状态")
	private String subStatus;

	@ApiModelProperty("来源案件id")
//	@ExcelProperty(converter = ObjStringConverter.class)
	private Long casesId;

	//工伤当事人参保信息
	@ApiModelProperty("是否参保")
	private Boolean hasInsurance;

	@ApiModelProperty("参保地")
	private String insuranceAddress;

	//工伤当事人信息
	@ApiModelProperty("职工姓名")
	private String name;

	@ApiModelProperty("职工性别")
	private String gender;

	@ApiModelProperty("出生年月日")
	private Date birthdayDate;

	@ApiModelProperty("身份证")
	private String idCard;

	@ApiModelProperty("身份证地址")
	private String idCardAddr;

	@ApiModelProperty("手机号码")
	private String phone;

	@ApiModelProperty("是否退休")
	private Boolean isRetired;

	@ApiModelProperty("家庭区域")
	private List homeRegion;

	@ApiModelProperty("家庭详细地址")
	private String homeAddr;

	@ApiModelProperty("工作岗位")
	@ExcelProperty(converter = DictListStringConverter.class)
	private List position;

	@ApiModelProperty("参加工作时间")
	private Date firstWorkDate;

	//2、单位信息
	@ApiModelProperty("单位id")
	private Long organizationId;

	@ApiModelProperty("单位名称")
	private String organization;

	@ApiModelProperty("单位区域")
	private List organizationRegion;

	@ApiModelProperty("单位地址")
	private String organizationAddr;

	@ApiModelProperty("单位地址经度")
	private Double organizationLongitude;

	@ApiModelProperty("单位地址纬度")
	private Double organizationLatitude;

	@ApiModelProperty("单位邮编")
	private String zipCode;

	@ApiModelProperty("单位法人")
	private String legalPerson;

	@ApiModelProperty("法人电话")
	private String legalPersonPhone;

	//3.事故信息
	//是否职业病相关
	@ApiModelProperty("是否职业病相关")
	@ExcelProperty(converter = BooleanStringConverter.class)
	private Boolean isOccupDiseaseRelated;

	@ApiModelProperty("职业病名称")
	private String occupDiseaseName;

	@ApiModelProperty("接触职业病危害岗位")
	private String occupDiseasePosition;

	@ApiModelProperty("接触职业病危害时间")
	private String occupDiseaseDate;

	//事故信息
	@ApiModelProperty("伤害部位")
	@ExcelProperty(converter = ListStringConverter.class)
	private List injuredPart;

	@ApiModelProperty("事故时间")
	private Date accidentTime;

	@ApiModelProperty("事故区域")
	private List accidentRegion;

	@ApiModelProperty("事故地点")
	private String accidentAddress;

	@ApiModelProperty("事故地点经度")
	private Double longitude;

	@ApiModelProperty("事故地点纬度")
	private Double latitude;

	@ApiModelProperty("事故经过")
	private String accidentDetail;

	@ApiModelProperty("事故原因")
	private String accidentCause;

	@ApiModelProperty("事发时从事的工作")
	private String workingOfAccident;

	@ApiModelProperty("补充说明")
	private String supplementary;

	//事故相关材料
	@ApiModelProperty("现场照片")
	private List pictures;

	@ApiModelProperty("现场视频")
	private List videos;

	@ApiModelProperty("受伤害部位图片")
	private List injuredPartPics;

	//诊断信息
	@ApiModelProperty("就诊医院id")
	private Long hospitalId;

	@ApiModelProperty("就诊医院")
	private String hospital;

	@ApiModelProperty("初诊日期")
	private Date firstClinicDate;

	@ApiModelProperty("诊断结果")
	private List diagnoses;

	@ApiModelProperty("住院科室")
	private String department;

	@ApiModelProperty("床位号")
	private String bedNumber;

	@ApiModelProperty("事故者状态")
	private String injuredStatus;

	//4.申报材料
	@ApiModelProperty("工伤(亡)认定申请表")
	private List injureDeclaration;

	@ApiModelProperty("受伤职工有效身份证明")
	private List idCardPics;

	@ApiModelProperty("与用人单位存在劳动关系的证明材料")
	private List laborRelation;

	@ApiModelProperty("用人单位事故调查报告书")
	private List orgSurveyReport;

	@ApiModelProperty("超退休年龄未领取养老金证明")
	private List unclaimedPensionProof;

	@ApiModelProperty("有无证人")
	private Boolean hasWitness;

	@ApiModelProperty("无证人申明")
	private List noWitnessDeclare;

	@ApiModelProperty("证人证言1")
	private List witnessTestimony1;

	@ApiModelProperty("证人证言2")
	private List witnessTestimony2;

	@ApiModelProperty("证人证言3")
	private List witnessTestimony3;

	@ApiModelProperty("证人证言4")
	private List witnessTestimony4;

	@ApiModelProperty("医疗就诊材料")
	private List medicalDiagnose;

	@ApiModelProperty("职业病诊断证明或鉴定书")
	private List occupDiseaseDiagnose;

	@ApiModelProperty("授权委托书")
	private List authorizationLetter;

	@ApiModelProperty("经办人身份证明")
	private List applicantCert;

	@ApiModelProperty("用人单位的营业证明")
	private List businessCertificate;

	@ApiModelProperty("工会介绍信")
	private List unionReference;

	@ApiModelProperty("医学死亡证明或火化证明")
	private List deathCremationCert;

	@ApiModelProperty("特殊认定情况")
	private String specialCondition;

	@ApiModelProperty("特殊认定")
	private SpecialIdentify specialIdentify;

	@ApiModelProperty("实物提交方式")
	private String materialSubmitWay;

	@ApiModelProperty("实物提交运单号")
	private List materialSubmitMailSn;

	@ApiModelProperty("通知领取方式")
	private String informReceiveWay;

	@ApiModelProperty("通知收件方式")
	private UserAddressOut informReceiveAddr;

	@ApiModelProperty("受理通知书签名")
	private String acceptInformSignature;

	@ApiModelProperty("通知确认收到签字")
	private String informReceiveSignature;

	@ApiModelProperty("通知领取运单号")
	private List informReceiveMailSn;

	@ApiModelProperty("认定工伤决定书")
	private String injureIdentification;

	@ApiModelProperty("不予认定工伤决定书")
	private String injureNotIdentify;

	@ApiModelProperty("伤害类型")
	private String injureType;

	@ApiModelProperty("伤害表现")
	private String injureExpression;

	@ApiModelProperty("伤残等级")
	private String injureLevel;

	@ApiModelProperty("撤销申请书")
	private List cancelApplication;

	@ApiModelProperty("撤销申请时间")
	private Date cancelApplyTime;

	@ApiModelProperty("撤销成功时间")
	private Date canceledTime;

	@ApiModelProperty("是否已经发起调查")
	private Boolean hasIssuedInvestigate;

	@ApiModelProperty("是否材料递交方式开放")
	private Boolean isSubmitWayOpen;

	//各种结论
	@ApiModelProperty("受理结论")
	private AcceptConclusion acceptConclusion;

	@ApiModelProperty("认定结论")
	private IdentifyConclusion identifyConclusion;

	@ApiModelProperty("退回结论")
	private RejectConclusion rejectConclusion;

	@ApiModelProperty("认定中止结论")
	private SuspendedConclusion suspendedConclusion;

	@ApiModelProperty("认定中止结论")
	private CancelConclusion cancelConclusion;

	@ApiModelProperty("认定中止材料")
	private List suspendFileList;

	@ApiModelProperty("申请工伤认定提交材料目录 文件名称")
	private String materialCatalogUrl;

	@ApiModelProperty("工伤认定材料清单 文件名称")
	private String materialListUrl;

	@ApiModelProperty("工伤认定申请表 文件名称")
	private String applyUrl;

	@ApiModelProperty("诊断信息")
	private List<DiagnosticInfo> diagnosticInfoList;

	@ApiModelProperty("单位注册住所-单位区域")
	private List organizationRegionV2;

	@ApiModelProperty("单位注册住所-单位地址")
	private String organizationAddrV2;

	@ApiModelProperty("单位注册住所-单位地址经度")
	private Double organizationLongitudeV2;

	@ApiModelProperty("单位注册住所-单位地址纬度")
	private Double organizationLatitudeV2;

	@ApiModelProperty("工伤认定申请材料")
	private List declarationMaterials;

	@ApiModelProperty("伤者授权委托书")
	private List authorizationLetterV2;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherMaterials;


	@ApiModelProperty("事故责任认定书")
	private List acciRespIdentify;

	@ApiModelProperty("路线图")
	private List routeMap;

	@ApiModelProperty("居住证明")
	private List residenceProof;

//	@ApiModelProperty("医学死亡证明或火化证明")
//	private List deathCremationCert;

	@ApiModelProperty("法院判决书，公安机关证明或其他证明")
	private List courtPoliceCert;

	@ApiModelProperty("民政部门或其他部门的证明")
	private List civilAffairsCert;

	@ApiModelProperty("伤残军人证")
	private List disabledMilitaryCert;

	@ApiModelProperty("旧伤复发鉴定证明")
	private List recurrenceCert;

	@ApiModelProperty("公安机关证明或者其它有效证明")
	private List publicSecurityCert;

	@ApiModelProperty("因工外出证明")
	private List workTravelCert;

	@ApiModelProperty("人民法院宣告死亡的结论")
	private List courtDeathCert;

//	@ApiModelProperty("因工外出证明")
//	private List workTravelCert;

	@ApiModelProperty("其他补充说明")
	private String otherSupplementary;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherCert;

	@ApiModelProperty("案件申请时间")
	private Date submitTime;

	private CasesExport casesExport;

	private String occupDiseaseDateStart;

	private String occupDiseaseDateEnd;

	private DiagnosticInfo diagnosticInfo;

	private String otherDiagnosticInfo;

	@ExcelProperty(converter = BooleanStringConverter.class)
	private Boolean isReceiveFile;

	private String receiveFileWritSn;

	@ExcelProperty(converter = BooleanStringConverter.class)
	private Boolean isSuspendedConclusion;

	@ExcelProperty(converter = BooleanStringConverter.class)
	private Boolean fromCases;

	public Boolean getFromCases() {
		return ObjectUtil.isNotEmpty(this.casesId);
	}

	// casesExport

	@ApiModelProperty(value = "报案人姓名")
	private String reportName;

	public String getReportName() {
		if (ObjectUtil.isNotNull(casesExport)) {
			return casesExport.getReportName();
		}
		return reportName;
	}

	@ApiModelProperty(value = "与当事人关系")
	private String casesRelationship;

	public String getCasesRelationship() {
		if (ObjectUtil.isNotNull(casesExport)) {
			return casesExport.getRelationship();
		}
		return casesRelationship;
	}

	@ApiModelProperty(value = "手机号码")
	private String casesPhone;

	public String getCasesPhone() {
		if (ObjectUtil.isNotNull(casesExport)) {
			return casesExport.getPhone();
		}
		return casesPhone;
	}

	@ApiModelProperty("案件申请时间")
	private Date casesSubmitTime;

	public Date getCasesSubmitTime() {
		if (ObjectUtil.isNotNull(casesExport)) {
			return casesExport.getSubmitTime();
		}
		return casesSubmitTime;
	}

	@ApiModelProperty("单位名称")
	private String casesOrganization;

	public String getCasesOrganization() {
		if (ObjectUtil.isNotNull(casesExport)) {
			return casesExport.getOrganization();
		}
		return casesOrganization;
	}

	// diagnosticInfo

	@ApiModelProperty("就诊医院")
	private String diagnosticInfoHospital;

	public String getDiagnosticInfoHospital() {
		if (ObjectUtil.isNotNull(diagnosticInfo)) {
			return diagnosticInfo.getHospital();
		}
		return diagnosticInfoHospital;
	}

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("初诊日期")
	private Date diagnosticInfoFirstClinicDate;

	public Date getDiagnosticInfoFirstClinicDate() {
		if (ObjectUtil.isNotNull(diagnosticInfo)) {
			return diagnosticInfo.getFirstClinicDate();
		}
		return diagnosticInfoFirstClinicDate;
	}

	@ApiModelProperty("诊断结果")
	@ExcelProperty(converter = ListStringConverter.class)
	private List<String> diagnosticInfoDiagnoses;

	public List<String> getDiagnosticInfoDiagnoses() {
		if (ObjectUtil.isNotNull(diagnosticInfo)) {
			return diagnosticInfo.getDiagnoses();
		}
		return diagnosticInfoDiagnoses;
	}

	private String acceptConclusionConclusion;

	public String getAcceptConclusionConclusion() {
		if (ObjectUtil.isNotNull(acceptConclusion)) {
			return acceptConclusion.getConclusion();
		}
		return acceptConclusionConclusion;
	}

	private String acceptConclusionReason;

	public String getAcceptConclusionReason() {
		if (ObjectUtil.isNotNull(acceptConclusion)) {
			return acceptConclusion.getReason();
		}
		return acceptConclusionReason;
	}

	private String acceptConclusionWritSn;

	public String getAcceptConclusionWritSn() {
		if (ObjectUtil.isNotNull(acceptConclusion)) {
			return acceptConclusion.getWritSn();
		}
		return acceptConclusionWritSn;
	}

	// 受理时间 v2.0.1新增
	private String acceptConclusionAcceptDateTime;

	public String getAcceptConclusionAcceptDateTime() {
		if (ObjectUtil.isNotNull(acceptConclusion)) {
			return acceptConclusion.getAcceptDateTime();
		}
		return acceptConclusionAcceptDateTime;
	}

	// SuspendedConclusion 展开字段
//	private String suspendedConclusionConclusion;
//
//	public String getSuspendedConclusionConclusion() {
//		if (ObjectUtil.isNotNull(suspendedConclusion)) {
//			return suspendedConclusion.getConclusion();
//		}
//		return suspendedConclusionConclusion;
//	}

	private String suspendedConclusionReason;

	public String getSuspendedConclusionReason() {
		if (ObjectUtil.isNotNull(suspendedConclusion)) {
			return suspendedConclusion.getReason();
		}
		return suspendedConclusionReason;
	}

	private String suspendedConclusionWritSn;

	public String getSuspendedConclusionWritSn() {
		if (ObjectUtil.isNotNull(suspendedConclusion)) {
			return suspendedConclusion.getWritSn();
		}
		return suspendedConclusionWritSn;
	}

//	private String suspendedConclusionSuspendDateTime;
//
//	public String getSuspendedConclusionSuspendDateTime() {
//		if (ObjectUtil.isNotNull(suspendedConclusion)) {
//			return suspendedConclusion.getSuspendDateTime();
//		}
//		return suspendedConclusionSuspendDateTime;
//	}

	private String identifyConclusionConclusion;

	public String getIdentifyConclusionConclusion() {
		if (ObjectUtil.isNotNull(identifyConclusion)) {
			return identifyConclusion.getConclusion();
		}
		return identifyConclusionConclusion;
	}

	private String identifyConclusionReason;

	public String getIdentifyConclusionReason() {
		if (ObjectUtil.isNotNull(identifyConclusion)) {
			return identifyConclusion.getReason();
		}
		return identifyConclusionReason;
	}

	private String identifyConclusionWritSn;

	public String getIdentifyConclusionWritSn() {
		if (ObjectUtil.isNotNull(identifyConclusion)) {
			return identifyConclusion.getWritSn();
		}
		return identifyConclusionWritSn;
	}

//	private String identifyConclusionIdentifyDateTime;
//
//	public String getIdentifyConclusionIdentifyDateTime() {
//		if (ObjectUtil.isNotNull(identifyConclusion)) {
//			return identifyConclusion.getIdentifyDateTime();
//		}
//		return identifyConclusionIdentifyDateTime;
//	}

//	private String cancelConclusionConclusion;
//
//	public String getCancelConclusionConclusion() {
//		if (ObjectUtil.isNotNull(cancelConclusion)) {
//			return cancelConclusion.getConclusion();
//		}
//		return cancelConclusionConclusion;
//	}

	private String cancelConclusionReason;

	public String getCancelConclusionReason() {
		if (ObjectUtil.isNotNull(cancelConclusion)) {
			return cancelConclusion.getReason();
		}
		return cancelConclusionReason;
	}

	private String cancelConclusionWritSn;

	public String getCancelConclusionWritSn() {
		if (ObjectUtil.isNotNull(cancelConclusion)) {
			return cancelConclusion.getWritSn();
		}
		return cancelConclusionWritSn;
	}

//	private String cancelConclusionCancelDateTime;
//
//	public String getCancelConclusionCancelDateTime() {
//		if (ObjectUtil.isNotNull(cancelConclusion)) {
//			return cancelConclusion.getCancelDateTime();
//		}
//		return cancelConclusionCancelDateTime;
//	}

}
