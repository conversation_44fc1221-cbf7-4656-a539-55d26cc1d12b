package com.yixun.wid.v2.entity;

import lombok.Data;
import java.util.List;

/**
 * 核销明细-门诊
 * 将所有门诊发票按费用项目分项汇总计算
 */
@Data
public class OutpatientClearing {
    
    /**
     * 门诊费用项目核销明细列表
     */
    private List<OutpatientClearingItem> clearingItems;
    
    /**
     * 总申报金额（分）
     */
    private Integer totalClaimAmountInCent;
    
    /**
     * 总扣减费用（分）
     */
    private Integer totalDeductionAmountInCent;
    
    /**
     * 总应付金额（分）
     */
    private Integer totalPayableAmountInCent;
    
    /**
     * 计算汇总金额
     * 根据明细项计算总申报金额、总扣减费用、总应付金额
     */
    public void calculateTotals() {
        if (clearingItems == null || clearingItems.isEmpty()) {
            totalClaimAmountInCent = 0;
            totalDeductionAmountInCent = 0;
            totalPayableAmountInCent = 0;
            return;
        }
        
        totalClaimAmountInCent = clearingItems.stream()
            .mapToInt(item -> item.getTotalClaimAmountInCent() != null ? item.getTotalClaimAmountInCent() : 0)
            .sum();
            
        totalDeductionAmountInCent = clearingItems.stream()
            .mapToInt(item -> item.getTotalDeductionAmountInCent() != null ? item.getTotalDeductionAmountInCent() : 0)
            .sum();
            
        totalPayableAmountInCent = clearingItems.stream()
            .mapToInt(item -> item.getPayableAmountInCent() != null ? item.getPayableAmountInCent() : 0)
            .sum();
    }
} 