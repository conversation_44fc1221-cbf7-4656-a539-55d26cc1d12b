package com.yixun.wid.v2.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.entity.em.DictType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.utils.BeanFieldCheckingUtils;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.v2.bean.in.DataDictListIn;
import com.yixun.wid.v2.entity.DataDict;
import com.yixun.wid.v2.service.DataDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Api(tags = "admin字典管理")
@RestController
@RequestMapping(value = "/v2/admin/dict")
public class AdminDictController {

    @Resource
    private DataDictService dataDictService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @GetMapping("/get-dict-list")
    @ApiOperation(value = "获取各种数据字典列表")
    public CommonResult<List<DataDict>> getDataDictList(String parentcode, DictType datatype,String label, CommonPage page){

        Page<DataDict> dataDictList = dataDictService.getAdminDataDictList(parentcode, datatype,label, page);

        return CommonResult.successData(dataDictList);
    }

    @GetMapping("/get-all-dict")
    @ApiOperation(value = "根据类型获取各种数据字典列表")
    public CommonResult<List<DataDict>> getDataDictListV2(DictType datatype){
        return CommonResult.successData(dataDictService.getAllDataDictList(datatype.name()));
    }

    @PostMapping("/doSave")
    @ApiOperation(value = "保存数据字典")
    public CommonResult<Void> doSave(@RequestBody DataDict dataDict){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(dataDict)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        try {
            dataDictService.insert(dataDict);
        }catch (Exception e){
            if (e.getMessage().contains("Duplicate entry")){
                throw new DataErrorException("类型下节点code值重复");
            }else{
                throw new DataErrorException(e.getMessage());
            }
        }
        redisTemplate.delete(dataDict.getDatatype()+"Dict");

        return CommonResult.successResult("保存成功");
    }

    @PostMapping("/doSaveList")
    @ApiOperation(value = "批量保存数据字典")
    public CommonResult<Void> doSaveList(@RequestBody DataDictListIn dataDictListIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(dataDictListIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        List<DataDictListIn.DataDictList> dataDictInList = dataDictListIn.getDataDictList();
        List<String> nodes = dataDictInList.stream().map(DataDictListIn.DataDictList::getNodecode).collect(Collectors.toList());
        List<DataDict> dictList = dataDictService.getDataDictListByNodes(nodes, dataDictListIn.getDatatype());

        try {
            dataDictInList.forEach(d->{
                Optional<DataDict> first = dictList.stream().filter(dict -> dict.getNodecode().equals(d.getNodecode())).findFirst();
                if (first.isPresent()){
                    DataDict dataDict = first.get();
                    BeanUtils.copyProperties(d, dataDict, true);
                    dataDictService.update(dataDict);
                }else {
                    DataDict dataDict = new DataDict();
                    BeanUtils.copyProperties(d, dataDict);
                    dataDict.setDatatype(dataDictListIn.getDatatype());
                    dataDictService.insert(dataDict);
                }
            });
        }catch (Exception e){
            if (e.getMessage().contains("Duplicate entry")){
                throw new DataErrorException("类型下节点code值重复");
            }else{
                throw new DataErrorException(e.getMessage());
            }
        }
        redisTemplate.delete(dataDictListIn.getDatatype()+"Dict");

        return CommonResult.successResult("保存成功");
    }

    @PostMapping("/doEdit")
    @ApiOperation(value = "修改数据字典")
    public CommonResult<Void> doEdit(@RequestBody DataDict dataDict){

        //检查参数
        if (dataDict.getNodecode()==null || dataDict.getDatatype()==null){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        DataDict dd = dataDictService.getOne(dataDict.getNodecode(), dataDict.getDatatype());
        if (dd==null){
            throw new DataErrorException("该字典不存在");
        }
        BeanUtils.copyProperties(dataDict, dd, true);

        try {
            dataDictService.update(dd);
        }catch (Exception e){
            if (e.getMessage().contains("Duplicate entry")){
                throw new DataErrorException("类型下节点code值重复");
            }else{
                throw new DataErrorException(e.getMessage());
            }
        }
        redisTemplate.delete(dataDict.getDatatype()+"Dict");

        return CommonResult.successResult("修改成功");
    }

}
