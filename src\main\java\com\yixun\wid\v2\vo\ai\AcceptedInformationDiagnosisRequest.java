package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 受理信息和临床诊断请求参数
 */
@Data
public class AcceptedInformationDiagnosisRequest {

    /**
     * 申请材料
     */
    private ApplicationMaterials application;

    /**
     * 就诊材料列表
     */
    private List<MedicalVisit> medical;

    @Data
    public static class ApplicationMaterials {
        @JSONField(name = "work_injury_benefit_application_form")
        private List<String> workInjuryBenefitApplicationForm;

        @JSONField(name = "work_injury_medical_rehabilitation_benefit_application_form")
        private List<String> workInjuryMedicalRehabilitationBenefitApplicationForm;

        @JSONField(name = "work_injury_determination_document")
        private List<String> workInjuryDeterminationDocument;

        @JSONField(name = "work_injury_receipt_list")
        private List<String> workInjuryReceiptList;

        @JSONField(name = "id_card")
        private List<String> idCard;

        @JSONField(name = "social_security_card")
        private List<String> socialSecurityCard;

        @JSONField(name = "bank_card")
        private List<String> bankCard;

        @JSONField(name = "initial_reexamination_appraisal_conclusion_document")
        private List<String> initialReexaminationAppraisalConclusionDocument;

        @JSONField(name = "non_tax_revenue_general_payment_document")
        private List<String> nonTaxRevenueGeneralPaymentDocument;

        private List<OtherMaterial> other;
    }
    
    @Data
    public static class MedicalVisit {
        /**
         * 就诊类型：住院/门诊
         */
        @JSONField(name = "visit_type")
        private String visitType;

        /**
         * 就诊时间
         */
        @JSONField(name = "visit_date")
        private String visitDate;

        /**
         * 材料
         */
        private MedicalMaterials material;
    }
    
    @Data
    public static class MedicalMaterials {
        @JSONField(name = "medical_record")
        private MedicalRecord medicalRecord;

        @JSONField(name = "surgical_record")
        private List<String> surgicalRecord;

        private List<String> certificate;
        private List<String> list;

        @JSONField(name = "electronic_invoice")
        private List<String> electronicInvoice;

        @JSONField(name = "non_electronic_invoice")
        private List<String> nonElectronicInvoice;

        @JSONField(name = "recall_notice")
        private List<String> recallNotice;

        @JSONField(name = "examination_report")
        private List<String> examinationReport;

        @JSONField(name = "doctor_order_sheet")
        private List<String> doctorOrderSheet;

        @JSONField(name = "other_consultation_reports")
        private List<String> otherConsultationReports;

        @JSONField(name = "other_unidentified")
        private List<String> otherUnidentified;
    }

    @Data
    public static class MedicalRecord {
        /**
         * 病历小类，如果修改文件类型为病历就添加到这里
         */
        @JSONField(name = "medical_record_subcategory")
        private List<String> medicalRecordSubcategory;

        /**
         * 病情证明书
         */
        @JSONField(name = "medical_condition_certificate")
        private List<String> medicalConditionCertificate;

        /**
         * 出院证明书
         */
        @JSONField(name = "discharge_certificate")
        private List<String> dischargeCertificate;

        /**
         * 出院记录
         */
        @JSONField(name = "discharge_record")
        private List<String> dischargeRecord;

        /**
         * 住院病案首页
         */
        @JSONField(name = "inpatient_medical_record_first_page")
        private List<String> inpatientMedicalRecordFirstPage;

        /**
         * 诊断证明书
         */
        @JSONField(name = "diagnosis_certificate")
        private List<String> diagnosisCertificate;

        /**
         * 入院证
         */
        @JSONField(name = "admission_certificate")
        private List<String> admissionCertificate;

        /**
         * 入院记录
         */
        @JSONField(name = "admission_record")
        private List<String> admissionRecord;

        /**
         * 处方签(笺)
         */
        @JSONField(name = "prescription_slip")
        private List<String> prescriptionSlip;

        /**
         * 体温单
         */
        @JSONField(name = "temperature_sheet")
        private List<String> temperatureSheet;

        /**
         * 病程记录
         */
        @JSONField(name = "progress_note")
        private List<String> progressNote;
    }

    @Data
    public static class OtherMaterial {
        @JSONField(name = "type_name")
        private String typeName;

        private String file;
    }
}
