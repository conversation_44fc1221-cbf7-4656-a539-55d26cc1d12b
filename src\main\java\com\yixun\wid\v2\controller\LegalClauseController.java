package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.LegalClause;
import com.yixun.wid.v2.entity.LegalRegulation;
import com.yixun.wid.v2.enums.GeneralStatusEnum;
import com.yixun.wid.v2.vo.SortVO;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 法规条款管理接口
 */
@RequestMapping("/v2/legal/clause")
@RestController
@AllArgsConstructor
public class LegalClauseController {

    private final MongoTemplate mongoTemplate;
    private final LegalRegulationController legalRegulationController;

    /**
     * 新增法规条款
     *
     * @param legalClause 法规条款
     */
    @PostMapping("/save")
    public CommonResult<Void> save(@Valid @RequestBody LegalClause legalClause) {
        // 验证法规依据是否存在
        if (ObjectUtil.isNotNull(legalClause.getRegulationId())) {
            try {
                CommonResult<LegalRegulation> result = legalRegulationController.getById(legalClause.getRegulationId());
                LegalRegulation regulation = result.getData();
                legalClause.setRegulationName(regulation.getRegulationName());
            } catch (Exception e) {
                throw new RuntimeException("关联的法规依据不存在");
            }
        }

        legalClause.setId(SnGeneratorUtil.getId());
        legalClause.setSort(SnGeneratorUtil.getId());
        if (ObjectUtil.isNull(legalClause.getStatus())) {
            legalClause.setStatus(GeneralStatusEnum.ON.getCode());
        }
        legalClause.setCreateTime(new Date());
        legalClause.setUpdateTime(new Date());
        mongoTemplate.save(legalClause);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询法规条款列表
     *
     * @param status       状态
     * @param search       搜索关键词
     * @param commonPage   分页参数
     * @param regulationId 法规依据ID
     * @return 法规条款列表
     */
    @GetMapping("/list")
    public CommonResult<List<LegalClause>> list(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search,
            CommonPage commonPage,
            @RequestParam(required = false) Long regulationId) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "clauseNumber"))
                .with(Sort.by(Sort.Direction.ASC, "subClauseNumber"))
                .with(Sort.by(Sort.Direction.DESC, "sort"))
                .with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));

        if (StrUtil.isNotBlank(search)) {
            Criteria criteria = new Criteria();
            criteria.orOperator(
                    Criteria.where("clauseContent").regex(".*" + search + ".*", "i"),
                    Criteria.where("subClauseContent").regex(".*" + search + ".*", "i"),
                    Criteria.where("regulationName").regex(".*" + search + ".*", "i")
            );
            query.addCriteria(criteria);
        }

        if (ObjectUtil.isNotNull(regulationId)) {
            query.addCriteria(Criteria.where("regulationId").is(regulationId));
        }

        if (ObjectUtil.isNotNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }

        MongoUtil.setPageInfo(mongoTemplate, LegalClause.class, query, commonPage);
        List<LegalClause> legalClauses = mongoTemplate.find(query, LegalClause.class);

        // 填充法规名称
        fillRegulationNames(legalClauses);

        return CommonResult.successPageData(legalClauses, commonPage);
    }

    /**
     * 根据法规依据ID查询条款列表（不分页）
     *
     * @param regulationId 法规依据ID
     * @param status       状态
     * @return 法规条款列表
     */
    @GetMapping("/listByRegulation")
    public CommonResult<List<LegalClause>> listByRegulation(
            @RequestParam Long regulationId,
            @RequestParam(required = false) Integer status) {

        Query query = Query.query(Criteria.where("regulationId").is(regulationId))
                .with(Sort.by(Sort.Direction.ASC, "clauseNumber"))
                .with(Sort.by(Sort.Direction.ASC, "subClauseNumber"))
                .with(Sort.by(Sort.Direction.DESC, "sort"));

        if (ObjectUtil.isNotNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }

        List<LegalClause> legalClauses = mongoTemplate.find(query, LegalClause.class);

        // 填充法规名称
        fillRegulationNames(legalClauses);

        return CommonResult.successData(legalClauses);
    }

    /**
     * 根据id查询法规条款
     *
     * @param id 主键
     * @return 法规条款
     */
    @GetMapping
    public CommonResult<LegalClause> getById(@RequestParam("id") Long id) {
        LegalClause legalClause = mongoTemplate.findById(id, LegalClause.class);
        if (ObjectUtil.isNull(legalClause)) {
            throw new RuntimeException("法规条款不存在");
        }

        // 填充法规名称
        if (ObjectUtil.isNotNull(legalClause.getRegulationId())) {
            try {
                CommonResult<LegalRegulation> result = legalRegulationController.getById(legalClause.getRegulationId());
                LegalRegulation regulation = result.getData();
                legalClause.setRegulationName(regulation.getRegulationName());
            } catch (Exception ignore) {
                // 忽略异常，保持原有数据
            }
        }

        return CommonResult.successData(legalClause);
    }

    /**
     * 更新法规条款
     *
     * @param legalClause 法规条款
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@Valid @RequestBody LegalClause legalClause) {
        Long id = legalClause.getId();
        if (ObjectUtil.isNull(id)) {
            throw new RuntimeException("id不能为空");
        }
        LegalClause existingClause = mongoTemplate.findById(id, LegalClause.class);
        if (ObjectUtil.isNull(existingClause)) {
            throw new RuntimeException("法规条款不存在");
        }

        // 验证法规依据是否存在并更新法规名称
        if (ObjectUtil.isNotNull(legalClause.getRegulationId())) {
            try {
                CommonResult<LegalRegulation> result = legalRegulationController.getById(legalClause.getRegulationId());
                LegalRegulation regulation = result.getData();
                legalClause.setRegulationName(regulation.getRegulationName());
            } catch (Exception e) {
                throw new RuntimeException("关联的法规依据不存在");
            }
        }

        BeanUtil.copyProperties(legalClause, existingClause, CopyOptions.create().ignoreNullValue());
        existingClause.setUpdateTime(new Date());
        mongoTemplate.save(existingClause);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 批量删除法规条款
     *
     * @param ids 法规条款ID列表
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Void> batchDelete(@RequestBody List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new RuntimeException("id列表不能为空");
        }

        Query query = Query.query(Criteria.where("id").in(ids));
        mongoTemplate.remove(query, LegalClause.class);
        return CommonResult.successResult("删除成功");
    }

    /**
     * 更新法规条款列表排序
     *
     * @param sort 列表排序
     * @return 更新结果
     */
    @PostMapping("/update/sort")
    public CommonResult<Void> updateSort(@RequestBody List<SortVO> sort) {
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, LegalClause.class);
        for (SortVO sortVO : sort) {
            Update update = Update.update("sort", sortVO.getSort())
                    .set("updateTime", new Date());
            operations.updateOne(Query.query(Criteria.where("id").is(sortVO.getId())), update);
        }
        operations.execute();
        return CommonResult.successResult("操作成功");
    }

    /**
     * 填充法规名称
     */
    private void fillRegulationNames(List<LegalClause> legalClauses) {
        if (ObjectUtil.isEmpty(legalClauses)) {
            return;
        }

        // 获取所有法规依据ID
        List<Long> regulationIds = legalClauses.stream()
                .map(LegalClause::getRegulationId)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        if (ObjectUtil.isEmpty(regulationIds)) {
            return;
        }

        // 批量查询法规依据
        Query regulationQuery = Query.query(Criteria.where("id").in(regulationIds));
        List<LegalRegulation> regulations = mongoTemplate.find(regulationQuery, LegalRegulation.class);
        Map<Long, LegalRegulation> regulationMap = regulations.stream()
                .collect(Collectors.toMap(LegalRegulation::getId, Function.identity()));

        // 填充法规名称
        for (LegalClause clause : legalClauses) {
            Long regulationId = clause.getRegulationId();
            if (ObjectUtil.isNotNull(regulationId)) {
                LegalRegulation regulation = regulationMap.get(regulationId);
                if (ObjectUtil.isNotNull(regulation)) {
                    clause.setRegulationName(regulation.getRegulationName());
                }
            }
        }
    }

    /**
     * 根据法规依据ID和条款序号查询条款列表
     *
     * @param regulationId  法规依据ID
     * @param clauseNumber  条款序号
     * @return 法规条款列表
     */
    @GetMapping("/listByClauseNumber")
    public CommonResult<List<LegalClause>> listByClauseNumber(
            @RequestParam Long regulationId,
            @RequestParam Integer clauseNumber) {

        Query query = Query.query(Criteria.where("regulationId").is(regulationId))
                .addCriteria(Criteria.where("clauseNumber").is(clauseNumber))
                .addCriteria(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()))
                .with(Sort.by(Sort.Direction.ASC, "subClauseNumber"))
                .with(Sort.by(Sort.Direction.DESC, "sort"));

        List<LegalClause> legalClauses = mongoTemplate.find(query, LegalClause.class);

        // 填充法规名称
        fillRegulationNames(legalClauses);

        return CommonResult.successData(legalClauses);
    }

    /**
     * 获取指定法规依据下的所有条款序号
     *
     * @param regulationId 法规依据ID
     * @return 条款序号列表
     */
    @GetMapping("/clauseNumbers")
    public CommonResult<List<Integer>> getClauseNumbers(@RequestParam Long regulationId) {
        Query query = Query.query(Criteria.where("regulationId").is(regulationId))
                .addCriteria(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()));

        List<LegalClause> legalClauses = mongoTemplate.find(query, LegalClause.class);

        List<Integer> clauseNumbers = legalClauses.stream()
                .map(LegalClause::getClauseNumber)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        return CommonResult.successData(clauseNumbers);
    }
}
