package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisRequest;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工伤待遇业务
 */
@Data
public class MedicalCases {

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 案件编号
     * 格式：BX1014受理日期+序号，例：BX101420250604001
     */
    private String caseNumber;

    /**
     * 业务办理状态
     */
    private MedicalCasesStatus status;

    // 受理信息

    /**
     * 职工姓名
     */
    private String workerName;

    /**
     * 用人单位名称
     */
    private String organization;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 性别
     */
    private String gender;

    /**
     * 是否参保
     */
    private Boolean hasInsurance;

    /**
     * 参保地
     */
    private String insuranceAddress;

    /**
     * 业务申请时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 事故日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date accidentDate;

    /**
     * 受理日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date acceptDate;

    /**
     * 治疗类型
     */
    private String treatmentType;

    /**
     * 申请业务列表
     */
    private List<String> businessTypes;

    // 诊断信息

    /**
     * 工伤诊断列表
     */
    private List<String> injuryDiagnoses;

    /**
     * 手术信息列表
     */
    private List<String> surgeryInfos;

    /**
     * 账单信息列表，用于前端传递和返回数据，不会持久化到数据库
     */
    @Transient
    private List<BillingInfo> billingInfos;

    /**
     * 待遇材料
     */
    private List<TreatmentMaterials> treatmentMaterials;

	/**
	 * 待遇材料是否提交 为null或false表示未提交，true表示已提交
	 */
	private Boolean treatmentMaterialsSubmit;

	/**
	 * 材料类型识别响应数据存储
	 */
	private AcceptedInformationDiagnosisRequest acceptedInformationDiagnosisRequest;

    /**
     * 任务接收人id
     */
    private String assigneeUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 创建用户id
     */
    private String createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 最近操作用户id
     */
    private String recentUserId;

    /**
     * 最近操作用户名
     */
    private String recentUserName;

    /**
     * 提交用户id
     */
    private String submitUserId;

    /**
     * 提交用户名称
     */
    private String submitUserName;

    /**
     * 受理用户id
     */
    private String acceptUserId;

    /**
     * 受理用户名称
     */
    private String acceptUserName;

	/**
	 * 受理时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date acceptTime;

    /**
     * 初审用户id
     */
    private String preReviewUserId;

    /**
     * 初审用户名
     */
    private String preReviewUserName;

    /**
     * 初审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date preReviewTime;

    /**
     * 复审用户id
     */
    private String reviewUserId;

    /**
     * 复审用户名
     */
    private String reviewUserName;

    /**
     * 复审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date reviewTime;

    /**
     * 终审用户id
     */
    private String finalReviewUserId;

    /**
     * 终审用户名
     */
    private String finalReviewUserName;

    /**
     * 终审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date finalReviewTime;

    /**
     * 是否为提交操作
     */
    private Boolean isSubmit;

	/**
	 * 理算结果 map结构 key为ResponsibilityTree的code
	 */
    private Map<String, ClaimsInformation> claimsInformations;

    /**
     * 理算条信息
     */
    @Data
    public static class ClaimsInformation {

        /**
         * 责任树编码
         */
        private String responsibilityTreeCode;

        /**
         * 理算状态（true：已理算，false：未理算）
         */
        private Boolean claimsStatus;

        /**
         * 理算信息数据
         */
        private ClaimsData claimsData;

        /**
         * 理算结果信息
         */
        private ClaimsResult claimsResult;

        /**
         * 门诊核销明细
         */
        private OutpatientClearing outpatientClearing;

        /**
         * 住院核销明细
         */
        private HospitalClearing hospitalClearing;

    }

}
