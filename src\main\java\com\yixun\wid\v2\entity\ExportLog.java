package com.yixun.wid.v2.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Map;

/**
 * 导出记录日志实体
 */
@Data
//@Document(collection = "export_log")
public class ExportLog {

    @Id
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("导出人ID")
    private Long exportUserId;

	private String exportUserType;

    @ApiModelProperty("导出人姓名")
    private String exportUserName;

    @ApiModelProperty("导出时间")
    private Date exportTime;

    @ApiModelProperty("导出模块")
    private String exportModule;

    @ApiModelProperty("导出文件名")
    private String fileName;

    @ApiModelProperty("导出数据量")
    private Integer dataCount;

    @ApiModelProperty("导出条件")
    private String exportConditions;

    @ApiModelProperty("用人单位ID")
    private Long organizationId;

    @ApiModelProperty("用人单位名称")
    private String organizationName;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("时间范围（月）")
    private Integer timeRange;

    @ApiModelProperty("导出状态：SUCCESS-成功，FAILED-失败")
    private String status;

    @ApiModelProperty("错误信息（导出失败时记录）")
    private String errorMessage;

    @ApiModelProperty("IP地址")
    private String ipAddress;

    @ApiModelProperty("用户代理")
    private String userAgent;

    @ApiModelProperty("其他扩展信息")
    private Map<String, Object> extraInfo;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
