package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.LegalClause;
import com.yixun.wid.v2.entity.LegalRegulation;
import com.yixun.wid.v2.enums.GeneralStatusEnum;
import com.yixun.wid.v2.vo.SortVO;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 法规依据管理接口
 */
@RequestMapping("/v2/legal/regulation")
@RestController
@AllArgsConstructor
public class LegalRegulationController {

    private final MongoTemplate mongoTemplate;

    /**
     * 新增法规依据
     *
     * @param legalRegulation 法规依据
     */
    @PostMapping("/save")
    public CommonResult<Void> save(@Valid @RequestBody LegalRegulation legalRegulation) {
        legalRegulation.setId(SnGeneratorUtil.getId());
        legalRegulation.setSort(SnGeneratorUtil.getId());
        if (ObjectUtil.isNull(legalRegulation.getStatus())) {
            legalRegulation.setStatus(GeneralStatusEnum.ON.getCode());
        }
        legalRegulation.setCreateTime(new Date());
        legalRegulation.setUpdateTime(new Date());
        mongoTemplate.save(legalRegulation);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询法规依据列表
     *
     * @param status     状态
     * @param search     搜索关键词（法规名称）
     * @param commonPage 分页参数
     * @return 法规依据列表
     */
    @GetMapping("/list")
    public CommonResult<List<LegalRegulation>> list(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search,
            CommonPage commonPage) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "sort"))
                .with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
        
        if (StrUtil.isNotBlank(search)) {
            query.addCriteria(Criteria.where("regulationName").regex(".*" + search + ".*", "i"));
        }
        
        if (ObjectUtil.isNotNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        
        MongoUtil.setPageInfo(mongoTemplate, LegalRegulation.class, query, commonPage);
        List<LegalRegulation> legalRegulations = mongoTemplate.find(query, LegalRegulation.class);
        return CommonResult.successPageData(legalRegulations, commonPage);
    }

    /**
     * 查询法规依据列表（不分页）
     *
     * @param status  状态
     * @param search  搜索关键词
     * @return 法规依据列表
     */
    @GetMapping("/listAll")
    public CommonResult<List<LegalRegulation>> listAll(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "sort"))
                .with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
        
        if (StrUtil.isNotBlank(search)) {
            query.addCriteria(Criteria.where("regulationName").regex(".*" + search + ".*", "i"));
        }
        
        if (ObjectUtil.isNotNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        
        List<LegalRegulation> legalRegulations = mongoTemplate.find(query, LegalRegulation.class);
        return CommonResult.successData(legalRegulations);
    }

    /**
     * 根据id查询法规依据
     *
     * @param id 主键
     * @return 法规依据
     */
    @GetMapping
    public CommonResult<LegalRegulation> getById(@RequestParam("id") Long id) {
        LegalRegulation legalRegulation = mongoTemplate.findById(id, LegalRegulation.class);
        if (ObjectUtil.isNull(legalRegulation)) {
            throw new RuntimeException("法规依据不存在");
        }
        return CommonResult.successData(legalRegulation);
    }

    /**
     * 更新法规依据
     *
     * @param legalRegulation 法规依据
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@Valid @RequestBody LegalRegulation legalRegulation) {
        Long id = legalRegulation.getId();
        if (ObjectUtil.isNull(id)) {
            throw new RuntimeException("id不能为空");
        }
        LegalRegulation existingRegulation = mongoTemplate.findById(id, LegalRegulation.class);
        if (ObjectUtil.isNull(existingRegulation)) {
            throw new RuntimeException("法规依据不存在");
        }
        BeanUtil.copyProperties(legalRegulation, existingRegulation, CopyOptions.create().ignoreNullValue());
        existingRegulation.setUpdateTime(new Date());
        mongoTemplate.save(existingRegulation);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 批量删除法规依据
     *
     * @param ids 法规依据ID列表
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Void> batchDelete(@RequestBody List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new RuntimeException("id列表不能为空");
        }

        Query query = Query.query(Criteria.where("id").in(ids));
        mongoTemplate.remove(query, LegalRegulation.class);
        return CommonResult.successResult("删除成功");
    }

    /**
     * 更新法规依据列表排序
     *
     * @param sort 列表排序
     * @return 更新结果
     */
    @PostMapping("/update/sort")
    public CommonResult<Void> updateSort(@RequestBody List<SortVO> sort) {
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, LegalRegulation.class);
        for (SortVO sortVO : sort) {
            Update update = Update.update("sort", sortVO.getSort())
                    .set("updateTime", new Date());
            operations.updateOne(Query.query(Criteria.where("id").is(sortVO.getId())), update);
        }
        operations.execute();
        return CommonResult.successResult("操作成功");
    }



    /**
     * 智能搜索法规依据
     *
     * @param regulationName 法规名称（可选）
     * @param content        条款内容（可选）
     * @return 法规依据列表
     */
    @GetMapping("/smartSearch")
    public CommonResult<List<LegalRegulation>> smartSearch(
            @RequestParam(required = false) String regulationName,
            @RequestParam(required = false) String content) {

        List<LegalRegulation> regulations;

        if (StrUtil.isBlank(regulationName) && StrUtil.isBlank(content)) {
            // 两个参数都为空时返回所有法规依据列表
            regulations = getAllEnabledRegulations();
        } else if (StrUtil.isNotBlank(regulationName) && StrUtil.isBlank(content)) {
            // 仅传入regulationName参数时：直接在LegalRegulation表中模糊搜索
            regulations = searchRegulationsByName(regulationName);
        } else if (StrUtil.isBlank(regulationName) && StrUtil.isNotBlank(content)) {
            // 仅传入content参数时：在LegalClause表中搜索内容，然后查询对应的法规依据
            regulations = searchRegulationsByContent(content);
        } else {
            // 同时传入两个参数时：先按内容搜索，再在结果中过滤法规名称
            regulations = searchRegulationsByContentAndName(content, regulationName);
        }

        return CommonResult.successData(regulations);
    }

    /**
     * 获取所有启用状态的法规依据
     */
    private List<LegalRegulation> getAllEnabledRegulations() {
        Query query = Query.query(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()))
                .with(Sort.by(Sort.Direction.DESC, "sort"));
        return mongoTemplate.find(query, LegalRegulation.class);
    }

    /**
     * 根据法规名称搜索法规依据
     */
    private List<LegalRegulation> searchRegulationsByName(String regulationName) {
        Query query = Query.query(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()))
                .addCriteria(Criteria.where("regulationName").regex(".*" + regulationName + ".*", "i"))
                .with(Sort.by(Sort.Direction.DESC, "sort"));
        return mongoTemplate.find(query, LegalRegulation.class);
    }

    /**
     * 根据条款内容搜索法规依据
     */
    private List<LegalRegulation> searchRegulationsByContent(String content) {
        // 在LegalClause表中搜索内容
        Query clauseQuery = Query.query(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()));
        Criteria contentCriteria = new Criteria();
        contentCriteria.orOperator(
                Criteria.where("clauseContent").regex(".*" + content + ".*", "i"),
                Criteria.where("subClauseContent").regex(".*" + content + ".*", "i")
        );
        clauseQuery.addCriteria(contentCriteria);

        List<LegalClause> clauses = mongoTemplate.find(clauseQuery, LegalClause.class);

        // 获取匹配条款的regulationId列表（去重）
        List<Long> regulationIds = clauses.stream()
                .map(LegalClause::getRegulationId)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        if (ObjectUtil.isEmpty(regulationIds)) {
            return new ArrayList<>();
        }

        // 根据regulationId查询对应的LegalRegulation数据
        Query regulationQuery = Query.query(Criteria.where("id").in(regulationIds))
                .addCriteria(Criteria.where("status").is(GeneralStatusEnum.ON.getCode()))
                .with(Sort.by(Sort.Direction.DESC, "sort"));
        return mongoTemplate.find(regulationQuery, LegalRegulation.class);
    }

    /**
     * 根据条款内容和法规名称搜索法规依据
     */
    private List<LegalRegulation> searchRegulationsByContentAndName(String content, String regulationName) {
        // 先根据内容搜索获取法规依据列表
        List<LegalRegulation> contentMatchedRegulations = searchRegulationsByContent(content);

        // 在结果集中再次过滤法规名称
        return contentMatchedRegulations.stream()
                .filter(regulation -> StrUtil.containsIgnoreCase(regulation.getRegulationName(), regulationName))
                .collect(Collectors.toList());
    }
}
