package com.yixun.wid.controller.admin;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.api.MessageApi;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.bean.in.MessageUpdateIn;
import com.yixun.wid.bean.in.*;
import com.yixun.wid.bean.other.MessageDefine;
import com.yixun.wid.bean.other.TaskType;
import com.yixun.wid.bean.out.DeclarationLogOut;
import com.yixun.wid.bean.out.DeclarationOut;
import com.yixun.wid.entity.*;
import com.yixun.wid.entity.em.CasesStatus;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.entity.em.DeclarationSubStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.*;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "admin工伤申报")
@RestController
@RequestMapping(value = "/admin/declaration")
public class AdminDeclarationController {

    @Resource
    private DeclarationService declarationService;

    @Resource
    private CasesService casesService;

    @Resource
    private DeliveryFileService deliveryFileService;

    @Resource
    private MessageService messageService;

    @Resource
    private ShortMsgService shortMsgService;

    @Resource
    private MessageRecordService messageRecordService;

    @Resource
    private MessageApi messageApi;

	@Resource(name="customRedisTemplate")
	private RedisTemplate redisTemplate;

	@Resource
	private ReceiveFileService receiveFileService;

//    @PostMapping("/test")
//    @ApiOperation("test")
//    public CommonResult<String> test(@RequestBody MessageUpdateIn messageUpdateIn) {
//        messageApi.setFinished(messageUpdateIn);
//        return CommonResult.successResult("1111");
//    }

    void sendMessage(String title, Declaration declaration) {
        Integer type = 1;
        String redirectUrl = "/pages/progress/declarationDetail/declarationDetail?id=" + declaration.getId();
        MessageDefine messageDefine = new MessageDefine();
        BeanUtils.copyProperties(declaration, messageDefine);
        try {
            messageService.sendAppMessage(declaration.getSubmitUserId(), title, null, type, redirectUrl, null,
                    new ObjectMapper().writeValueAsString(messageDefine));
        } catch (Exception e) {
            log.error("json转换出错，{}", e);
        }
    }

    void setFinish(Declaration declaration, String taskType) {
        MessageDefine messageDefine = new MessageDefine();
        BeanUtils.copyProperties(declaration, messageDefine);
        MessageUpdateIn messageUpdateIn = new MessageUpdateIn();
        messageUpdateIn.setUserId(declaration.getSubmitUserId() + "");
        messageUpdateIn.setType(2);
        try {
            messageUpdateIn.setBusinessParam(new ObjectMapper().writeValueAsString(messageDefine));
        } catch (JsonProcessingException e) {
            log.error("json转换出错，{}", e);
        }
        messageUpdateIn.setBusinessType(taskType);
        messageService.setFinished(messageUpdateIn);
    }

    @GetMapping("/getList")
    @ApiOperation("获取申报列表")
    public CommonResult<List<DeclarationOut>> getList(DeclarationGetIn declarationGetIn, CommonPage commonPage) {

        List<Declaration> declarationList = declarationService.getDeclarationList(declarationGetIn, commonPage);
        List<DeclarationOut> outList = BeanUtils.copyToOutList(declarationList, DeclarationOut.class);
        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{declarationId}")
    @ApiOperation("获取申报详情")
    public CommonResult<DeclarationOut> getDetail(@PathVariable("declarationId") Long declarationId) {

        Declaration declaration = declarationService.getById(declarationId);
        if (declaration == null) {
            throw new DataErrorException("该申报信息不存在");
        }
		if (ObjectUtil.isNull(declaration.getIsAdminInvolved()) || !declaration.getIsAdminInvolved()) {
			declaration.setIsAdminInvolved(true);
			declarationService.update(declaration);
		}
		DeclarationOut out = new DeclarationOut();
        BeanUtils.copyProperties(declaration, out);

        return CommonResult.successData(out);
    }

    @GetMapping("/getLogList")
    @ApiOperation("获取申报操作日志")
    public CommonResult<List<DeclarationLogOut>> getLogList(Long declarationId, CommonPage commonPage) {

        List<DeclarationLog> casesList = declarationService.getDeclarationLogList(declarationId, commonPage);
        List<DeclarationLogOut> outList = BeanUtils.copyToOutList(casesList, DeclarationLogOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @PostMapping("/save")
    @ApiOperation("新建申报信息")
    public CommonResult<DeclarationOut> save(@RequestBody DeclarationIn declarationIn) {

        Declaration declaration = new Declaration();

        Cases cases = null;
        if (declarationIn.getCasesId() != null) {
            cases = casesService.getById(declarationIn.getCasesId());
            if (cases == null) {
                throw new DataErrorException("报案信息不存在");
            }
            if (cases.getStatus().equals(CasesStatus.Applying.name())) {
                throw new DataErrorException("该报案已在申报中");
            } else if (cases.getStatus().equals(CasesStatus.Applied.name())) {
                throw new DataErrorException("该报案已经申报");
            } else if (cases.getStatus().equals(CasesStatus.Cancelled.name())) {
                throw new DataErrorException("该报案已经撤销");
            }
            BeanUtils.copyProperties(cases, declaration);
        } else {
	        String insuranceAddress = declaration.getInsuranceAddress();
	        if (StrUtil.isNotBlank(insuranceAddress)) {
		        declaration.setCaseSn(SnGeneratorUtil.getSiChuanCaseSnV2(redisTemplate, "工", insuranceAddress));
//            declaration.setCaseSn(casesService.getCaseSn());
	        }
        }

        Long id = SnGeneratorUtil.getId();
        declaration.setId(id);
        declaration.setCreateTime(new Date());
        if (declarationIn.getIsToAccepting() != null && declarationIn.getIsToAccepting()) {
            declaration.setStatus(DeclarationStatus.Accepting.name());
        } else {
            declaration.setStatus(DeclarationStatus.Applying.name());
        }
        declaration.setIsLocked(false);
        declaration.setIsAdminInvolved(true);
        BeanUtils.copyProperties(declarationIn, declaration);

        declarationService.save(declaration);

        if (cases != null) {
            if (declarationIn.getIsToAccepting() != null && declarationIn.getIsToAccepting()) {
                cases.setStatus(CasesStatus.Applied.name());
            } else {
                cases.setStatus(CasesStatus.Applying.name());
            }
            casesService.update(cases);
        }
        DeclarationOut out = new DeclarationOut();
        BeanUtils.copyProperties(declaration, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/update/{declarationId}")
    @ApiOperation("更新申报信息")
    public CommonResult<Void> update(@PathVariable("declarationId") Long declarationId,
                                     @RequestBody DeclarationIn declarationIn) {

        Declaration declaration = declarationService.getById(declarationId);
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }
        BeanUtils.copyProperties(declarationIn, declaration, true);

        if (declarationIn.getIsToAccepting() != null) {
            if (declarationIn.getIsToAccepting()) {
                declaration.setStatus(DeclarationStatus.Accepting.name());
            } else {
                declaration.setStatus(DeclarationStatus.Applying.name());
            }
        }

	    String caseSn = declaration.getCaseSn();
		if (StrUtil.isBlank(caseSn)) {
			declaration.setCaseSn(SnGeneratorUtil.getSiChuanCaseSnV2(redisTemplate, "工", declaration.getInsuranceAddress()));
		}

        // bugfix: 案件的申报信息的申请时间应记录提交的时间（即申报中的案件点击提交后），且申请时间可以手动修改
        String status = declaration.getStatus();
        Date submitTime = declaration.getSubmitTime();
        if (ObjectUtil.isNull(submitTime) && StrUtil.isNotBlank(status) && status.equals(DeclarationStatus.Accepting.name())) {
            declaration.setSubmitTime(new Date());
        }

        declarationService.update(declaration);

        if (declarationIn.getCasesId() != null) {
            Cases cases = casesService.getById(declarationIn.getCasesId());
            if (cases == null) {
                throw new DataErrorException("报案信息不存在");
            }
            if (declarationIn.getIsToAccepting() != null && declarationIn.getIsToAccepting()) {
                cases.setStatus(CasesStatus.Applied.name());
            } else {
                cases.setStatus(CasesStatus.Applying.name());
            }
            casesService.update(cases);
        }

        declarationService.setDeclarationLog(declaration.getId(), "历史修改");


        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/updateMaterials/{declarationId}")
    @ApiOperation("更新申报材料")
    public CommonResult<Long> updateMaterials(@PathVariable("declarationId") Long declarationId, @RequestBody DeclarationIn materialsIn) {

        Declaration declaration = declarationService.getById(declarationId);
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }
        BeanUtils.copyProperties(materialsIn, declaration, true);
        if (declaration.getStatus().equals(DeclarationStatus.Done.name())
                && !DeclarationSubStatus.FurtherInfo.name().equals(declaration.getSubStatus())
                && !DeclarationSubStatus.WaitMaterialSubmit.name().equals(declaration.getSubStatus())) {
            throw new DataErrorException("该申报已办结，不可提交");
        }

        declarationService.update(declaration);

        declarationService.setDeclarationLog(declaration.getId(), "历史上传");

        return CommonResult.successData(declaration.getId());
    }

    @PostMapping("/setMailSn")
    @ApiOperation("设置通知邮件运单号")
    public CommonResult<Void> setMailSn(@RequestBody MailSnIn mailSnIn) {

        Declaration declaration = declarationService.getById(mailSnIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        declaration.setInformReceiveMailSn(mailSnIn.getInformReceiveMailSn());

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/setCancel")
    @ApiOperation("审核撤销申报")
    public CommonResult<Void> setCancel(@RequestBody CancelIn cancelIn) {

        Declaration declaration = declarationService.getById(cancelIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        String step = "";
        if (declaration.getStatus().equals(DeclarationStatus.Accepting.name())) {
            step = "受理";
        } else if (declaration.getStatus().equals(DeclarationStatus.Identifying.name())) {
            step = "认定";
        }

        if (cancelIn.getIsCancelled()) {
            declaration.setStatus(DeclarationStatus.Cancelled.name());
            declaration.setCanceledTime(new Date());
            declaration.setSubStatus(null);
            declaration.setIsLocked(true);

            CancelConclusion cancelConclusion = new CancelConclusion();
            BeanUtils.copyProperties(cancelIn, cancelConclusion);
            declaration.setCancelConclusion(cancelConclusion);

            if (declaration.getCasesId() != null) {
                Cases cases = casesService.getById(declaration.getCasesId());
                if (cases != null) {
                    cases.setStatus(CasesStatus.Cancelled.name());
                    casesService.update(cases);
                }
            }

            deliveryFileService.handleDeliveryFile(declaration, step, "撤销决定通知书",
                    cancelIn.getWritSn());
        } else {
            declaration.setStatus(declaration.getStatusBeforeCancel());
            declaration.setStatusBeforeCancel(null);
            declaration.setSubStatus(declaration.getSubStatusBeforeCancel());
            declaration.setSubStatusBeforeCancel(null);
            declaration.setReason(cancelIn.getReason());
            declaration.setCanceledTime(null);
            //发消息
            String title = declaration.getName() + "的工伤认定申请未撤销成功！";
            this.sendMessage(title, declaration);
            messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "应用内", title, "受理".equals(step) ? 2 : 3, declaration.getId() + "");
        }

        declaration.setRemark(cancelIn.getRemark());

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/setAccept")
    @ApiOperation("设置受理结论")
    public CommonResult<Void> setAccept(@RequestBody AcceptIn acceptIn) {

        Declaration declaration = declarationService.getById(acceptIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        AcceptConclusion acceptConclusion = new AcceptConclusion();
        BeanUtils.copyProperties(acceptIn, acceptConclusion);
	    acceptConclusion.setAcceptDateTime(DateUtil.now());
        declaration.setAcceptConclusion(acceptConclusion);

        if (acceptIn.getIsSubmitWayOpen() != null) {
            declaration.setIsSubmitWayOpen(acceptIn.getIsSubmitWayOpen());
            if (acceptIn.getIsSubmitWayOpen()) {
                //发送待办
                String title = declaration.getName() + "的工伤认定申请可选择材料递交方式！";
                messageService.sendAppTask(TaskType.OPEN_MATERIAL, title, declaration);
                String message = "申请材料提交方式有更新，请尽快前往选择";
                messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "认定申请", "PC开放递送方式",
                        message);
                messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "微信服务通知", message, 2, declaration.getId() + "");
            }
        }

        if (acceptIn.getIsSubmit()) {
            if (acceptIn.getConclusion().equals("受理")) {
                declaration.setStatus(DeclarationStatus.Identifying.name());
                declaration.setSubStatus(null);

				// bugfix: 受理后不锁定申报记录
				// declaration.setIsLocked(true);

                deliveryFileService.handleDeliveryFile(declaration, "受理", "受理决定通知书",
                        acceptIn.getWritSn());
                //发送订阅消息
                String message = null;
                if ("现场".equals(declaration.getMaterialSubmitWay())) {
                    message = "请申报人提前准备申请材料，根据通知前往业务窗口递送并领取通知书！";
                } else {
                    message = "请申报人及时寄送申请材料，否则影响通知书送达！";
                }
                messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "已受理", "工伤认定申请",
                        message);
                messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "微信服务通知", message, 2, declaration.getId() + "");
            } else {
                declaration.setStatus(DeclarationStatus.Done.name());

				// bugfix: 2025/06/16 PC出具的结论为不予受理时，未登记收件，进度详情中无状态图标及说明性文字
	            ReceiveFile material = receiveFileService.getByDeclaration(declaration.getId(), "material");
				if (ObjectUtil.isNull(material) || (ObjectUtil.isNotNull(material.getIsConfirmed()) && material.getIsConfirmed())) {
					declaration.setSubStatus(DeclarationSubStatus.NotAccept.name());
				} else {
					declaration.setSubStatus(DeclarationSubStatus.WaitMaterialSubmit.name());
				}

                deliveryFileService.handleDeliveryFile(declaration, "受理", "不予受理决定通知书",
                        acceptIn.getWritSn());

            }
        }

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/setSuspended")
    @ApiOperation("认定中止")
    public CommonResult<Void> setSuspended(@RequestBody SuspendedIn suspendedIn) {

        Declaration declaration = declarationService.getById(suspendedIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        SuspendedConclusion suspendedConclusion = new SuspendedConclusion();
        BeanUtils.copyProperties(suspendedIn, suspendedConclusion);
        declaration.setSuspendedConclusion(suspendedConclusion);

        if (suspendedIn.getIsSubmit()) {
            if (suspendedIn.getIsComplete()) {
                declaration.setSubStatus(null);
                declaration.setReason(null);
                declaration.setRemark(null);
                declaration.setSuspendedConclusion(null);
                //说明资料已提交完整，将待办设为已读
                // 设置待办为已完成
                this.setFinish(declaration, TaskType.DEEMED_TERMINATION);
            } else {
                declaration.setSubStatus(DeclarationSubStatus.Suspended.name());
                declaration.setReason(suspendedIn.getReason());
                declaration.setRemark(suspendedIn.getRemark());
                //发送待办
                String title = declaration.getName() + "的工伤认定申请已进入认定中止状态，请及时补充相关文书材料！";
                messageService.sendAppTask(TaskType.DEEMED_TERMINATION, title, declaration);
                //发送订阅消息
                String message = "待缺失文书齐备后，请及时上传并准备申请材料递送";
                messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "认定申请", "进入待实物提交",
                        message);
                messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "微信服务通知", message, 3, declaration.getId() + "");
            }
        }

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/setReject")
    @ApiOperation("退回申报")
    public CommonResult<Void> setReject(@RequestBody RejectIn rejectIn) {

        Declaration declaration = declarationService.getById(rejectIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        if (declaration.getStatus().equals(DeclarationStatus.Canceling.name())) {
            throw new DataErrorException("该案件已提起撤销!");
        }
        if (declaration.getStatus().equals(DeclarationStatus.Cancelled.name())) {
            throw new DataErrorException("该案件已撤销!");
        }
        declaration.setStatus(DeclarationStatus.Rejected.name());
        declaration.setReason(rejectIn.getReason());
        declaration.setRemark(rejectIn.getRemark());

        RejectConclusion rejectConclusion = new RejectConclusion();
        BeanUtils.copyProperties(rejectIn, rejectConclusion);
        declaration.setRejectConclusion(rejectConclusion);

        declarationService.update(declaration);
        //发消息
        String title = declaration.getName() + "工伤认定申请被退回！";
        this.sendMessage(title, declaration);
        messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "应用内", title, 2, declaration.getId() + "");
        //发送订阅消息
        String message = "本次工伤认定申请已被退回，点击进入详情查看原因，如有疑问，可拨打业务办理窗口电话咨询";
        messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "退回申请", "工伤认定申请",
                message);
        messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "微信服务通知", message, 2, declaration.getId() + "");
        return CommonResult.successResult("退回成功");
    }

    @PostMapping("/setIdentify")
    @ApiOperation("设置认定结论")
    public CommonResult<Void> setIdentify(@RequestBody IdentifyIn identifyIn) {

        Declaration declaration = declarationService.getById(identifyIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        IdentifyConclusion identifyConclusion = new IdentifyConclusion();
        BeanUtils.copyProperties(identifyIn, identifyConclusion);
        declaration.setIdentifyConclusion(identifyConclusion);

        if (identifyIn.getIsSubmitWayOpen() != null) {
            declaration.setIsSubmitWayOpen(identifyIn.getIsSubmitWayOpen());
        }

        if (identifyIn.getIsSubmit()) {
            if (identifyIn.getConclusion().equals("不予认定")) {
                declaration.setStatus(DeclarationStatus.Done.name());

	            // bugfix: 2025/06/16 PC出具的结论为不予受理时，未登记收件，进度详情中无状态图标及说明性文字
	            ReceiveFile material = receiveFileService.getByDeclaration(declaration.getId(), "material");
	            if (ObjectUtil.isNull(material) || (ObjectUtil.isNotNull(material.getIsConfirmed()) && material.getIsConfirmed())) {
		            declaration.setSubStatus(DeclarationSubStatus.NotIdentify.name());
	            } else {
		            declaration.setSubStatus(DeclarationSubStatus.WaitMaterialSubmit.name());
	            }

//                declaration.setSubStatus(DeclarationSubStatus.NotIdentify.name());
                declaration.setReason(identifyIn.getReason());

                deliveryFileService.handleDeliveryFile(declaration, "认定", "不予认定决定通知书",
                        identifyIn.getWritSn());
            } else {
                //declaration.setStatus(DeclarationStatus.Classifying.name());
                //2024-05-31 认定后改为办结
                declaration.setStatus(DeclarationStatus.Done.name());

	            // bugfix: 2025/06/16 PC出具的结论为不予受理时，未登记收件，进度详情中无状态图标及说明性文字
	            ReceiveFile material = receiveFileService.getByDeclaration(declaration.getId(), "material");
	            if (ObjectUtil.isNull(material) || (ObjectUtil.isNotNull(material.getIsConfirmed()) && material.getIsConfirmed())) {
//		            declaration.setSubStatus(DeclarationSubStatus.NotIdentify.name());
		            declaration.setSubStatus(null);
	            } else {
		            declaration.setSubStatus(DeclarationSubStatus.WaitMaterialSubmit.name());
	            }

//                declaration.setSubStatus(null);

                deliveryFileService.handleDeliveryFile(declaration, "认定", "认定决定通知书",
                        identifyIn.getWritSn());
                if ("自领".equals(declaration.getInformReceiveWay())) {
                    //办结发待办
                    String title = declaration.getName() + "的工伤认定申请已办结，请及时前往业务办理窗口提交材料并领取通知书！";
                    messageService.sendAppTask(TaskType.LIVE_SIGN, title, declaration);
                }
                //发送订阅消息
                String message = null;
                if ("现场".equals(declaration.getMaterialSubmitWay())) {
                    message = "请申报人提前准备申请材料，根据通知前往业务窗口递送并领取通知书！";
                } else {
                    message = "请申报人及时寄送申请材料，否则影响通知书送达！";
                }
                messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "已办结", "工伤认定申请",
                        message);
                messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "微信服务通知", message, 3, declaration.getId() + "");
            }
            //发消息
            String title = declaration.getName() + "工伤认定申请已办结！";
            this.sendMessage(title, declaration);
            messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "应用内", title, 3, declaration.getId() + "");
            //发送短信
            Map<String, String> paramMap = new HashMap<>();
            if (StringUtils.hasLength(declaration.getApplicantPhone())) {
                paramMap.put("phone", declaration.getApplicantPhone());
                paramMap.put("name", declaration.getName());
                shortMsgService.sendSMS("SMS_470570107", paramMap);
                String smsContent = declaration.getName() + "的工伤认定申请已出具认定结论，详情请通过微信小程序进行查看。";
                messageRecordService.saveTrueRecord(declaration.getSubmitUserId(), declaration.getRelationship(), "短信", smsContent, 3, declaration.getId() + "");
            }
        }

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/setClassify")
    @ApiOperation("鉴定评级")
    public CommonResult<Void> setClassify(@RequestBody ClassifyIn classifyIn) {

        Declaration declaration = declarationService.getById(classifyIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        declaration.setInjureType(classifyIn.getInjureType());
        declaration.setInjureExpression(classifyIn.getInjureExpression());
        declaration.setInjuredPart(classifyIn.getInjuredPart());
        declaration.setInjureLevel(classifyIn.getInjureLevel());

        declaration.setStatus(DeclarationStatus.Done.name());
        declaration.setSubStatus(null);

        declarationService.update(declaration);

        return CommonResult.successResult("操作成功");
    }

}
