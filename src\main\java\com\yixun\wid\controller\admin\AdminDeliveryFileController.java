package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.DeliveryFileGetIn;
import com.yixun.wid.bean.in.DeliveryFileIn;
import com.yixun.wid.bean.other.TaskType;
import com.yixun.wid.bean.out.DeliveryFileOut;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.DeliveryFile;
import com.yixun.wid.entity.MailFileItem;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.service.DeliveryFileService;
import com.yixun.wid.service.MessageService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin送达列表")
@RestController
@RequestMapping(value = "/admin/deliveryFile")
public class AdminDeliveryFileController {

    @Resource
    private DeclarationService declarationService;

    @Resource
    private DeliveryFileService deliveryFileService;

    @Resource
    private MessageService messageService;

    @GetMapping("/getList")
    @ApiOperation("获取送达列表")
    public CommonResult<List<DeliveryFileOut>> getList(DeliveryFileGetIn deliveryFileGetIn, CommonPage commonPage) {

        List<DeliveryFile> deliveryFileList = deliveryFileService.getList(deliveryFileGetIn, commonPage);
        List<DeliveryFileOut> outList = BeanUtils.copyToOutList(deliveryFileList, DeliveryFileOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{declarationId}")
    @ApiOperation("按申报id获取送达详情")
    public CommonResult<DeliveryFileOut> getDetail(@PathVariable("declarationId") Long declarationId) {

        DeliveryFile deliveryFile = deliveryFileService.getByDeclaration(declarationId);
        DeliveryFileOut out = new DeliveryFileOut();
        BeanUtils.copyProperties(deliveryFile, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/doSave")
    @ApiOperation("保存送达详情")
    public CommonResult<Void> doSave(@RequestBody DeliveryFileIn deliveryFileIn) {

        Declaration declaration = declarationService.getById(deliveryFileIn.getDeclarationId());
        if (declaration == null) {
            throw new DataErrorException("申报信息不存在");
        }

        DeliveryFile deliveryFile = deliveryFileService.getByDeclaration(deliveryFileIn.getDeclarationId());
        if (deliveryFile == null) {
            throw new DataErrorException("送达详情不存在");
        }
        BeanUtils.copyProperties(deliveryFileIn, deliveryFile, true);

        if (deliveryFileIn.getIsConfirmed() != null && deliveryFileIn.getIsConfirmed()) {
            deliveryFile.setConfirmedTime(new Date());
            //提交的时候发送待办
            if ("邮寄".equals(deliveryFileIn.getType())) {
                List<MailFileItem> mailList = deliveryFileIn.getMailList();
                if (!CollectionUtils.isEmpty(mailList)) {
                    String title = declaration.getName() + "的工伤认定申请通知书已寄送，请注意签收！";
                    messageService.sendAppTask(TaskType.POST_SIGN, title, declaration);
                    String mailSnString = mailList.stream()
                            .map(MailFileItem::getMailSn)
                            .filter(mailSn -> StringUtils.hasLength(mailSn))
                            .collect(Collectors.joining(","));
                    //发送订阅消息
                    String message = "通知书已寄出，运单号：" + mailSnString + "，请注意查收！";
                    messageService.sendTrueWxMessage(declaration.getSubmitUserId(), "通知书寄出PC录入运单号", "工伤认定申请",
                            message);
                }
            }
        }
        deliveryFileService.update(deliveryFile);
        return CommonResult.successResult("保存成功");
    }

}
