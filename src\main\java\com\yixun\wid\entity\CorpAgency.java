package com.yixun.wid.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "代理企业")
@Data
public class CorpAgency {

    private Long id;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty(value = "代办公司id")
    private Long agencyCorpId;

    @ApiModelProperty(value = "代办公司名称")
    private String agencyCorpName;

    @ApiModelProperty(value = "目标公司id")
    private Long corporationId;

    @ApiModelProperty(value = "目标公司名称")
    private String corpName;

    @ApiModelProperty(value = "目标公司信用代码")
    private String creditCode;

    @ApiModelProperty(value = "认证状态")
    private String agencyStatus;

    @ApiModelProperty(value = "失败原因")
    private String reason;
}
