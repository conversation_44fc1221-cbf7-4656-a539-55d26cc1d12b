package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.CorpAgencyGetIn;
import com.yixun.wid.entity.CorpAgency;

import java.util.List;

public interface CorpAgencyService {

    List<CorpAgency> getByAgencyCorpId(Long agencyCorpId);

    void save(CorpAgency corpAgency);

    void update(CorpAgency corpAgency);

    CorpAgency getById(Long corpAgencyId);

    List<CorpAgency> getListByCorpName(String corpName);

    CorpAgency getByCorporation(Long corporationId);

    List<CorpAgency> getPage(CorpAgencyGetIn corpAgencyGetIn, CommonPage commonPage);

    List<CorpAgency> getList(Long agencyCorpId);
}
