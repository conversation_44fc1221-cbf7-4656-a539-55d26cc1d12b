package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ThirdPartyInfo {

    @ApiModelProperty("接收通报时间")
    private String acceptTime;

    @ApiModelProperty("到达现场时间")
    private String reachSceneTime;

    @ApiModelProperty("调查到达时间")
    private String investigateReachTime;

    @ApiModelProperty("调查结束时间")
    private String investigateEndTime;

    @ApiModelProperty("协助调查事项")
    private String investigateAssistItems;

    @ApiModelProperty("单位名称")
    private String companyName;

    @ApiModelProperty("单位地址")
    private String companyAddress;

    @ApiModelProperty("核查人员")
    private String responsiblePerson;

    @ApiModelProperty("核查过程简述")
    private String investigationBrief;
}
