package com.yixun.wid.v2.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.v2.bean.in.BillingInfoBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.entity.BillingInfo;

import java.util.List;

/**
 * 账单信息业务逻辑接口
 */
public interface BillingInfoService {
    
    /**
     * 分页查询账单信息
     *
     * @param medicalCasesId 关联的工伤待遇业务ID
     * @param hospital 医院名称（模糊查询）
     * @param treatmentType 治疗类型
     * @param commonPage 分页参数
     * @return 账单信息列表（分页）
     */
    List<BillingInfo> list(Long medicalCasesId, String hospital, String treatmentType, CommonPage commonPage);
    
    /**
     * 根据ID获取账单信息详情
     *
     * @param id 账单信息ID
     * @return 账单信息详情
     */
    BillingInfo getDetail(Long id);
    
    /**
     * 新增账单信息
     *
     * @param billingInfoIn 账单信息输入参数
     * @return 新增后的账单信息
     */
    BillingInfo add(BillingInfoIn billingInfoIn);
    
    /**
     * 更新账单信息
     *
     * @param billingInfo 账单信息
     * @return 更新后的账单信息
     */
    BillingInfo update(BillingInfo billingInfo);
    
    /**
     * 批量删除账单信息
     *
     * @param batchDeleteIn 批量删除参数
     * @return 删除的记录数
     */
    Long batchDelete(BillingInfoBatchDeleteIn batchDeleteIn);
    
    /**
     * 根据工伤待遇业务ID查询关联的账单信息列表
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @return 账单信息列表
     */
    List<BillingInfo> listByMedicalCases(Long medicalCasesId);
} 