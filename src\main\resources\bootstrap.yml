server:
  servlet:
    context-path: /work-injury-declare-admin-api
spring:
  jackson:
    time-zone: Asia/Shanghai
  profiles:
    active: ${profile.env}
  application:
    name: work-injury-declare-admin-api
  cloud:
    nacos:
      config:
        # 已经创建好的命名空间，会有一个id
        namespace: longquan-prj

mybatis-plus:
  global-config:
    banner: false
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0

filePath: /data/file/

message:
  smsSign: 成都易训企业管理

aliyun:
  expired: 3600
  access-key-id: LTAI5tG6iHn9PH7wFT5fuGKb
  access-key-secret: ******************************
  role-arn: acs:ram::1841673459408468:role/ramossrolenew
  policy-user: |
    {
      "Version": "1",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": "oss:PutObject",
          "Resource": "acs:oss:*:*:*/*"
        },
        {
          "Effect": "Deny",
          "Action": "oss:PutObject",
          "Resource": [
            "acs:oss:*:*:*/*.html",
            "acs:oss:*:*:*/*.HTML",
            "acs:oss:*:*:*/*.Html",
            "acs:oss:*:*:*/*.HTM",
            "acs:oss:*:*:*/*.htm",
            "acs:oss:*:*:*/*.Exe",
            "acs:oss:*:*:*/*.EXE",
            "acs:oss:*:*:*/*.exe",
            "acs:oss:*:*:*/*.bat",
            "acs:oss:*:*:*/*.BAT"
          ]
        }
      ]
    }

api:
  ocrApi: "https://u507501-b1c0-********.nmb1.seetacloud.com:8443/Diagnosis"
  generalOcrApi: "http://192.168.252.62:9202/ocr"
  aiCorpSimilarity: http://192.168.252.61:9216

jwt:
  secret:
    account: ********************************************************
  expiration: 604800    #单位秒，值为7天‬

weixin:
  page: 1
