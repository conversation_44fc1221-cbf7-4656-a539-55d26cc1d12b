package com.yixun.wid.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.aop.RequiredPermission;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.RoleEditIn;
import com.yixun.wid.bean.in.RoleIn;
import com.yixun.wid.bean.in.RolePermissionIn;
import com.yixun.wid.bean.out.RoleOut;
import com.yixun.wid.entity.Permission;
import com.yixun.wid.entity.Role;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.RolePermissionService;
import com.yixun.wid.utils.BeanFieldCheckingUtils;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "admin角色权限配置")
@RestController
@RequestMapping("/admin/rolePermission")
public class AdminRolePermissionController {

	@Resource
	private RolePermissionService rolePermissionService;

	@RequiredPermission("save:initPermission:rolePermission")
	@ApiOperation(value = "初始化权限")
	@PostMapping(value = "/initPermission")
	public CommonResult<Void> initPermission() {

		List<Permission> permissionList = rolePermissionService.loadPermissions();
		rolePermissionService.initPermission(permissionList);

		return CommonResult.successResult("初始化成功");
	}

	@RequiredPermission("get:getAllRoleList:rolePermission")
	@ApiOperation(value = "获取所有角色列表")
	@GetMapping(value = "/roleList")
	public CommonResult<List<RoleOut>> getAllRoleList(String label, CommonPage page) {

		Page<Role> allRoleList = rolePermissionService.getAllRoleList(label, page);
		Page<RoleOut> outList = BeanUtils.copyToOutListPage(allRoleList, RoleOut.class);

		return CommonResult.successData(outList);
	}

	@RequiredPermission("save:doSaveRole:rolePermission")
	@ApiOperation(value = "新增角色")
	@PostMapping(value = "/doSaveRole")
	public CommonResult<Void> doSaveRole(@RequestBody RoleIn roleIn) {

		//检查参数
		if (!BeanFieldCheckingUtils.forAllFieldNotNull(roleIn)) {
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		// 检查name字段是否重复
		Role existRole = rolePermissionService.getRoleByName(roleIn.getName());
		if (existRole != null) {
			throw new DataErrorException("角色标签已存在");
		}

		Role role = new Role();
		role.setId(SnGeneratorUtil.getId());
		BeanUtils.copyProperties(roleIn, role);

		rolePermissionService.insertRole(role);

		return CommonResult.successResult("保存成功");
	}

	@RequiredPermission("update:doEditRole:rolePermission")
	@ApiOperation(value = "修改角色")
	@PostMapping(value = "/doEditRole/{roleId}")
	public CommonResult doEditRole(@PathVariable("roleId") Long roleId, @RequestBody RoleEditIn roleEditIn) {

		//检查参数
		if (!BeanFieldCheckingUtils.forAnyFieldNotNull(roleEditIn)) {
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		Role role = rolePermissionService.getRoleById(roleId);
		if (role == null) {
			throw new DataErrorException("该角色不存在");
		}

		role.setLabel(roleEditIn.getLabel());

		rolePermissionService.updateRole(role);

		return CommonResult.successResult("修改成功");
	}

	@RequiredPermission("delete:doDeleteRole:rolePermission")
	@ApiOperation(value = "删除角色")
	@PostMapping(value = "/doDeleteRole/{roleId}")
	public CommonResult doDeleteRole(@PathVariable("roleId") Long roleId) {

		Role role = rolePermissionService.getRoleById(roleId);
		if (role == null) {
			throw new DataErrorException("该角色不存在");
		}
		List<Permission> permissionListByRole = rolePermissionService.getPermissionListByRole(role.getName());
		if (!permissionListByRole.isEmpty()) {
			throw new DataErrorException("该角色还有绑定的权限，不能删除");
		}

		rolePermissionService.deleteRole(roleId);

		return CommonResult.successResult("删除成功");
	}

	@RequiredPermission("save:setRolePermissions:rolePermission")
	@ApiOperation(value = "设置角色权限")
	@PostMapping(value = "/setRolePermissions")
	public CommonResult<Void> setRolePermissions(@RequestBody RolePermissionIn rolePermissionIn) {

		//检查参数
		if (!BeanFieldCheckingUtils.forAllFieldNotNull(rolePermissionIn)) {
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		Role role = rolePermissionService.getRoleByName(rolePermissionIn.getRole());
		if (role == null) {
			throw new DataErrorException("该角色不存在");
		}

		try {
			rolePermissionService.setRolePermissions(rolePermissionIn.getRole(), rolePermissionIn.getPermissionList());
		} catch (Exception e) {
			if (e.getMessage().contains("Duplicate entry")) {
				throw new DataErrorException("设置的权限有重复");
			} else {
				throw new DataErrorException(e.getMessage());
			}
		}

		return CommonResult.successResult("设置成功");
	}

	@RequiredPermission("get:getRolePermissions:rolePermission")
	@ApiOperation(value = "获取指定角色的权限列表")
	@GetMapping(value = "/rolePermissions")
	public CommonResult<List<Permission>> getRolePermissions(@RequestParam String role) {

		Role roleByName = rolePermissionService.getRoleByName(role);
		if (roleByName == null) {
			throw new DataErrorException("该角色不存在");
		}
		List<Permission> rolePermissions = rolePermissionService.getPermissionListByRole(role);

		return CommonResult.successData(rolePermissions);
	}

	@RequiredPermission("get:getAllPermissionList:rolePermission")
	@ApiOperation(value = "获取所有权限列表")
	@GetMapping(value = "/permissionList")
	public CommonResult<List<Permission>> getAllPermissionList() {

		List<Permission> rolePermissions = rolePermissionService.getAllPermissionList();

		return CommonResult.successData(rolePermissions);
	}

}
