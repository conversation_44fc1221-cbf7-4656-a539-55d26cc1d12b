工伤待遇审核系统

V1.0

需求说明文档

**文件修订记录**

  ---------- -------------- ----------------------------------- ------------
  **版本**   **修订日期**   **描述说明**                        **修订人**

                                                                

                                                                

                                                                
  ---------- -------------- ----------------------------------- ------------

# 引言

基于工伤待遇支付，医疗待遇审核扣费问题，现阶段均为线下手工扣费，人工结算，效率低下，准确率难以保证，现需利用数字化及智能化手段进行优化。

# 产品概述

## 产品定位

工伤待遇审核系统是一款专业用于工伤待遇核算的系统。它结合了材料识别、在线扣费、智能理算等功能，旨在提高工伤待遇费用核算的准确率与效率。

作为工伤业务系统，本系统拟于工伤申报系统集成，新系统名称"智慧工伤业务经办系统"

## 产品角色

+:--------------------+:-----------------------------------------------------+
| **角色**            | **主要操作**                                         |
+---------------------+------------------------------------------------------+
| 信息维护人员        | 运营配置、信息查询的编辑与配置                       |
+---------------------+------------------------------------------------------+
| 待遇受理            | - 工伤待遇/任务列表/业务受理中，新建、编辑、提交功能 |
|                     |                                                      |
|                     | - 作业数据查询                                       |
+---------------------+------------------------------------------------------+
| 待遇初审            | - 工伤待遇/任务列表/待接收任务，查询、获取任务       |
|                     |                                                      |
|                     | - 工伤待遇/任务列表/我的任务，查看、编辑             |
|                     |                                                      |
|                     | - 作业数据查询                                       |
+---------------------+------------------------------------------------------+
| 待遇复审            | - 工伤待遇/任务列表/待接收任务，查询、获取任务       |
|                     |                                                      |
|                     | - 工伤待遇/任务列表/我的任务，查看、编辑             |
|                     |                                                      |
|                     | - 作业数据查询                                       |
+---------------------+------------------------------------------------------+
| 待遇终审            | - 工伤待遇/任务列表/待接收任务，查询、获取任务       |
|                     |                                                      |
|                     | - 工伤待遇/任务列表/我的任务，查看、编辑             |
|                     |                                                      |
|                     | - 作业数据查询                                       |
+---------------------+------------------------------------------------------+

## 产品功能结构

无

## 产品主要流程

![](./media/image1.png){width="5.754166666666666in" height="6.175in"}

## 产品功能清单

+:-----+:---------+:-------------+:-----------------------------------------------+:-----------------------------------------------------+
| 序号 | 一级功能 | 二级功能     | 功能描述                                       | 使用角色                                             |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 1    | 主页     | 待遇审核看板 | 待遇审核数据分析查看                           | 信息维护人员；待遇受理；待遇初审；待遇复审；待遇终审 |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 2    |          | 工伤申报看板 | 工伤申报数据分析查看                           | 信息维护人员；报案管理；申报管理                     |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 3    | 信息查询 | 医院信息     | 就诊医院信息查询、维护                         | 全部可查看，仅信息维护人员可编辑                     |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 4    |          | 三目录信息   | 三目录信息查询、维护                           | 全部可查看，仅信息维护人员可编辑                     |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 5    |          | 企业认证信息 | 企业认证信息查询、审核、材料提交方式控制       | 全部可查看，仅信息维护人员、申报类角色可编辑         |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 6    | 工伤申报 | 任务列表     | 工伤认定相关任务处理                           | 报案管理、申报管理                                   |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 7    |          | 作业数据     | 清单生成与查询、历史案件查询、企业申报情况查询 | 报案管理、申报管理                                   |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 8    | 工伤待遇 | 任务列表     | 业务受理、接收、处理                           | 待遇受理；待遇初审；待遇复审；待遇终审               |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 9    |          | 作业数据     | 清单生成与查询、历史案件查询、医院支付统计     | 待遇受理；待遇初审；待遇复审；待遇终审               |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 10   | 运营配置 | 字典管理     |                                                | 信息维护人员                                         |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 11   |          | 业务指南配置 |                                                | 信息维护人员                                         |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 12   |          | 终端机配置   |                                                | 信息维护人员                                         |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+
| 13   | 系统     | 个人中心     |                                                |                                                      |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 14   |          | 后台用户     |                                                |                                                      |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 15   |          | 角色管理     |                                                |                                                      |
+------+          +--------------+------------------------------------------------+------------------------------------------------------+
| 16   |          | 部门管理     |                                                |                                                      |
+------+----------+--------------+------------------------------------------------+------------------------------------------------------+

: 表 2‑1 智慧工伤业务经办系统功能清单

# 全局说明

  -------- ---------------- ---------------------------------------------------------------------------------------------------------------------------------------
  序号     项目             说明

  1        列表排序规则     未特别说明时，按数据记录的创建时间降序排列；

  2        数据保存         若一次性保存的数据较多，需要的时间较长，超过5s，需要给出反馈，如遮罩"正在保存，请稍后"类似提示语

  3        文件上传         未特别说明时，单场景下，支持批量上传文件，单个文件大小限制20M，支持的文件格式包括：图片类、文档类、视频类。

  4        查询条件         无特别说明的情况下，查询条件可灵活搭配，文本查询是否输入，下拉查询是否选择，选择几级都不应设限。

  5        内容展示、处理   一般提到用逗号隔开，无特别说明，均指以中文逗号隔开，但系统需具有兼容性，如导入数据时，EXCEL模板中用户填写为了英文逗号，能够进行处理。
  -------- ---------------- ---------------------------------------------------------------------------------------------------------------------------------------

# 功能说明

## 主页

### 功能概述

查看业务数据分析看板。

### 功能描述

原工伤申报系统的数据看板转移至此处且不变，改为"工伤申报看板"，仅工伤申报相关权限可见；新增"待遇审核看板"，仅待遇审核相关权限可见。

#### 待遇审核看板主界面

![](./media/image2.png){width="5.750694444444444in"
height="3.147222222222222in"}

1)  **展示内容**

- 待办业务：展示各业务环节的待办业务量，按案件状态统计

  - 受理中：业务受理列表中案件，状态均默认为"受理中"

  - 初审中：待接收列表、我的任务中的，案件状态为"待初审"的

  - 复审中：待接收列表、我的任务中的，案件状态为"复审中"的

  - 终审中：待接收列表、我的任务中的，案件状态为"终审中"的

- 业务情况统计：支持按年或按年-月统计，默认统计当年全年数据，鼠标悬浮展示当前统计维度及统计结果

  - 业务办理统计：针对业务量进行统计，环形图表现

    - 总案件量=受理中+待初审+待复审+待终审+已办结

  - 金额统计：仅统计已办结金额

    - 申请总金额=发票总金额+伙食补助总金额

    - 一般审核扣减=不可报销金额-非工伤

    - 非工伤费用扣减=非工伤

    - 第三方支付扣减=第三方分摊赔付金额

    - 住院伙食费=住院伙食补助

    - 实际支付医疗费=可报销金额

  - 业务办结统计：柱状图+折线图

    - 柱状图：统计每月办结量

    - 折线图：统计平均每件案件办理时长（办结时间-受理时间，天）

### 信息查询

#### 功能概述

用于查询、维护医院信息、三目录信息及企业认证信息，所有角色可访问、查询，但仅信息维护岗可编辑。

### 用例说明

### 功能描述

#### 医院信息

![](./media/image3.png){width="5.759722222222222in"
height="3.147222222222222in"}

数据来源：

- 手动新建/批量导入：仅信息维护岗可新建

- 自动新增：

  - 医疗待遇审核案件中未匹配医院库中的医院名称，办结后自动新增至医院信息库

  - 医院详情中，机构名称默认医院名称，是否协议机构默认"否"，其他信息置空

  - 费用项目查询中，自动新增电子清单中的项目名称、费用类型、单价信息

展示信息：

1)  **查询条件**

> 机构名称：关键词搜索查询
>
> 是否协议机构：单选，是；否
>
> 所在区域：级联选择，省/市/区，不限选至第几级

2)  **医院列表**

    a)  **表头**

        固定"机构名称"、状态、操作列，其他信息左右滑动查看

        展示字段：

+----------------------+-----------------------+-----------------------+
| **表头字段**         | **对应医院详情**      | **说明**              |
+----------------------+-----------------------+-----------------------+
| 机构名称             | 机构名称              |                       |
+----------------------+-----------------------+-----------------------+
| 等级                 | 机构等级              | 示例"一级甲等"        |
+----------------------+-----------------------+-----------------------+
| 机构类型             | 机构类型              | 多个标签展示          |
+----------------------+-----------------------+-----------------------+
| 是否协议机构         | 是否协议机构          | 是/否                 |
+----------------------+-----------------------+-----------------------+
| 开始时间             | 开始时间              | 年-月-日              |
+----------------------+-----------------------+-----------------------+
| 结束时间             | 结束时间              | 年-月-日              |
+----------------------+-----------------------+-----------------------+
| 省                   | 省市区                | 取省                  |
+----------------------+-----------------------+-----------------------+
| 市                   | 省市区                | 取市                  |
+----------------------+-----------------------+-----------------------+
| 区                   | 省市区                | 取区                  |
+----------------------+-----------------------+-----------------------+
| 详细地址             | 详细地址              | 仅取详细地址          |
+----------------------+-----------------------+-----------------------+
| 状态                 | 是否禁用              |                       |
+----------------------+-----------------------+-----------------------+
| 操作                 |                       | 查看：界面同新建      |
|                      |                       |                       |
|                      |                       | 项目费用查询          |
|                      |                       |                       |
|                      |                       | 主次机构设置          |
|                      |                       |                       |
|                      |                       | 删除                  |
+----------------------+-----------------------+-----------------------+

固定"机构名称"、状态、操作列，其他信息左右滑动查看

b)  **状态**

    支持在列表中修改状态，状态为禁用的，
    快报定点医院医院列表/终端机定点医院列表不展示；账单匹配医院信息时展示禁用状态（见案件详情）

c)  **查看**

    仅信息维护岗可编辑，其他角色仅可查看，界面同新建

d)  **项目费用查询**

    ![](./media/image4.png){width="5.759722222222222in"
    height="3.147222222222222in"}

    顶部展示医院信息，下方展示查询条件、操作按钮、项目列表。

    **医院信息：**

    机构名称·是否协议机构：

- 医院详情中，是否协议机构为否时：机构名称·非协议机构

- 医院详情中，是否协议机构为是时：

  - 状态禁用：机构名称·协议机构（已禁用）

  - 开始日期在当前日期之后：机构名称·协议机构（未开始）

  - 结束日期在当前日期之前：机构名称·协议机构（已终止）

    地址信息：省/市/区/详细地址

    机构类型：按标签形式展示

    **查询条件**

    按费用类型查询：药品费、检查治疗费、治疗费、血费、材料费、床位费

    按项目名称查询

    **批量新建/新建**

    **TODO**

    **批量删除/删除**

    多选项目，点击删除，提示："是否删除所选项目？删除后不可恢复"点击"确认"，删除所选项目，点击"取消"，取消删除

    **项目列表信息**

    ![](./media/image5.png){width="5.75625in" height="0.73125in"}

    当待遇支付案件办结后，该案件中各电子清单对应的医院中自动新增电子清单中的项目名称、费用类型、单价，更新时间为办结时间

    若项目名称已存在（名称完全相同），则不再新增

    列表中信息支持编辑、删除（删除提示同上）

    ![](./media/image6.png){width="5.766666666666667in"
    height="3.154166666666667in"}

    （图中标题改为编辑项目）

  a)  **主次机构设置**

      规则类似智慧工伤云平台中机构管理

      ![](./media/image7.png){width="1.6875in"
      height="2.1979166666666665in"}

      点击图标，设置该医院为主机构/次机构，主机构以最新设置为主机构医院为准

      点击列表上方"机构归集"，弹窗展示机构归集界面

      顶部展示主机构名称与地址，下方展示已设置为次机构的医院名称与地址，点击"推荐相似机构"，在次机构列表中新增AI对比的相似机构（根据机构名称、机构别称对比推荐）

      次机构列表中，支持删除或更换该次机构为主机构，更换后，原主机构变为次机构

      点击确认，医院列表中删除次机构医院详情信息，将其项目费用、金额统计信息等合并至主机构医院，其中项目费用信息中，项目名称、单价完全一致的，需去重处理

      ![](./media/image8.png){width="5.0in" height="4.65625in"}

3)  **新建**

> 仅信息维护岗可新建，其他角色不可新建
>
> ![](./media/image9.png){width="5.759722222222222in"
> height="3.147222222222222in"}

+:-------------+:-------------+:-------------+:------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| **字段名称** | **输入方式** | **是否必填** | **字段说明**                                                                                                                                                      |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 机构名称     | 字符串       | 是           | 输入4个字符后，AI自动推荐医院列表中相似医院名称（根据机构名称、机构别称推荐），关闭后，可点击输入框右侧"推荐相似医院"打开推荐列表（不满足推荐要求时，该按钮禁用） |
|              |              |              |                                                                                                                                                                   |
|              |              |              | 点击推荐的机构名称，回显该机构名称信息，不可编辑，仅查看                                                                                                          |
|              |              |              |                                                                                                                                                                   |
|              |              |              | 机构名称不可与机构名称、机构别称重复（小字提示"机构名称重复"）                                                                                                    |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 是否协议机构 | 单选         | 是           |                                                                                                                                                                   |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 是否禁用     | 单选         | 是           | 默认启用，禁用后，即使满足协议机构条件，仍不可在快报定点医院列表展示                                                                                              |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 机构别称     | 字符串       |              | 输入内容后点击添加，添加为新别称，新别称支持删除                                                                                                                  |
|              |              |              |                                                                                                                                                                   |
|              |              |              | 机构名称不可与机构名称、机构别称重复（小字提示"机构别称重复"）                                                                                                    |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 机构等级     | 级联选择     |              | 一级选项：一级;二级;三级;未定级；二级选项：甲等;乙等;丙等（未定级无二级选项）                                                                                     |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 开始日期     | 选择         |              | 年-月-日                                                                                                                                                          |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 结束日期     | 选择         |              | 年-月-日                                                                                                                                                          |
|              |              |              |                                                                                                                                                                   |
|              |              |              | 终止日期为空的，一直有效                                                                                                                                          |
|              |              |              |                                                                                                                                                                   |
|              |              |              | 当前日期超过结束日期的，"是否协议机构"自动变更为否                                                                                                                |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 机构类型     | 多选         | 是           | 医疗机构;辅助器具配置;急性康复期;维持康复期;稳定康复期（仅是否协议机构"是"的必填，否则禁用）                                                                      |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 限价医院等级 | 关联搜索选择 |              | 根据字典                                                                                                                                                          |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 详细地址     |              |              | 输入地址关键词，查询详细地址并选择，自动填充经纬度                                                                                                                |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 省市区       | 级联选择     |              | 根据详细地址，自动填充省市区                                                                                                                                      |
+--------------+--------------+--------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+

4)  **批量导入**

    ![](./media/image10.png){width="5.7625in"
    height="3.238888888888889in"}

    导入方式与智慧工伤云平台机构管理中一致。

    **导入文件：**

    模板：![](./media/image11.emf)

    提示语："请使用下载的模板，不要调整结构，仅支持Excel上传"

    点击确认，开始解析文件数据，进入下方界面（解析过程+解析结果）：

    ![](./media/image12.png){width="5.7625in"
    height="4.348611111111111in"}

    解析过程

    ![](./media/image13.png){width="5.7625in"
    height="5.977083333333334in"}

    解析结果

    **解析规则与结果：**

    相似导入选项：

- 选择"先确认"时，

  - AI对比现有医院列表信息，按名称、机构别称对比相似机构

  - 如有相似机构，在选项下方生成Excel文件（文件名：相似机构对比结果），在原上传文件中最后一列新增相似机构名称，相似机构对应行号标红

  - 对比期间，不可确认上传；对比完成并成功生成Excel，点击上传，相似机构正常新建

- 选择"直接导入"时，无需AI对比，正常导入

  重复导入选项：

<!-- -->

- 必选，选项："名称相同跳过;名称相同覆盖"

- 选择名称相同跳过，按机构名称对比，名称完全一致的，不导入，导入失败

- 选择名称相同覆盖，按机构名称对比，名称完全一致的，导入后，覆盖该机构原本信息（导入信息中未填充的字段，导入时保留原数据，不覆盖）

  解析过程：展示导入行数、成功导入行数、导入失败行数、导入失败行号

  解析结果：展示导入行数、成功导入行数、导入失败行数、导入失败行号、导入失败原因

5)  **其他使用方向**

    工伤快报中定点医院信息取医院信息中，是否协议医院为"是"的、未禁用的，且开始时间在当前日期前、结束日期在当前日期之后/无结束日期的

#### 三目录信息

![](./media/image14.png){width="5.759722222222222in"
height="3.147222222222222in"}

（图中顶部查询条件删除）

支持在列表顶部按关键词搜索项目信息

列表中按目录编码、目录类别、项目名称、费用等级、开始日期、结束日期、是否有效展示及搜索

**新建及编辑**

![](./media/image15.png){width="5.759722222222222in"
height="3.147222222222222in"}

新建/编辑界面如上图，仅信息维护岗可新建或编辑，其他角色仅可查看项目详情（其他角色不可见列表中的编辑图标）

目录编码：非必填，字符串

项目名称：必填，字符串

费用等级：必填，选项（甲;乙;丙）

目录类别：必填，选项（药品目录;诊疗服务;医用耗材）

限价目录信息：

- 按列表展示，支持新增、删除，限价医院等级以字典为准，字典变更后，保留原数据内容，新增时按字典为准新增；

- 当前日期在项目的开始/结束日期之内的（含开始日期在当前日之前且无结束日期的/结束日期在当前日期之后且无开始日期的），为有效，否则无效

**批量导入**

模板：![](./media/image16.emf)

同一项目存在多个限价医院等级信息的，按多行输入，目录编码、项目名称、费用等级、目录类别、开始日期、结束日期一致，系统自动维护至同一项目信息下

**批量删除/删除**

多选后点击删除，或在列表中删除。

点击删除后，提示"是否删除所选项目？删除后不可恢复"点击"确认"，删除所选项目，点击"取消"，取消删除

**查看目录详情：**不可编辑，仅查看

![](./media/image17.png){width="5.759722222222222in"
height="3.147222222222222in"}

#### 企业认证信息

维持现状，全部可查看，仅信息维护人员、申报类角色可编辑

## 工伤申报

### 用例说明

### 功能描述

![](./media/image18.png){width="5.759722222222222in"
height="3.147222222222222in"}

任务列表，与当前申报功能一致

作业数据，原申报系统的清单生成、清单列表、历史案件、企业申报情况在此列表展示

其中，历史案件查询类别为身份证查询、姓名查询

"搜索方式"改为"搜索内容"：

选择身份证查询时，搜索内容输入身份证关键词，查询历史案件

选择姓名查询时，搜索内容输入姓名关键词，查询历史案件

## 工伤待遇

### 功能概述

待遇相关岗位用于案件受理、初审、复审、终审，查看相关作业数据。

### 用例说明

### 功能描述

#### 业务受理

![](./media/image19.png){width="5.759722222222222in"
height="3.147222222222222in"}

仅待遇受理岗可使用

展示信息：

1)  **查询条件**

> 职工姓名、单位名称、身份证号码，关键词搜索查询，受理时间，时间范围选择查询。

2)  **列表信息**

> 展示所有未提交的受理中案件
>
> 姓名、身份证号码、单位名称、受理时间、创建用户、最近操作用户
>
> 其中创建用户为初次保存用户名称
>
> 最近操作用户为最近一次保存的用户名称

3)  **新建及编辑**

> ![](./media/image20.png){width="5.750694444444444in"
> height="3.147222222222222in"}
>
> **材料上传**
>
> 点击材料区空白处"点击上传"、信息区右下角"上传"按钮，进入材料上传界面，上传业务材料
>
> ![](./media/image21.png){width="5.756944444444445in"
> height="3.147222222222222in"}
>
> 默认展示分类：申请材料（1）、就诊材料（1），其中就诊材料支持新增、删除
>
> 每个分类均可点击"本地上传"，批量上传本地图片、文档（word、Excel、pdf），支持多选批量删除已上传材料，序号同每类材料的页码
>
> 材料区顶部分类分页：
>
> （图中二级需修改为一级）材料类别名称-页码
>
> 申请材料：

- 文件类型：身份证、待遇申请表、工伤认定决定书、劳动关系证明、其他未识别

  就诊材料：

- 分类名称：需选择就诊类型 （住院/门诊）及就诊日期（年月日）

- 材料区顶部分类名称：就诊材料/就诊类型/就诊日期

- 文件类型：病历、手术记录、合格证、清单、电子发票、非电子发票、追回单、检查报告、医嘱单、其他就诊报告、其他未识别

- 每个分类右上角可点击新增、删除，创建新的就诊材料分类，或删除当前分类下材料

材料类型识别：

- 存在已上传的材料时，选择材料（点击分类，全选该分类下材料），点击右上角"材料类型识别"，开始AI识别已选中材料类型

- 识别过程中，顶部展示"正在识别文件类型，当前排队xxx个，请稍后"

- 每个材料类型下展示当前类型材料识别进度，

  - 识别中的，点击"停止识别"，以当前识别结果为准，进度状态变为"已停止识别"

  - 未开始识别的，点击"取消识别"，不再识别，进度状态变为"已停止识别"

    ![](./media/image22.png){width="3.5in" height="0.15625in"}

    ![](./media/image23.png){width="3.5729166666666665in"
    height="0.22916666666666666in"}

    ![](./media/image24.png){width="4.0in"
    height="0.9895833333333334in"}

    点击提交时，文件类型存在"其他未识别"类型时（未完成AI识别时，展示如下提示，点击"确认"，以当前识别结果为准，默认全部停止识别/取消识别）；点击"取消"，取消提交，继续识别或编辑

    ![](./media/image25.png){width="3.6875in"
    height="1.7604166666666667in"}

    上传完成后，点击"取消"，展示如下提示，点击"确认"，提交上传信息后返回案件详情；点击"取消"，继续编辑

    ![](./media/image26.png){width="3.6875in"
    height="1.7604166666666667in"}

> ~~已上传的案件，材料区下方展示每页材料的文件类型（支持修改）、已关联账单（通过选择账单号，可绑定已填充账单，支持解除关联）、上传用户~~
>
> ![](./media/image27.png){width="5.766666666666667in"
> height="0.9416666666666667in"}
>
> **受理信息**
>
> ![](./media/image28.png){width="5.757638888888889in"
> height="1.0347222222222223in"}
>
> 材料上传完成后，AI自动识别受理信息、默认填充部分信息，均支持修改
>
> 其中职工姓名处，支持截图选择身份证图片，ocr识别后，自动填充职工姓名、性别、身份证号码

+:--------:+:----:+:------------:+:------------:+:------------------------:+:------------:+:--------------:+:------------:+:--------------------------------------:+
| **模块**        | **字段**     | **是否必填** | **填充方式**             | **输入类型** | **是否AI识别** | **计算公式** | **说明**                               |
+----------+------+--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
| 受理信息 | \\   | 受理日期     | 是           | 自动生成，可修改         | 日期         |                |              | 默认当前日期，可修改                   |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 事故日期     | 是           | 识别填充，可修改         | 日期         | 是             |              |                                        |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 治疗类型     | 是           | 默认"初次治疗"，可修改   | 单选         |                |              | 初次治疗；康复治疗；旧伤复发；再次治疗 |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 职工姓名     | 是           | 识别填充，可修改         | 字符串       | 是             |              |                                        |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 性别         | 是           | 识别填充，可修改         | 字符串       | 是             |              |                                        |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 身份证号码   | 是           | 识别填充，可修改         | 字符串       | 是             |              |                                        |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 用人单位名称 | 是           | 识别填充，可修改         | 字符串       | 是             |              |                                        |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 是否参保     | 是           | 默认是，可修改           | 单选         |                |              | 是/否                                  |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 参保地       | 是           | 默认"锦江区"可修改       | 单选         |                |              | 同工伤申报参保地选项                   |
|          |      +--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+
|          |      | 申请业务     | 是           | 默认选择医疗费、伙食补助 | 多选         |                |              | 医疗费；伙食补助                       |
+----------+------+--------------+--------------+--------------------------+--------------+----------------+--------------+----------------------------------------+

> **诊断信息**
>
> ![](./media/image29.png){width="5.763888888888889in"
> height="1.6416666666666666in"}
>
> 材料上传完成后，AI识别工伤认定决定书及病历信息，自动填充至诊断信息模块，其中：
>
> 工伤诊断：

- AI识别工伤认定决定书，分条填充临床诊断

- 支持拖动表格排序

- 支持手动新增/删除

  手术信息：

- AI识别病历材料，分条识别手术名称

- 支持拖动表格排序

- 支持手动新增/删除

> **账单信息**
>
> ![](./media/image30.png){width="5.7625in"
> height="2.6798611111111112in"}
>
> 门诊账单详情
>
> ![](./media/image31.png){width="5.7625in"
> height="3.0930555555555554in"}
>
> 住院账单详情
>
> **翻页**：账单列表支持上下翻页，序号不受翻页影响
>
> **账单列表：**回显账单号、就诊日期、就诊类型、治疗医院、账单金额、合理费用、审核扣减、非工伤扣减、先期类型、是否追回信息，点击账单，在下方展示账单信息详情，可编辑
>
> **AI智能识别账单：**材料上传后，AI识别电子发票、非电子发票材料及其对应病历，自动创建账单信息并填充，并通过账单号关联至对应电子发票/非电子发票材料（材料区）
>
> **新增账单：**点击账单列表自动定位至尾页最后一条信息，新增一条空白数据并选中，下方展示空白账单详情，填充后自动回显至列表中
>
> **OCR新增：**点击后进入截图界面，识别成功后，在列表末尾新增识别成功数据；未识别成功的，提示"未识别到账单信息！"
>
> **删除账单：**多选账单后，点击"删除账单"，删除选中账单信息及其电子清单信息
>
> **重复报销：**账单信息识别完成后，将本案账单号与历史已办结案件对比，完全一致的，在上方提示，"账单xxxx存在历史报销，点击案件编码查看历史案件详情
> 案件编码"，点击编码，查看历史案件详情，仅回显，不可编辑
>
> **发票验真：**选择账单，点击"发票验真"，传输发票信息至验真接口：

- 全部真：提示"未检测到虚假发票"

- 存在假/未检测出结果：提示"xxx疑似虚假发票；xxx检测失败"

> 账单信息字段及填充规则如下：

+:--------:+:----------------:+:----------------:+:------------:+:----------------------:+:--------------:+:------------------------------------------------------------------------------------------------------------------------:+:--------------------------------------------------------------------------------------------------------:+
| **模块**                    | **字段**         | **是否必填** | **填充方式**           | **是否AI识别** | **计算公式**                                                                                                             | **说明**                                                                                                 |
+----------+------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
| 账单信息 | 发票信息（通用） | 治疗医院         | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 输入关键词，即可自动推荐相似机构（以机构名称、别称对比），点击自动填充机构名称，匹配医院等级、性质等信息 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 医院等级         |              | 识别填充               |                |                                                                                                                          | 根据治疗医院名称匹配医院库信息                                                                           |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 医院性质         |              | 识别填充，可修改       | 是             |                                                                                                                          | 根据治疗医院名称匹配医院库信息\                                                                          |
|          |                  |                  |              |                        |                |                                                                                                                          | 识别为协议机构的：\                                                                                      |
|          |                  |                  |              |                        |                |                                                                                                                          | 门诊开始时间/入院日期在开始/结束日期之间的，回显"定点医院"\                                              |
|          |                  |                  |              |                        |                |                                                                                                                          | 门诊开始时间/入院日期在开始日期之前的，回显"定点医院（未开始）"\                                         |
|          |                  |                  |              |                        |                |                                                                                                                          | 门诊开始时间/入院日期在结束日期之后的，回显"定点医院（已终止）"\                                         |
|          |                  |                  |              |                        |                |                                                                                                                          | 状态为禁用的，回显"定点医院（禁用）"\                                                                    |
|          |                  |                  |              |                        |                |                                                                                                                          | 识别为非协议机构的：回显"非定点医院"\                                                                    |
|          |                  |                  |              |                        |                |                                                                                                                          | 未匹配到医院信息的，回显"其他"，展示按钮"匹配"，匹配医院信息，根据匹配结果修改医院等级、医院性质         |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 账单号           | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 不可与本案件的账单号重复                                                                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 账单数           |              | 自动填充               |                | 按账单数量合计                                                                                                           |                                                                                                          |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 治疗类型         | 是           | 识别填充，可修改，单选 | 是             |                                                                                                                          | 按住院/门诊选择，根据选择结果，展示不同字段                                                              |
|          +------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          | 发票信息（门诊） | 门诊开始时间     | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 年-月-日                                                                                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 门诊结束时间     | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 年-月-日                                                                                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 门诊天数         | 是           | 自动生成，可修改       |                | 门诊结束日期-门诊开始日期                                                                                                | 按天计算，保留整数                                                                                       |
|          +------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          | 发票信息（住院） | 入院日期         | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 年-月-日                                                                                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 出院日期         | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 年-月-日                                                                                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 住院天数         | 是           | 自动生成，可修改       |                | 出院日期-入院日期                                                                                                        | 按天计算，保留整数                                                                                       |
|          +------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          | 发票信息（通用） | 先期给付类型     |              | 识别填充，可修改，单选 | 是             |                                                                                                                          | 商业保险；职工医保；农村合作医疗；大病支付；贫困救助；其他救助；其他基金                                 |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 是否追回         | 是           | 识别填充，可修改       | 是             |                                                                                                                          | 未选择先期给付类型时禁用，选择先期给付类型后，必填                                                       |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 是否跨统筹区就诊 | 是           | 自动生成，可修改       | 是             |                                                                                                                          | 是/否；\                                                                                                 |
|          |                  |                  |              |                        |                |                                                                                                                          | 匹配医院库信息后，若该医院为四川省的，自动选中"是"；非四川省的，自动选中"否"                             |
|          +------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          | 账单分项         | 编码             | 是           | 自动生成，可修改，数字 | 是             |                                                                                                                          | 选择费用项目后，自动填充编码（字典nodecode）                                                             |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 费用项目         | 是           | 自动生成，可修改，单选 | 是             |                                                                                                                          | 根据费用项目字典选择，编码输入正确的，自动选择对应项目，单张账单费用项目不可重复                         |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 账单金额         | 是           | 识别填充，可修改，数字 | 是             |                                                                                                                          | AI识别账单中各分项金额                                                                                   |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 合理费用         |              | 自动生成，不可修改     |                | 账单金额-审核扣减-非工伤扣减                                                                                             |                                                                                                          |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 审核扣减         |              | 自动生成，不可修改     |                | 电子清单中：点击"不可报销"后扣除的不可报销金额+费用等级匹配为丙类的不可报销金额+选中限价医院等级后自动扣减的不可报销金额 |                                                                                                          |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 非工伤扣减       |              | 自动生成，不可修改     |                | 电子清单中：点击"不可报销（非工伤）"后扣除的不可报销金额+是否工伤选择为否的不可报销金额                                  |                                                                                                          |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 备注             |              |                        |                |                                                                                                                          |                                                                                                          |
|          |                  +------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+
|          |                  | 总计             |              |                        |                | 各字段的分项金额相加                                                                                                     |                                                                                                          |
+----------+------------------+------------------+--------------+------------------------+----------------+--------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------+

> ![](./media/image32.png){width="3.84375in" height="0.3125in"}
>
> 医院信息未匹配的（"其它"），点击"匹配"，弹窗搜索医院库信息，输入4个字，AI即可根据机构名称、机构别称推荐医院信息库中相似机构，点击回显该机构基础信息，不可编辑，点击保存，匹配成功，账单信息中"治疗医院"不变；
>
> 修改治疗医院信息后，以新内容为准，重新匹配医院信息
>
> ![](./media/image33.png){width="5.763194444444444in"
> height="4.240972222222222in"}
>
> **电子清单**
>
> ![](./media/image34.png){width="5.763194444444444in"
> height="4.540277777777778in"}
>
> AI识别账单信息后，传输账单号、校验码等信息至识别接口，自动生成电子清单，接口传输失败的，AI识别该账单对应费用清单，自动生成电子清单。
>
> 已生成的电子清单自动对比三目录库中的费用等级自动扣除不可报销金额，自动填充至账单各分项的审核扣减金额

  ----------------------------------- -----------------------------------
  电子清单费用类别                    三目录对应目录类别

  床位费                              一般医疗服务

  药品费                              药品费

  检查费                              一般医疗服务

  治疗费                              一般医疗服务

  材料费                              医用耗材

  血费                                一般医疗服务；医用耗材
  ----------------------------------- -----------------------------------

> 点击账单分项表头上方"电子清单"，打开电子清单界面，展示全部分项的电子清单；点击账单分项中"查询"，打开电子清单界面，自动筛选对应分项展示电子清单。
>
> **电子清单/基本信息**
>
> 治疗医院：回显该账单对应医院名称，不可修改
>
> 机构等级：回显该医院对应医院等级，不可修改
>
> 限价医院等级：

- 回显该医院在医院库中的限价医院等级，可修改，修改信息仅在此电子清单使用

- 修改限价医院等级后，甲类且是否工伤未选择或为是的项目，自动按照限价医院等级，扣除超限价费用，可修改

- 不可报销金额=（单价-限价）\*数量

- 该项目的甲乙丙、是否工伤修改后，自动扣除超限价费用，可修改

- 确认限价医院等级后，弹窗提示，点击确认，按上述规则扣费

  ![](./media/image35.png){width="3.6666666666666665in"
  height="1.0625in"}

> **电子清单/清单明细**
>
> 列表信息：

- 序号：顺序排列，翻页不影响序号排列，不重新排序

- 项目名称：必填，自动填充，可修改，未自动填充的可新增输入

- 项目编码：非必填，自动填充，可修改，未自动填充的可新增输入

- 费用类别：

  - 可根据列表中现有费用类别（对应账单费用类别）选择筛选查看

  - 默认"未匹配"，可通过AI自动匹配或手动修改匹配

- 费用等级：可根据列表中现有费用等级选择筛选

  - 默认"未匹配"

  - 根据项目名称自动匹配三目录库甲、乙、丙类

  - 未匹配三目录库的：未匹配

  - 三目录库中有多个匹配项的：多个匹配项

- 数量：必填，仅数字，小数点后2位

- 单价：必填，自动计算，金额/数量

- 金额：必填，自动计算，数量\*单价

- 是否工伤：

  - 必填，选择是/否，

  - 默认"是"

  - 选择否的，自动扣除全部金额至不可报销金额，扣除费用合计后标记并填充账单至"非工伤扣减"

- 不可报销金额：必填，默认0

  - 三目录匹配为甲、乙类的，不自动扣费（超限价扣除的除外）；三目录匹配为丙类的，自动扣除金额，标记为审核扣减

  - 超限价扣除在此扣除，标记为审核扣减

  - 是否工伤，标记为否的，自动扣除金额，标记为非工伤扣减

  - 多选后选择顶部批量操作按钮中可报销的，扣减金额为0；选择不可报销的，自动扣减金额，标记为审核扣减；选择不可报销（非工伤）的，自动扣减金额，标记为非工伤扣减

  - 手动扣除的，标记为审核扣减

- 操作

  - 目录匹配查询

    查询三目录中匹配信息

<!-- -->

- 顶部展示选中项目、费用类别、限价医院等级信息，仅回显

- 回显信息下方展示查询条件，项目名称自动填充选中项目名称，可修改，支持关键词搜索；目录类别按三目录类别选择查询；目录编码支持关键词搜索

- 查询结果：已匹配三目录信息的，置顶展示已匹配目录信息，下方展示其他查询到的结果，限价金额以当前电子清单确认的限价医院等级对应金额为准；已匹配信息不可选，其他查询结果点击匹配，则修改匹配结果并关闭弹窗，回到电子清单界面

  ![](./media/image36.png){width="5.763888888888889in"
  height="3.2083333333333335in"}

  - AI识别项目

    点击后弹窗展示AI查询结果（适应症+禁忌），搜索项目自动填充为项目名称，支持修改，点击查询，展示查询结果

    下方展示查询项目名称，自动填充为项目名称，支持修改，搜索关键词选择，查询结果展示该医院信息中该项目更新时间最近的单价及医院信息库中成都市医院的该项目更新时间最近的各医院最低单价-最高单价

    ![](./media/image37.png){width="5.763194444444444in"
    height="3.217361111111111in"}

  - 添加至本地目录

    选中项目信息自动填充至列表，限价金额以当前电子清单的限价医院等级为准，点击确认，添加至三目录库

    ![](./media/image38.png){width="5.763888888888889in"
    height="3.2083333333333335in"}

> **电子清单/批量操作按钮**
>
> ![](./media/image39.png){width="5.767361111111111in"
> height="0.36944444444444446in"}
>
> 可报销：多选项目后，点击，选中项目不可报销金额为0
>
> 不可报销：多选项目后，点击，选中项目不可报销为项目金额，标记为审核扣减
>
> 不可报销（非工伤）：多选项目后，点击，选中项目不可报销金额为项目金额，是否工伤自动选择为"否"，标记为非工伤扣减
>
> 添加至本地目录：多选后，项目信息自动填充至列表，目录类别根据费用类别自动匹配，规则同上，点击确认，自动添加至三目录库
>
> ![](./media/image38.png){width="5.763888888888889in"
> height="3.2083333333333335in"}
>
> 批量关联项目：

- 多选后，展示选中项目信息，展示在弹窗列表中，可通过点击"移除"，取消选择

- 选择关联项目，即费用类别后，点击"确认"，修改选择项目的费用类别

> ![](./media/image40.png){width="5.761111111111111in"
> height="3.2993055555555557in"}
>
> 提示信息：分别展示账单总金额、分项金额与电子清单总金额、各费用类型项目金额
>
> ![](./media/image41.png){width="5.761805555555555in"
> height="0.2388888888888889in"}
>
> Ocr新增：点击后进入截图界面，截取清单图片，AI识别后自动填充至电子清单并自动匹配；未识别的，提示如下
>
> ![](./media/image42.png){width="3.2291666666666665in" height="1.25in"}
>
> 新增：点击后自动定位至尾页列表末尾，新增3个空白项
>
> 删除：多选后，点击删除，删除所选项目，其他项目序号排序自动调整
>
> **电子清单/提交**
>
> 验证项目如下：

- 电子清单金额或分项金额与账单不符：点击取消，停留在电子清单界面；点击确认，不修改账单总金额/分项金额，仅填充扣费结果至账单信息

  ![](./media/image43.png){width="3.6666666666666665in"
  height="1.0625in"}

- 分项金额大于账单金额：提示如下，不可提交

  ![](./media/image44.png){width="3.2291666666666665in" height="1.25in"}

- 存在未匹配三目录项目（费用等级）：点击取消，停留电子清单界面；点击确认，该项目扣减费用为0，返回录入界面并填充电子清单扣费结果至账单信息

  ![](./media/image45.png){width="3.6666666666666665in"
  height="1.0625in"}

- 存在未选择费用类别项目：不可提交，该项目高亮展示

  ![](./media/image46.png){width="3.2291666666666665in" height="1.25in"}

> 验证无误后可正常提交
>
> **电子清单/保存**
>
> 保存扣费信息，返回录入界面，自动填充至电子账单信息
>
> 点击左上角退出/底部取消后，提示如下，点击取消，不保存本次编辑结果，点击确认，保存本次编辑结果后返回录入界面
>
> ![](./media/image47.png){width="3.6666666666666665in"
> height="1.0625in"}
>
> **案件操作记录**
>
> 案件详情底部展示创建用户信息及创建时间、当前用户名称，点击操作记录，弹窗展示操作记录
>
> 状态：新建受理、受理修改、初审修改、初审提交、复审修改、复审提交、终审修改、终审提交
>
> ![](./media/image48.png){width="5.763194444444444in"
> height="3.217361111111111in"}
>
> **受理案件提交**
>
> ![](./media/image49.png){width="5.761111111111111in"
> height="5.918055555555555in"}
>
> 点击提交，需验证：

- 存在未填写的必填项

  ![](./media/image50.png){width="3.6875in"
  height="1.7604166666666667in"}

- 存在未确认扣费的账单：即账单对应的电子清单扣费结果未提交

  ![](./media/image51.png){width="3.6875in"
  height="1.7604166666666667in"}

#### AI识别助手

![](./media/image52.png){width="5.757638888888889in"
height="2.301388888888889in"}

点击浮标"AI识别助手"，弹窗展示"AI识别助手页面"，点击屏幕截图，截图识别图片文字（交互同工伤申报）

输入查询内容，点击AI诊疗查询，若内容为药品、检查、诊疗项目，AI查询对应内容的适应症及禁忌并展示；若内容为诊断、手术，则AI查询对应内容的解释（什么是xxx）

#### 计算器

![](./media/image53.png){width="5.768055555555556in"
height="2.497916666666667in"}

点击浮标"计算器"，弹窗展示"人性化计算器"页面。

输入数字公式（+-\*/），键盘=或回车，自动计算结果并自动换行，以便继续计算

输入文字，点击回车，换行提示"非计算公式，无法计算！"，自动换行，不影响继续计算

点击右上角"清空"，清除内容框所有内容，初始化

#### 待接收任务

![](./media/image54.png){width="5.750694444444444in"
height="3.147222222222222in"}

**数据来源：**

待初审：受理提交

待复审：待初审任务提交

待终审：待复审任务提交

**查询条件：**

职工姓名：关键词搜索

单位名称：关键词搜索

身份证号码：关键词搜索

申请时间：时间范围搜索

案件状态：待初审、待复审、待初审，根据角色展示选项

**列表信息：**

案件编号：受理提交后自动生成，格式：BX1014受理日期+序号，例：BX101420250604001

职工姓名：回显

身份证号码：回显，无需部分遮蔽（原型需修改）

单位名称：回显

受理时间：受理提交时间

案件状态：待初审、待复审、待终审

提交用户：上一环节提交的用户名

**接收案件：**

仅待复审案件不可接收自己初审完成的案件

可点击列表操作列的接收图标，接收案件至自己的任务列表

可选中案件后，点击右上角"批量接收"，案件接收至自己的任务列表，接收成功后弹窗提示

![](./media/image55.png){width="4.71875in"
height="1.3958333333333333in"}

**查看详情：**界面同待初审、待复审、待终审一致，仅回显，不可编辑，底部仅保留"退出""接收"按钮，点击"退出"，返回待接收任务列表，点击"接收"，案件流转至我的任务列表，返回待接收任务列表

#### 我的任务

![](./media/image56.png){width="5.756944444444445in"
height="3.3152777777777778in"}

(1) **查询条件：**职工姓名、单位名称、身份证号码可关键词搜索；受理时间按时间范围搜索

(2) **任务列表**

分为"我的任务"、"差错案件"2个tab，并展示各列表案件量

列表展示案件编号、职工姓名、身份证号码、单位名称、受理时间、案件状态、上一环节提交用户（含待复审/待初审退回）

其中案件状态支持筛选（同电子清单）

(3) **数据来源**

我的任务：待接收任务列表中，根据岗位角色接收任务；

差错案件：待复审、待终审案件审核后，不通过的，案件状态恢复为"待初审"

(4) **待初审案件详情**

![](./media/image57.png){width="5.7659722222222225in"
height="3.29375in"}

信息区分为上下2部分，上半部分展示申请信息、账单信息；下半部分展示理算结果，用于理算最终结果

**申请信息**：展示受理信息、诊断信息模块、操作记录内容，同受理界面，可编辑，规则一致

![](./media/image58.png){width="5.763194444444444in"
height="3.6305555555555555in"}

**账单信息**：展示账单信息，同受理界面账单信息模块，功能一致（含电子清单）

**理算结果：**

![](./media/image59.png){width="5.763194444444444in"
height="4.699305555555555in"}

责任列表：

- 按树形展示可理算责任，当前仅需展示工伤保险责任/工伤医疗待遇/工伤门诊就诊费用；工伤保险责任/工伤医疗待遇/工伤住院就诊费用

- 点击理算条，下方理算信息模块展示该理算条需理算内容

- 理算内容确认的，理算条变为"理算条名称（已理算）"，后方展示"删除理算"链接按钮，点击后删除该理算条理算信息

  理算信息

  ![](./media/image60.png){width="5.761805555555555in"
  height="2.4451388888888888in"}

  门诊理算条

  ![](./media/image61.png){width="5.761805555555555in"
  height="3.4944444444444445in"}

  住院理算条

<!-- -->

- 理算结果确认：

  - 未确认（保存）理算结果的：

    - 点击理算条，按当前账单信息进行理算

    - 点击确认，保存理算结果

    - 点击重算，按当前账单信息重新进行理算

  - 已确认（保存）理算结果的：

    - 责任列表中理算条变为"理算条名称（已理算）"，后方展示"删除理算"链接按钮，点击后删除理算结果，理算条恢复原状

    - 点击理算条，查看理算结果

    - 点击重算，按当前账单重新进行理算

    - 点击确认，按当前理算结果保存

- 理算条基础理算字段，住院理算条与门诊理算条一致，根据各账单的就诊类型，分别汇总计算

  ------------------ -------------------- --------------------------------- ----------------------------------------------------------------------------------------------------------------
  **字段**           **填充方式**         **计算公式**                      **说明**

  责任项目           回显                                                   根据选择的责任条，回显：工伤保险责任/工伤医疗待遇/工伤门诊就诊费用；工伤保险责任/工伤医疗待遇/工伤住院就诊费用

  历史赔付次数       回显                                                   回显该受伤害职工（通过身份证查询）该责任条的历史赔付次数，按案件量计算

  历史给付天数       回显                                                   仅住院费用责任条需回显，回显该受伤害职工（通过身份证查询）该责任条的历史赔付的"共计给付天数"合计天数

  历史赔付金额       回显                                                   回显该受伤害职工（通过身份证查询）该责任条的历史赔付金额合计结果

  住院伙食补助       自动选择，可修改                                       选择住院责任条的自动选择是；否则自动选择否，可修改

  补助标准           输入，数字                                             选择门诊责任条的，禁用

  共计给付天数       自动填充，不可修改   所有住院账单，住院天数合计        选择门诊责任条的，禁用，不计算

  住院伙食补助金额   自动填充             补助标准\*共计给付天数            选择门诊责任条的，禁用

  发票总金额         自动填充             所有住院/门诊发票总金额合计金额   选择住院责任条的，自动计算所有住院发票的账单总金额；选择门诊责任条的，同理

  发票张数           自动填充                                               选择住院责任条的，自动计算所有住院发票的账单数量；选择门诊责任条的，同理

  不可报销金额       自动填充                                               选择住院责任条的，自动计算所有住院发票的账单的审核扣减金额+非工伤扣减金额；选择门诊责任条的，同理

  可报销金额         自动填充                                               选择住院责任条的，自动计算所有住院发票的账单的合理费用；选择门诊责任条的，同理

  应付总金额         自动填充             可报销金额+住院伙食补助金额       选择住院责任条的，自动计算所有住院发票的账单的合理费用+住院伙食补助金额；选择门诊责任条的，同理
  ------------------ -------------------- --------------------------------- ----------------------------------------------------------------------------------------------------------------

- 核销明细：

  - 门诊：将所有门诊发票按费用项目分项汇总计算申报总金额、扣减总费用（审核扣减+非工伤扣减）、可报销金额

  -------------- -------------- --------------------- -----------------------------------------------------------
  **字段名称**   **填充方式**   **计算公式**          **说明**

  费用项目       自动填充                             所有门诊账单涉及到的各费用项目

  申报总金额     自动填充                             所有门诊账单涉及的费用项目对应金额总和

  扣减总费用     自动填充       审核扣减+非工伤扣减   所有门诊账单涉及的费用项目对应审核扣减金额+非工伤扣减总和

  应付金额       自动填充                             所有门诊账单涉及的费用项目对应合理费用总和
  -------------- -------------- --------------------- -----------------------------------------------------------

- 住院：

  - 按每次住院（每个住院账单）展示基本核算信息（后续可能会根据医院名称的维度统计）

  -------------- -------------- --------------------------------- -------------------------------------------
  **字段名称**   **填充方式**   **计算公式**                      **说明**

  治疗医院       自动填充                                         账单对应治疗医院名称

  发票张数       自动填充                                         默认1（后续可能会根据医院名称的维度统计）

  就诊天数       自动填充                                         该次就诊的住院天数

  发票费用金额   自动填充       账单总金额                        按单张发票计算

  可报销金额     自动填充                                         按单张发票计算合理费用总金额

  不可报销金额   自动填充       审核扣减总金额+非工伤扣减总金额   按单张发票计算

  住院伙食补助   自动填充       补助标准\*该次就诊的住院天数      

  应付总金额     自动填充       可报销金额+住院伙食补助金额       
  -------------- -------------- --------------------------------- -------------------------------------------

- 默认折叠，点击"查看明细"，查看该账单的各分项的申报总金额、扣减总费用（审核扣减+非工伤扣减）、可报销金额

  -------------- -------------- --------------------- ---------------------------------------------
  **字段名称**   **填充方式**   **计算公式**          **说明**

  费用项目       自动填充                             该账单涵盖的费用项目

  申报总金额     自动填充                             该账单各费用项目账单金额

  扣减总费用     自动填充       审核扣减+非工伤扣减   该账单各费用项目对应审核扣减金额+非工伤扣减

  应付金额       自动填充                             该账单合各费用项目对应合理费用
  -------------- -------------- --------------------- ---------------------------------------------

理算结果：

![](./media/image62.png){width="5.761805555555555in"
height="0.6930555555555555in"}

汇总各理算条的最终理算结果

  -------------------- -------------- -------------------------------------------- ---------------------------------------------------------------------
  **字段名称**         **填充方式**   **计算公式**                                 **说明**

  申报总金额           自动填充       发票总额+住院伙食补助金额                    各理算条理算的发票总额+住院伙食补助金额汇总合计（通用汇总计算字段）

  不可报销金额         自动填充                                                    各理算条理算的不可报销金额的汇总合计（通用汇总计算字段）

  应付总金额           自动填充                                                    各理算条理算的应付总金额的汇总合计（通用汇总计算字段）

  第三方赔付金额       输入，数字                                                  

  报销比例             输入，百分比                                                

  第三方实际赔付金额   自动生成       第三方赔付金额\*报销比例                     自动计算后生成

  实际支付金额         自动生成       申报总金额-不可报销金额-第三方实际赔付金额   计算公式中字段均为理算结果中对应字段

  备注                 输入，字符串                                                
  -------------------- -------------- -------------------------------------------- ---------------------------------------------------------------------

(5) **待复审/待终审案件详情**

    界面与待初审界面基本一致：

- 除案件信息中仅账单信息可编辑、理算结果区域可编辑，其余字段均不可编辑

- 理算结果区域新增审核意见模块

  **账单信息**

  ![](./media/image63.png){width="5.767361111111111in"
  height="2.6819444444444445in"}

  账单详情均可正常编辑，规则同受理、初审。

  编辑后，与初审数据提交不一致的，账单列表中展示差错图标；

  账单信息删除禁用

  新增账单展示绿色差错图标

- 电子清单详情：编辑后，与初审提交数据不一致的，字段内容红色高亮

- 电子清单：其余规则一致，仅新增、修改、删除交互不同；取消顶部批量操作按钮（一键可报销等）

  ![](./media/image64.png){width="5.7652777777777775in"
  height="4.542361111111111in"}

  - 默认回显初审提交时的清单内容，可编辑、删除、重新匹配三目录、查询诊疗项目

    ![](./media/image65.png){width="5.761805555555555in"
    height="0.1875in"}

  - 编辑、重新匹配三目录交互：

    - 编辑：

      ![](./media/image66.png){width="5.761805555555555in"
      height="0.1875in"}

      - 点击编辑图标，该行数据恢复可编辑状态，编辑图标更换为取消、确认图标

      - 编辑后，点击取消，恢复回显状态；点击确认，完成编辑（需验证必填项）

      - 编辑过程中，可重新匹配三目录

    - 重新匹配三目录：

      - 规则同受理、初审

      - 重新匹配成功后，视为编辑完成

    - 完成编辑/重新匹配三目录交互

      ![](./media/image67.png){width="5.761805555555555in"
      height="0.1875in"}

      - 编辑完成数据红色高亮

      - 编辑完成数据新增"恢复"操作按钮，点击恢复原始数据

      - 编辑完成数据支持删除整条数据，点击变为删除完成状态

  - 删除

    ![](./media/image68.png){width="5.761805555555555in"
    height="0.1875in"}

    - 原始数据、已编辑完成数据均可删除

    - 删除成功数据整条置灰，不再进入计算范围

    - 删除成功数据取消编辑、删除按钮，新增"恢复"操作按钮，点击恢复原始数据，进入计算范围

  - 新增

    - OCR新增、新增按钮规则与初审、受理时一致

    - 新增数据，可操作按钮同编辑状态

      ![](./media/image69.png){width="5.761805555555555in"
      height="0.1875in"}

    - 点击取消，清空该条已填充数据

    - 点击确认，新增数据成功，可进入计算范围，该条数据绿色高亮

      ![](./media/image70.png){width="5.761805555555555in"
      height="0.1875in"}

    - 已新增数据可删除，删除后清空该数据记录

  - 保存

    - 编辑状态数据自动编辑成功/新增成功

    - 编辑状态下，有数据的，必填项为空的，保存失败，提示"请补充序号1、序号2项目的必填信息"

    - 完成必填验证后，可成功保存

    - 退出时的保存提示同理

  - 提交：

    - 存在编辑状态数据：不可提交，提示"序号1、序号2编辑中"

    - 其他提交验证同初审、受理

      **理算结果区域**

<!-- -->

- 责任条与理算信息：点击责任条

  - 展示已保存的理算结果

  - 点击重算，按修改后的最新账单结果重新理算，保留初审提交的理算结果信息，重新理算结果在后方用（）展示并红字标识

  - 点击确认，保留初审提交数据并保存更新理算结果

    ![](./media/image71.png){width="5.761805555555555in"
    height="2.4451388888888888in"}

  - 更新后，理算结果与初审不一致的，状态变为"已修正"，点击"删除修正"，恢复初审理算结果

    ![](./media/image72.png){width="3.433333333333333in"
    height="0.6763888888888889in"}

<!-- -->

- 理算结果

  ![](./media/image73.png){width="5.761805555555555in"
  height="0.6930555555555555in"}

  - 各责任条理算结果更新后，最终理算结果同步更新

  - 更新后保留初审理算结果，重新理算结果在后方用（）展示并红字标识

- 审核意见：待复审/待终审案件在底部新增审核意见模块

  ![](./media/image74.png){width="5.761805555555555in"
  height="0.9208333333333333in"}

  - 审核意见：必填，单选

    - 账单信息修改、理算结果修改的，默认不通过，可修改

    - 案件信息未做任何改动的，默认通过，可修改

  - 意见描述：

    - 审核意见为通过的，非必填

    - 审核意见为不通过的，必填

      **复审/终审案件提交**

<!-- -->

- 复审提交

  - 审核意见通过的：

    - 待复审案件提交后，进入待接件任务列表，状态变更为"待终审"

    - 若案件账单、理算结果存在修改，则保留修改后交互（修改结果标红、删除置灰、新增标绿等），流转至待终审环节

    - 如有复审意见的，终审环节责任条上方红字展示复审意见

      ![](./media/image75.png){width="5.767361111111111in"
      height="1.2951388888888888in"}

  - 审核意见不通过的：

    - 待复审案件提交后，进入该案件初审用户的"我的任务"列表中"差错案件"列表，状态变更为"待初审"

    - 若案件账单、理算结果存在修改，则保留修改后交互（修改结果标红、删除置灰、新增标绿等），流转至差错案件审核列表

    - 如有复审意见的，差错案件（待初审）责任条上方展示复审意见

- 终审提交

  - 审核意见通过的

    - 提交后，案件审核完成

    - 已完成的，若案件详情存在修改，后端保留初审内容，前端修改内容替换初审结果，以修改结果为准

  - 审核意见不通过的

    - 待终审案件提交后，进入该案卷初审用的"我的任务"列表中"差错案件"列表，状态变更为"待初审"

    - 若案件账单、理算结果存在修改，则保留修改后交互（修改结果标红、删除置灰、新增标绿等），流转至差错案件审核列表

    - 如有复审意见的，差错案件（待初审）责任条上方展示复审意见

(6) **差错案件详情**

    查询界面与待复审/终审界面修改后基本一致：

- 案件详情均可编辑

- 字段被修改后，取消修改状态

- 理算结果区域，责任列表上方展示复审/终审意见

  **申请信息**

  可正常编辑，保存/提交后以最近编辑结果为准

  **账单信息**

<!-- -->

- 账单详情

  - 可正常编辑、新增、删除

  - 账单详情字段编辑后，取消红色高亮，取消被修改状态，保存/提交后以最近编辑结果为准

- 电子清单

  ![](./media/image76.png){width="5.7625in"
  height="2.252083333333333in"}

  - 所有字段恢复可编辑，已删除/修改/新增状态及高亮颜色保留

  - 复审/终审时，已删除/修改/新增清单数据

    - 被编辑后，取消被修改状态，保存/提交后以最近编辑结果为准

    - 未编辑的

      - 保存后保留原状态并保留期高亮颜色

      - 提交后：

        - 复审/终审已删除的，自动删除该数据，序号自动填补排序

        - 复审/终审已修改的，取消修改状态，以被修改结果为准

        - 复审/终审已新增的，取消新增状态，以新增结果为准

  - 批量操作按钮（可报销、不可报销等），与初审功能一致

  - 操作按钮中，新增"初审详情"图标按钮，点击查看初审意见详情：

    - 展示对比字段、初审意见、复审意见、终审意见结果

    - 复审/终审时未修改信息，该按钮禁用

      ![](./media/image77.png){width="5.7659722222222225in"
      height="3.361111111111111in"}

  - 其他交互与初审一致

    **理算结果**

    ![](./media/image78.png){width="5.7659722222222225in"
    height="1.4777777777777779in"}

<!-- -->

- 责任列表上方展示复审/终审审核意见

- 若初审/复审修改的理算条，状态与初审/复审修改后状态保持一致，可点击查看或删除修正

  ![](./media/image79.png){width="5.764583333333333in"
  height="2.5770833333333334in"}

- 点击重算，理算详情字段以重算结果为准

- 点击确认，以理算结果以当前为准，取消理算条修正状态

- 理算条删除修正/删除理算/确认理算信息结果后，理算结果同步修改

  ![](./media/image80.png){width="5.764583333333333in"
  height="0.7305555555555555in"}

  **差错案件保存**

  差错案件保存时，若存在未取消修改状态的字段，则保留其修改状态

  **差错案件提交**

- 差错案件提交时，若存在未取消修改状态的字段，自动取消其修改状态，以当前结果提交

- 已提交的差错案件，流转至待接收列表，恢复待复审状态，其案件详情与其他待初审状态一致

#### 清单生成、清单列表

![](./media/image81.png){width="5.759722222222222in"
height="1.7340277777777777in"}

清单类型：

- 待遇审核结果：

  - 模板：![](./media/image82.emf)

  - 一个案件多次就诊（同一就诊医院，且同一就诊方式，且同一就诊时间为一次就诊）的，按就诊次数分行展示，每行申请信息一致，就诊信息部就诊费用详情以每次就诊为准

- 待遇审核明细

  - 模板![](./media/image83.emf)

  - Zip包形式生成

    - 文件名：待遇审核明细导出时间范围

    - Zip包内，以姓名+身份证尾号（后6位）为文件名生成文件夹，文件夹内为生成的模板结果，文件名为医院名称+就诊方式+就诊开始-结束时间

    - 以理算条理算信息对标：

      - 门诊：汇总所有门诊费用结果，生成一份文件

      - 住院：按每张账单分别生成文件

        时间范围：

<!-- -->

- 以受理时间为准

- 仅导出已完成审核案件

#### 历史案件

规则同申报，可查询所有案件，含受理中、待初审、待复审、待终审、已完成案件

#### 医院支付统计

![](./media/image84.png){width="5.766666666666667in"
height="3.1590277777777778in"}

**数据来源**：所有已办结案件的审核结果，以医院名称为维度统计

**查询条件**：

- 机构名称：支持关键词搜索选择

- 是否协议机构：单选

- 所在区域：按医院所在区域，省市区任意一级选择筛选

- 统计时间范围：按受理时间为准

  **查询结果**

- 顶部展示所查询结果中，所有查询结果中所有医院的共计就诊人数、就诊金额（发票总金额）、门诊就诊金额、住院就诊金额、合计住院天数

- 列表查询维度

+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 列表字段                          | 说明                                                                                             |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 机构名称                          | 就诊医院名称                                                                                     |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 是否协议机构                      | 是否协议机构（状态）                                                                             |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 所在区域                          | 省市区                                                                                           |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 就诊人数                          | 按职工身份证号统计，仅人数，非人次                                                               |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 就诊金额（元）                    | 门诊+住院所有发票金额                                                                            |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 门诊就诊金额                      | 门诊发票金额                                                                                     |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 住院就诊金额                      | 住院发票金额                                                                                     |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 门诊核赔金额                      | 门诊发票金额-不可报销金额                                                                        |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 住院核赔金额                      | 住院发票金额-不可报销金额                                                                        |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 合计核赔金额                      | 发票金额-不可报销金额                                                                            |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 住院伙食补助                      | 住院伙食补助金额之和                                                                             |
+-----------------------------------+--------------------------------------------------------------------------------------------------+
| 实际支付金额                      | 每个案件中，实际支付金额分摊至该就诊医院金额                                                     |
|                                   |                                                                                                  |
|                                   | 单个案件中该医院分摊实际支付金额=该医院合理费用/可报销金额（理算结果）\*实际支付金额（理算结果） |
+-----------------------------------+--------------------------------------------------------------------------------------------------+

- 统计明细

  ![](./media/image85.png){width="5.766666666666667in"
  height="3.1590277777777778in"}

  - 查询条件

    - 职工姓名：关键词查询

    - 身份证号码：关键词查询

    - 用人单位名称；关键词查询

    - 统计区间：时间范围查询（受理时间）

  - 列表明细：按职工维度展示

- 总额明细

  ![](./media/image86.png){width="5.766666666666667in"
  height="3.1590277777777778in"}

  - 票据信息：展示查询范围内，所有票据、住院票据、门诊票据的总数量、正常票据数量（合理费用≠0的）、核减票据数量（合理费用=0的）

  - 金额信息：所有票据、住院票据、门诊票据的总金额、报销金额（合理费用）、核减金额（不可报销费用）以及住院的天数、住院伙食费总金额、住院+门诊的非工伤扣减费用

## 字典

(1) 新增"限价医院等级"字典

  ------------------ ----------------- ----------------- -----------------
       **名称**        **nodecode**        **等级**        **是否可用**

     一级甲等省级            1                 1               TRUE

     一级甲等市级            2                 1               TRUE

     一级甲等县级            3                 1               TRUE

     一级甲等民营            4                 1               TRUE

     一级甲等基层            5                 1               TRUE

     一级乙等省级            6                 1               TRUE

     一级乙等市级            7                 1               TRUE

     一级乙等县级            8                 1               TRUE

     一级乙等民营            9                 1               TRUE

     一级乙等基层           10                 1               TRUE

   一级乙等以下市级         11                 1               TRUE

     二级甲等省级           12                 1               TRUE

     二级甲等市级           13                 1               TRUE

     二级甲等县级           14                 1               TRUE

     二级甲等民营           15                 1               TRUE

     二级甲等基层           16                 1               TRUE

     二级乙等省级           17                 1               TRUE

     二级乙等市级           18                 1               TRUE

     二级乙等县级           19                 1               TRUE

     二级乙等民营           20                 1               TRUE

     二级乙等基层           21                 1               TRUE

   二级乙等以下市级         22                 1               TRUE

     三级甲等省级           23                 1               TRUE

     三级甲等市级           24                 1               TRUE

     三级甲等县级           25                 1               TRUE

     三级甲等民营           26                 1               TRUE

     三级甲等基层           27                 1               TRUE

     三级乙等省级           28                 1               TRUE

     三级乙等市级           29                 1               TRUE

     三级乙等县级           30                 1               TRUE

     三级乙等民营           31                 1               TRUE

     三级乙等基层           32                 1               TRUE

   三级乙等以下市级         33                 1               TRUE
  ------------------ ----------------- ----------------- -----------------

(2) 新增"费用项目"字典

  ----------------- ----------------- ----------------- -----------------
      **名称**        **nodecode**        **等级**        **是否可用**

       床位费             1001                1               TRUE

       药品费             1004                1               TRUE

       检查费             1010                1               TRUE

       治疗费             1007                1               TRUE

       材料费             1011                1               TRUE

        血费              1014                1               TRUE

       未匹配             0000                1               TRUE
  ----------------- ----------------- ----------------- -----------------

# 非功能需求
