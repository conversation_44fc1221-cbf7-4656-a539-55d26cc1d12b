package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "关联事故输出对象")
@Data
public class RelateCasesOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty(value = "案件状态（Submitted: '待申报' Applying: '申报中' Applied: '已申报' Accepting: '待受理' Identifying: '认定中' Classifying: '鉴定中' Cancelled: '已撤销' Canceling: '撤销中' Rejected: '已退回' Done: '已办结',）")
    private String status;

    @ApiModelProperty(value = "受害职工姓名")
    private String name;

    @ApiModelProperty(value = "受害职工单位id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long organizationId;

    @ApiModelProperty(value = "受害职工单位名称")
    private String organization;

    @ApiModelProperty(value = "伤害部位")
    private List<String> injuredPart;

}
