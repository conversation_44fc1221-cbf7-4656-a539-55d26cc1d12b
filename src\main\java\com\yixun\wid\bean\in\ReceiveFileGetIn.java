package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ReceiveFileGetIn {

    @ApiModelProperty("环节 申报，补充资料，认定中止，撤销")
    private String step;

    @ApiModelProperty("职工姓名")
    private String name;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("事故发生时间开始")
    private Date startDate;

    @ApiModelProperty("事故发生时间结束")
    private Date endDate;

}
