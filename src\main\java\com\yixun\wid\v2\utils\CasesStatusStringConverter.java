package com.yixun.wid.v2.utils;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.converters.string.StringStringConverter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import com.yixun.wid.entity.em.CasesStatus;

public class CasesStatusStringConverter extends StringStringConverter {

	@Override
	public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
		GlobalConfiguration globalConfiguration) {
		CasesStatus casesStatus = CasesStatus.valueOf(value);
		return new WriteCellData<>(casesStatus.getMark());
	}

}
