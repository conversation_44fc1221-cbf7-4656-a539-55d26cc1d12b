package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.CorporationGetIn;
import com.yixun.wid.entity.Corporation;

import java.util.List;

public interface CorporationService {

    Corporation getByUserId(Long userId);

    Corporation recognize(String businessLicenseUrl);

    void update(Corporation corporation);

    List<Corporation> getCorporationList(CorporationGetIn corporationGetIn, CommonPage commonPage);

    Corporation getById(Long corporationId);

    List<Corporation> getByIds(List<Long> corpIds);
}
