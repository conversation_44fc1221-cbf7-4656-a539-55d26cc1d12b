package com.yixun.wid.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.yixun.wid.entity.MessageRecord;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.MessageRecordService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.SnGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MessageRecordServiceImpl implements MessageRecordService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private UserService userService;

    @Value("${message.msRecordFlag}")
    private Integer msRecordFlag;

    @Override
    public void save(MessageRecord messageRecord) {
        try {
            if (msRecordFlag == 1) {
                if (messageRecord.getId() == null) {
                    messageRecord.setId(SnGeneratorUtil.getId());
                }
                if (messageRecord.getUserId() != null) {
                    //查询用户名称
                    User user = userService.getUserById(messageRecord.getUserId());
                    if (user != null) {
                        messageRecord.setUserName(user.getRealName());
                    }
                }
                messageRecord.setSendTime(new Date());
                mongoTemplate.save(messageRecord);
            }
        } catch (Exception e) {
            log.error("保存消息记录异常", e);
        }
    }

    @Override
    public void saveTrueRecord(Long userId, String relationship, String msgType, String msgContent, Integer busType, String businessId) {
        try {
            if (msRecordFlag == 1) {

                if (ObjectUtil.isNull(userId)) {
                    log.warn("用户id为空，跳过消息发送：{}, {}, {}, {}, {}", relationship, msgType, msgContent, busType, businessId);
                    return;
                }

                MessageRecord messageRecord = new MessageRecord();
                messageRecord.setUserId(userId);
                if ("本人".equals(relationship)) {
                    messageRecord.setPersonType("职工");
                } else if ("其他".equals(relationship)) {
                    messageRecord.setPersonType("报备人");
                } else {
                    messageRecord.setPersonType(relationship);
                }
                messageRecord.setMsgType(msgType);
                messageRecord.setMsgContent(msgContent);
                messageRecord.setBusType(busType);
                messageRecord.setBusinessId(businessId);
                this.save(messageRecord);
            }
        } catch (Exception e) {
            log.error("保存真实消息记录异常", e);
        }
    }

    @Override
    public List<MessageRecord> getRecordByBus(String businessId, Integer busType) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("businessId").is(businessId));
            query.addCriteria(Criteria.where("busType").is(busType));
            List<MessageRecord> messageRecords = mongoTemplate.find(query, MessageRecord.class);
            return messageRecords;
        } catch (Exception e) {
            log.error("根据业务获取消息记录异常", e);
            return null;
        }
    }
}
