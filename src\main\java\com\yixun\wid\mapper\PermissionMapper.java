package com.yixun.wid.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yixun.wid.entity.Permission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PermissionMapper extends BaseMapper<Permission> {

    void initPermission(List<Permission> permissionList);

    List<Permission> getPermissionListByRole(@Param("role") String role);

    List<Permission> getPermissionListByRoleList(@Param("roleList") List<String> roleList);
}
