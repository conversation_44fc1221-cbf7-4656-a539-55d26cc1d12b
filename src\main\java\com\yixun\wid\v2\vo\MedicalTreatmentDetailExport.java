package com.yixun.wid.v2.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 医疗待遇审核明细导出VO
 */
@Data
public class MedicalTreatmentDetailExport {
    
    /**
     * 职工姓名
     */
    private String workerName;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 用人单位名称
     */
    private String organization;
    
    /**
     * 事故日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date accidentDate;
    
    /**
     * 治疗医院
     */
    private String hospital;
    
    /**
     * 就诊类型：住院、门诊
     */
    private String treatmentType;
    
    /**
     * 就诊开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date visitStartDate;
    
    /**
     * 就诊结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date visitEndDate;
    
    /**
     * 就诊天数
     */
    private Integer totalPayDays;
    
    /**
     * 费用类别
     */
    private String feeCategory;
    
    /**
     * 伙食补助金额（分）
     */
    private Long foodAllowanceAmountInCent;
    
    /**
     * 申报总金额（分）
     */
    private Long totalAmountInCent;
    
    /**
     * 可报销金额（分）
     */
    private Long reimbursableAmountInCent;
    
    /**
     * 不可报销金额（分）
     */
    private Long nonReimbursableAmountInCent;
    
    /**
     * 第三方支付金额（分）
     */
    private Long thirdPartyPayAmountInCent;
    
    /**
     * 实际支付金额（分）
     */
    private Long actualPayAmountInCent;
    
} 