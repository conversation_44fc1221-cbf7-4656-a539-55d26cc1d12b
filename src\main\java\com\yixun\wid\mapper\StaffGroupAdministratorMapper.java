package com.yixun.wid.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yixun.wid.bean.out.GroupStaffOut;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupAdministrator;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StaffGroupAdministratorMapper extends BaseMapper<StaffGroupAdministrator> {

    List<GroupStaffOut> getGroupStaffList(@Param("staffGroupId") Long staffGroupId);

    List<GroupStaffOut> getAllGroupStaffList();

    StaffGroup getGroupNameByUser(@Param("administratorId") Long administratorId);
}
