<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.wid.mapper.AdministratorMapper" >

    <select id="getAdministratorPageList" resultType="com.yixun.wid.bean.out.AdministratorOut">
        SELECT a.*, g.group_name, g.id as staffGroupId
            FROM administrator as a
                LEFT JOIN staff_group_administrator as ga on ga.administrator_id=a.id
                LEFT JOIN staff_group as g on ga.staff_group_id=g.id
        <where>
            <if test="username!=null">
                and (a.username LIKE concat('%',#{username},'%'))
            </if>
            <if test="email!=null">
                and (a.email LIKE concat('%',#{email},'%'))
            </if>
            <if test="realName!=null">
                and (a.real_name LIKE concat('%',#{realName},'%'))
            </if>
            <if test="phone!=null">
                and (a.phone LIKE concat('%',#{phone},'%'))
            </if>
            <if test="type!=null">
                and (a.type = #{type})
            </if>
            <if test="government!=null">
                and (a.government = #{government})
            </if>
        </where>

    </select>
</mapper>