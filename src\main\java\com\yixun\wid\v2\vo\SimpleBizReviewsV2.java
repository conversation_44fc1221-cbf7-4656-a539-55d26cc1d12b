package com.yixun.wid.v2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lzhpo.sensitive.SensitiveStrategy;
import com.lzhpo.sensitive.annocation.Sensitive;
import com.yixun.wid.v2.utils.ReviewsLevelDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class SimpleBizReviewsV2 {

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 评价等级
	 */
	@JsonDeserialize(using = ReviewsLevelDeserializer.class)
	private String reviewsLevel;

	/**
	 * 评价内容
	 */
	private String reviewsContent;

	/**
	 * 评价时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date reviewsTime;

	/**
	 * 评价用户
	 */
	@Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
	private String reviewsUserName;

	/**
	 * 评价用户联系方式
	 */
	@Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
	private String reviewsUserPhone;

}
