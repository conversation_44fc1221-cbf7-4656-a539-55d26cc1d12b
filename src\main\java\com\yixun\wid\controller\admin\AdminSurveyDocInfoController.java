package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.DocIn;
import com.yixun.wid.entity.SurveyDocInfo;
import com.yixun.wid.service.SurveyDocInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "admin档案查询")
@RestController
@RequestMapping(value = "/admin/SurveyDocInfo")
public class AdminSurveyDocInfoController {

    @Resource
    private SurveyDocInfoService surveyDocInfoService;

    @GetMapping("/getList")
    @ApiOperation("获取档案列表")
    public CommonResult<List<SurveyDocInfo>> getList(DocIn docIn, CommonPage commonPage) {
        List<SurveyDocInfo> surveyDocList = surveyDocInfoService.getSurveyDocList(docIn, commonPage);
        return CommonResult.successPageData(surveyDocList, commonPage);
    }

}
