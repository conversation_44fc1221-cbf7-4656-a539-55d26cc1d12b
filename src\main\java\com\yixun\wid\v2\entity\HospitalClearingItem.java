package com.yixun.wid.v2.entity;

import org.springframework.data.annotation.Transient;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 核销明细-住院项目
 * 按每次住院（每个住院账单）展示基本核算信息
 */
@Data
public class HospitalClearingItem {
    
    /**
     * 治疗医院（账单对应治疗医院名称）
     */
    private String hospital;
    
    /**
     * 医院ID
     */
    private String hospitalId;
    
    /**
     * 发票张数（默认1，后续可能会根据医院名称的维度统计）
     */
    private Integer invoiceCount;
    
    /**
     * 就诊天数（该次就诊的住院天数）
     */
    private Integer hospitalDays;
    
    /**
     * 发票费用金额（分，数据库存储，按单张发票计算账单总金额）
     */
    private Integer invoiceAmountInCent;
    /**
     * 发票费用金额（元，接口交互）
     */
    @Transient
    private BigDecimal invoiceAmount;
    
    /**
     * 可报销金额（分，数据库存储，按单张发票计算合理费用总金额）
     */
    private Integer reimbursableAmountInCent;
    /**
     * 可报销金额（元，接口交互）
     */
    @Transient
    private BigDecimal reimbursableAmount;
    
    /**
     * 不可报销金额（分，数据库存储，按单张发票计算审核扣减总金额+非工伤扣减总金额）
     */
    private Integer nonReimbursableAmountInCent;
    /**
     * 不可报销金额（元，接口交互）
     */
    @Transient
    private BigDecimal nonReimbursableAmount;
    
    /**
     * 住院伙食补助（分，数据库存储，补助标准*该次就诊的住院天数）
     */
    private Integer hospitalFoodAllowanceInCent;
    /**
     * 住院伙食补助（元，接口交互）
     */
    @Transient
    private BigDecimal hospitalFoodAllowance;
    
    /**
     * 应付总金额（分，数据库存储，可报销金额+住院伙食补助金额）
     */
    private Integer totalPayableAmountInCent;
    /**
     * 应付总金额（元，接口交互）
     */
    @Transient
    private BigDecimal totalPayableAmount;
    
    /**
     * 关联的账单明细列表
     * 默认折叠，点击"查看明细"，查看该账单的各分项
     */
    private List<HospitalClearingDetailItem> detailItems;
    
    // 元分转换方法
    public BigDecimal getInvoiceAmount() {
        if (invoiceAmountInCent == null) {
            return null;
        }
        return new BigDecimal(invoiceAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setInvoiceAmount(BigDecimal amount) {
        if (amount == null) {
            this.invoiceAmountInCent = null;
        } else {
            this.invoiceAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getReimbursableAmount() {
        if (reimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(reimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.reimbursableAmountInCent = null;
        } else {
            this.reimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setNonReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.nonReimbursableAmountInCent = null;
        } else {
            this.nonReimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getHospitalFoodAllowance() {
        if (hospitalFoodAllowanceInCent == null) {
            return null;
        }
        return new BigDecimal(hospitalFoodAllowanceInCent).divide(new BigDecimal(100));
    }
    
    public void setHospitalFoodAllowance(BigDecimal amount) {
        if (amount == null) {
            this.hospitalFoodAllowanceInCent = null;
        } else {
            this.hospitalFoodAllowanceInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalPayableAmount() {
        if (totalPayableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalPayableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalPayableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalPayableAmountInCent = null;
        } else {
            this.totalPayableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    /**
     * 计算应付总金额
     * 根据可报销金额和住院伙食补助金额计算应付总金额
     */
    public void calculateTotalPayableAmount() {
        if (reimbursableAmountInCent == null) {
            reimbursableAmountInCent = 0;
        }
        
        if (hospitalFoodAllowanceInCent == null) {
            hospitalFoodAllowanceInCent = 0;
        }
        
        totalPayableAmountInCent = reimbursableAmountInCent + hospitalFoodAllowanceInCent;
    }
} 