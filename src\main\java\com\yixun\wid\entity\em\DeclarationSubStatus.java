package com.yixun.wid.entity.em;

public enum DeclarationSubStatus {

    NoNeedApply("无需申报"),
    FurtherInfo("补充资料"),
    Auditing("审核中"),
    SuspendAuditing("时效中止提交审核中"),
    Canceling("撤销申请"),
    WaitMaterialSubmit("待实物提交"),
    Suspended("时效终止"),
    NotAccept("不予受理"),
    NotIdentify("不予认定");

    private String mark;

    DeclarationSubStatus(String mark ) {
        this.mark = mark;
    }

    public String getMark() {
        return mark;
    }
}
