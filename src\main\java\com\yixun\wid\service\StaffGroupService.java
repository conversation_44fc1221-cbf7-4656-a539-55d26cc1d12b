package com.yixun.wid.service;

import com.yixun.wid.bean.in.StaffGroupSearchIn;
import com.yixun.wid.bean.out.GroupStaffOut;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupAdministrator;

import java.util.List;

public interface StaffGroupService {

    void insert(StaffGroup staffGroup);

    void update(StaffGroup staffGroup);

    StaffGroup getById(Long staffGroupId);

    StaffGroup getGroupNameByUser(Long administratorId);

    List<StaffGroup> getListByParentId(Long parentId);

    StaffGroupAdministrator getGroupByAdminId(Long administratorId);

    void insertStaffGroup(StaffGroupAdministrator staffGroupAdministrator);

    void updateGroupForAdmin(StaffGroupAdministrator staffGroupAdministrator);

    void addStaffGroup(Long staffGroupId, List<Long> administratorIdList);

    void deleteStaffGroup(Long staffGroupId, List<Long> administratorIdList);

    List<StaffGroupAdministrator> getStaffInGroupList(Long staffGroupId);

    void delete(StaffGroup staffGroup);

    List<GroupStaffOut> getGroupStaffList(Long staffGroupId);

    List<GroupStaffOut> getAllGroupStaffList();

    List<StaffGroup> getAllGroupList();

    List<Long> getOwnAndSubGroupList(Long administratorId);

    List<StaffGroup> getAllGroupListBySearch(StaffGroupSearchIn staffGroupSearchIn);

}
