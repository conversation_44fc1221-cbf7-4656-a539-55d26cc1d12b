package com.yixun.wid.v2.vo;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.v2.utils.BooleanStringConverter;
import com.yixun.wid.v2.utils.CasesStatusStringConverter;
import com.yixun.wid.v2.utils.ListStringConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CasesExport {

	private Long id;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	@ApiModelProperty(value = "案件号")
	private String caseSn;

	@ApiModelProperty(value = "上报方式")
	private String submitWay;

	//报案人信息
	@ApiModelProperty(value = "上报人的用户id")
	private Long userId;

	@ApiModelProperty(value = "与当事人关系")
	private String relationship;

	@ApiModelProperty(value = "与当事人关系详情")
	private String relationshipDetail;

	@ApiModelProperty(value = "报案人姓名")
	private String reportName;

	@ApiModelProperty(value = "报案人手机")
	private String reportPhone;

	@ApiModelProperty(value = "报案状态")
	@ExcelProperty(converter = CasesStatusStringConverter.class)
	private String status;

	//工伤当事人参保信息
	@ApiModelProperty(value = "是否参保")
	private Boolean hasInsurance;

	@ApiModelProperty(value = "参保地")
	private String insuranceAddress;

	//工伤当事人信息
	@ApiModelProperty(value = "职工姓名")
	private String name;

	@ApiModelProperty(value = "职工性别")
	private String gender;

	@ApiModelProperty(value = "出生年月日")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date birthdayDate;

	@ApiModelProperty(value = "身份证")
	private String idCard;

	@ApiModelProperty(value = "身份证地址")
	private String idCardAddr;

	@ApiModelProperty(value = "手机号码")
	private String phone;

	@ApiModelProperty("单位id")
	private Long organizationId;

	@ApiModelProperty("单位上报者id")
	private Long orgSubmitterId;

	@ApiModelProperty("单位名称")
	private String organization;

	@ApiModelProperty(value = "单位信用代码")
	private String creditCode;

	//职工工作信息
	@ApiModelProperty(value = "工作岗位")
	private List position;

	@ApiModelProperty(value = "参加工作时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date firstWorkDate;

	@ApiModelProperty(value = "单位区域")
	private List organizationRegion;

	@ApiModelProperty(value = "单位地址")
	private String organizationAddr;

	@ApiModelProperty("单位地址经度")
	private Double organizationLongitude;

	@ApiModelProperty("单位地址纬度")
	private Double organizationLatitude;

	@ApiModelProperty(value = "单位邮编")
	private String zipCode;

	//工伤事故信息
	@ApiModelProperty(value = "伤害部位")
	@ExcelProperty(converter = ListStringConverter.class)
	private List injuredPart;

	@ApiModelProperty(value = "疾病名称")
	private String diseaseName;

	@ApiModelProperty(value = "事故时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date accidentTime;

	@ApiModelProperty(value = "事故区域")
	private List accidentRegion;

	@ApiModelProperty(value = "事故地点")
	private String accidentAddress;

	@ApiModelProperty(value = "事故地点经度")
	private Double longitude;

	@ApiModelProperty(value = "事故地点纬度")
	private Double latitude;

	@ApiModelProperty(value = "报案区域")
	private List reportRegion;

	@ApiModelProperty(value = "报案地点")
	private String reportAddress;

	@ApiModelProperty(value = "报案地点经度")
	private Double reportLongitude;

	@ApiModelProperty(value = "报案地点纬度")
	private Double reportLatitude;

	@ApiModelProperty(value = "事故经过")
	private String accidentDetail;

	@ApiModelProperty(value = "事故原因")
	private String accidentCause;

	@ApiModelProperty(value = "事发时从事的工作")
	private String workingOfAccident;

	@ApiModelProperty(value = "补充说明")
	private String supplementary;

	@ApiModelProperty(value = "涉及人数")
	private String involvedNumber;

	//是否职业病相关
	@ApiModelProperty(value = "是否职业病相关")
	@ExcelProperty(converter = BooleanStringConverter.class)
	private Boolean isOccupDiseaseRelated;

	@ApiModelProperty(value = "职业病名称")
	private String occupDiseaseName;

	@ApiModelProperty(value = "接触职业病危害岗位")
	private String occupDiseasePosition;

	@ApiModelProperty(value = "接触职业病危害时间")
	private String occupDiseaseDate;

	@ApiModelProperty(value = "职业病诊断证明或鉴定书")
	private List occupDiseaseDiagnose;

	//事故相关材料
	@ApiModelProperty(value = "现场照片")
	private List pictures;

	@ApiModelProperty(value = "现场视频")
	private List videos;

	@ApiModelProperty(value = "受伤害部位图片")
	private List injuredPartPics;

	@ApiModelProperty(value = "职工工伤事故情况快报表")
	private List reportFormPics;

	//实物提交方式
	@ApiModelProperty(value = "实物提交方式")
	private String materialSubmitWay;

	@ApiModelProperty(value = "是否无需申报")
	private Boolean isNoNeedApply;

	@ApiModelProperty(value = "无需申报原因")
	private String noNeedReason;

	@ApiModelProperty(value = "无需申报其他原因")
	private String noNeedReasonOther;

	@ApiModelProperty("案件申请时间")
	private Date submitTime;

}
