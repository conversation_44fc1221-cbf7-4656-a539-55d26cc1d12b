package com.yixun.wid.v2.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 账单信息与明细的视图对象
 */
@Data
public class BillingInfoWithDetailsVO {
    
    /**
     * 账单信息
     */
    private BillingInfo billingInfo;
    
    /**
     * 账单明细列表
     */
    private List<BillingDetail> billingDetails;
} 