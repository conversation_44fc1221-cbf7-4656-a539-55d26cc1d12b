package com.yixun.wid.v2.vo;

import lombok.Data;

/**
 * 医院支付统计VO
 */
@Data
public class HospitalPaymentStatVO {

    /**
     * 机构名称
     */
    private String hospitalName;
    
    /**
     * 是否协议机构
     */
    private Boolean isAgreementHospital;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区域
     */
    private String district;
    
    /**
     * 所在区域 (省/市/区)
     */
    private String area;
    
    /**
     * 就诊人数
     */
    private Integer patientCount;
    
    /**
     * 就诊金额（元）
     */
    private Double totalInvoiceAmount;
    
    /**
     * 门诊就诊金额（元）
     */
    private Double outpatientInvoiceAmount;
    
    /**
     * 住院就诊金额（元）
     */
    private Double inpatientInvoiceAmount;
    
    /**
     * 门诊核赔金额（元）
     */
    private Double outpatientReimbursableAmount;
    
    /**
     * 住院核赔金额（元）
     */
    private Double inpatientReimbursableAmount;
    
    /**
     * 合计核赔金额（元）
     */
    private Double totalReimbursableAmount;
    
    /**
     * 住院伙食补助（元）
     */
    private Double totalHospitalFoodAllowance;
    
    /**
     * 住院天数
     */
    private Integer totalHospitalDays;
    
    /**
     * 实际支付金额（元）
     */
    private Double totalActualPayAmount;
    
    /**
     * 获取所在区域
     * @return 所在区域字符串，如"四川省/成都市/锦江区"
     */
    public String getArea() {
        StringBuilder sb = new StringBuilder();
        if (province != null && !province.isEmpty()) {
            sb.append(province);
        }
        if (city != null && !city.isEmpty()) {
            if (sb.length() > 0) sb.append("/");
            sb.append(city);
        }
        if (district != null && !district.isEmpty()) {
            if (sb.length() > 0) sb.append("/");
            sb.append(district);
        }
        return sb.toString();
    }
} 