package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WeixinMiniLoginPreUserIn {

    @ApiModelProperty(value="微信openId", required = true)
    private String openId;
    @ApiModelProperty(value="微信iv", required = true)
    private String iv;
    @ApiModelProperty(value="微信encryptedData", required = true)
    private String encryptedData;
    @ApiModelProperty(value="机器码", required = true)
    private String clientId;

}
