package com.yixun.wid.controller.adminApi;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CasesGetIn;
import com.yixun.wid.bean.out.CasesLogOut;
import com.yixun.wid.bean.out.CasesOut;
import com.yixun.wid.bean.out.RelateCasesOut;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.CasesLog;
import com.yixun.wid.entity.SurveyDocInfo;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.SurveyDocInfoService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "adminApi用户报案")
@RestController
@RequestMapping(value = "/adminApi/cases")
public class AdminApiCasesController {

    @Resource
    private CasesService casesService;

    @Resource
    private SurveyDocInfoService surveyDocInfoService;

    @GetMapping("/getList")
    @ApiOperation("获取报案列表")
    public CommonResult<List<CasesOut>> getList(CasesGetIn casesGetIn, CommonPage commonPage) {
        List<Cases> casesList = casesService.getCasesList(casesGetIn, commonPage);
        List<CasesOut> outList = BeanUtils.copyToOutList(casesList, CasesOut.class);
        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/getLogList")
    @ApiOperation("获取报案操作日志")
    public CommonResult<List<CasesLogOut>> getLogList(Long casesId, CommonPage commonPage) {

        List<CasesLog> casesList = casesService.getCasesLogList(casesId, commonPage);
        List<CasesLogOut> outList = BeanUtils.copyToOutList(casesList, CasesLogOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{casesId}")
    @ApiOperation("获取报案详情")
    public CommonResult<CasesOut> getDetail(@PathVariable("casesId") Long casesId) {

        Cases cases = casesService.getById(casesId);
        if (cases == null) {
            throw new DataErrorException("该报案不存在");
        }
        CasesOut out = new CasesOut();
        BeanUtils.copyProperties(cases, out);

        return CommonResult.successData(out);
    }

    @GetMapping("/getRelateCase")
    @ApiOperation("获取关联事故数据")
    public CommonResult<List<RelateCasesOut>> getRelateCase(Long casesId) {
        List<RelateCasesOut> relateCase = casesService.getRelateCase(casesId);
        return CommonResult.successData(relateCase);
    }

    @PostMapping("/saveSurveyDocInfo")
    @ApiOperation("保存档案数据")
    public CommonResult<String> saveSurveyDocInfo(@RequestBody SurveyDocInfo surveyDocInfo) {
        surveyDocInfoService.saveSurveyDocInfo(surveyDocInfo);
        return CommonResult.successResult("操作成功");
    }

    @PostMapping("/batchSaveSurveyDocInfo")
    @ApiOperation("批量保存档案数据")
    public CommonResult<String> saveSurveyDocInfo(@RequestBody List<SurveyDocInfo> surveyDocInfos) {
        surveyDocInfoService.batchSaveSurveyDocInfo(surveyDocInfos);
        return CommonResult.successResult("操作成功");
    }

}
