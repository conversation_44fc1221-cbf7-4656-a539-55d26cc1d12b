package com.yixun.wid.v2.controller;

import com.yixun.wid.v2.entity.ThreeCatalogue;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ThreeCatalogue有效性筛选测试类
 * 测试Controller中的有效性筛选逻辑
 */
public class ThreeCatalogueEffectiveFilterTest {

    /**
     * 获取指定天数前/后的日期
     * @param days 天数，正数表示未来，负数表示过去
     * @return 日期
     */
    private Date getDateByDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    /**
     * 创建测试数据
     * @return 测试数据列表
     */
    private List<ThreeCatalogue> createTestData() {
        List<ThreeCatalogue> list = new ArrayList<>();

        // 1. 有效的药品（长期有效）
        ThreeCatalogue validDrug = new ThreeCatalogue();
        validDrug.setId(1L);
        validDrug.setProjectName("有效药品");
        validDrug.setLevel("甲");
        validDrug.setType("药品");
        validDrug.setStartDate(getDateByDays(-30)); // 30天前开始
        // 无结束日期，长期有效
        list.add(validDrug);

        // 2. 有效的诊疗项目（在有效期内）
        ThreeCatalogue validDiagnosis = new ThreeCatalogue();
        validDiagnosis.setId(2L);
        validDiagnosis.setProjectName("有效诊疗");
        validDiagnosis.setLevel("乙");
        validDiagnosis.setType("诊疗");
        validDiagnosis.setStartDate(getDateByDays(-10)); // 10天前开始
        validDiagnosis.setEndDate(getDateByDays(20));    // 20天后结束
        list.add(validDiagnosis);

        // 3. 无效的医疗器械（已过期）
        ThreeCatalogue invalidDevice = new ThreeCatalogue();
        invalidDevice.setId(3L);
        invalidDevice.setProjectName("过期器械");
        invalidDevice.setLevel("丙");
        invalidDevice.setType("医疗器械");
        invalidDevice.setStartDate(getDateByDays(-60)); // 60天前开始
        invalidDevice.setEndDate(getDateByDays(-5));    // 5天前结束
        list.add(invalidDevice);

        // 4. 无效的药品（未来生效）
        ThreeCatalogue futureValidDrug = new ThreeCatalogue();
        futureValidDrug.setId(4L);
        futureValidDrug.setProjectName("未来药品");
        futureValidDrug.setLevel("甲");
        futureValidDrug.setType("药品");
        futureValidDrug.setStartDate(getDateByDays(10)); // 10天后开始
        futureValidDrug.setEndDate(getDateByDays(50));   // 50天后结束
        list.add(futureValidDrug);

        // 5. 永久有效的项目（无开始和结束日期）
        ThreeCatalogue permanentValid = new ThreeCatalogue();
        permanentValid.setId(5L);
        permanentValid.setProjectName("永久有效项目");
        permanentValid.setLevel("甲");
        permanentValid.setType("诊疗");
        // 无开始和结束日期
        list.add(permanentValid);

        return list;
    }

    @Test
    public void testFilterEffectiveTrue() {
        // 测试筛选有效的项目
        List<ThreeCatalogue> testData = createTestData();
        
        // 模拟Controller中的筛选逻辑
        Boolean effective = true;
        List<ThreeCatalogue> filteredList = testData.stream()
                .filter(catalogue -> effective.equals(catalogue.getEffective()))
                .collect(Collectors.toList());

        // 验证结果
        assert filteredList.size() == 3; // 应该有3个有效项目
        
        // 验证具体的有效项目
        List<String> validProjectNames = filteredList.stream()
                .map(ThreeCatalogue::getProjectName)
                .collect(Collectors.toList());
        
        assert validProjectNames.contains("有效药品");
        assert validProjectNames.contains("有效诊疗");
        assert validProjectNames.contains("永久有效项目");
        
        System.out.println("测试通过：筛选有效项目 - 找到 " + filteredList.size() + " 个有效项目");
        filteredList.forEach(item -> 
            System.out.println("  - " + item.getProjectName() + " (有效性: " + item.getEffective() + ")")
        );
    }

    @Test
    public void testFilterEffectiveFalse() {
        // 测试筛选无效的项目
        List<ThreeCatalogue> testData = createTestData();
        
        // 模拟Controller中的筛选逻辑
        Boolean effective = false;
        List<ThreeCatalogue> filteredList = testData.stream()
                .filter(catalogue -> effective.equals(catalogue.getEffective()))
                .collect(Collectors.toList());

        // 验证结果
        assert filteredList.size() == 2; // 应该有2个无效项目
        
        // 验证具体的无效项目
        List<String> invalidProjectNames = filteredList.stream()
                .map(ThreeCatalogue::getProjectName)
                .collect(Collectors.toList());
        
        assert invalidProjectNames.contains("过期器械");
        assert invalidProjectNames.contains("未来药品");
        
        System.out.println("测试通过：筛选无效项目 - 找到 " + filteredList.size() + " 个无效项目");
        filteredList.forEach(item -> 
            System.out.println("  - " + item.getProjectName() + " (有效性: " + item.getEffective() + ")")
        );
    }

    @Test
    public void testFilterEffectiveNull() {
        // 测试不筛选（返回全部）
        List<ThreeCatalogue> testData = createTestData();
        
        // 模拟Controller中的筛选逻辑
        Boolean effective = null;
        List<ThreeCatalogue> filteredList;
        
        if (effective != null) {
            filteredList = testData.stream()
                    .filter(catalogue -> effective.equals(catalogue.getEffective()))
                    .collect(Collectors.toList());
        } else {
            filteredList = testData; // 不筛选，返回全部
        }

        // 验证结果
        assert filteredList.size() == 5; // 应该返回全部5个项目
        
        System.out.println("测试通过：不筛选有效性 - 返回全部 " + filteredList.size() + " 个项目");
        filteredList.forEach(item -> 
            System.out.println("  - " + item.getProjectName() + " (有效性: " + item.getEffective() + ")")
        );
    }

    @Test
    public void testEffectiveStatusConsistency() {
        // 测试有效性状态的一致性
        List<ThreeCatalogue> testData = createTestData();
        
        // 统计有效和无效的数量
        long validCount = testData.stream()
                .filter(catalogue -> Boolean.TRUE.equals(catalogue.getEffective()))
                .count();
        
        long invalidCount = testData.stream()
                .filter(catalogue -> Boolean.FALSE.equals(catalogue.getEffective()))
                .count();
        
        // 验证总数一致性
        assert (validCount + invalidCount) == testData.size();
        
        System.out.println("测试通过：有效性状态一致性检查");
        System.out.println("  总项目数: " + testData.size());
        System.out.println("  有效项目数: " + validCount);
        System.out.println("  无效项目数: " + invalidCount);
        
        // 详细显示每个项目的有效性状态
        System.out.println("详细状态:");
        testData.forEach(item -> {
            System.out.println("  - " + item.getProjectName() + 
                " (开始: " + (item.getStartDate() != null ? "有" : "无") +
                ", 结束: " + (item.getEndDate() != null ? "有" : "无") +
                ", 有效性: " + item.getEffective() + ")");
        });
    }

    @Test
    public void testBusinessScenarioFiltering() {
        // 测试实际业务场景的筛选
        System.out.println("=== 业务场景筛选测试 ===");
        
        List<ThreeCatalogue> testData = createTestData();
        
        // 场景1：查询当前有效的药品
        List<ThreeCatalogue> validDrugs = testData.stream()
                .filter(catalogue -> "药品".equals(catalogue.getType()))
                .filter(catalogue -> Boolean.TRUE.equals(catalogue.getEffective()))
                .collect(Collectors.toList());
        
        assert validDrugs.size() == 1; // 应该只有1个有效药品
        assert "有效药品".equals(validDrugs.get(0).getProjectName());
        System.out.println("场景1通过：当前有效的药品 - " + validDrugs.size() + " 个");
        
        // 场景2：查询已过期的项目
        List<ThreeCatalogue> expiredItems = testData.stream()
                .filter(catalogue -> Boolean.FALSE.equals(catalogue.getEffective()))
                .filter(catalogue -> catalogue.getEndDate() != null && catalogue.getEndDate().before(new Date()))
                .collect(Collectors.toList());
        
        assert expiredItems.size() == 1; // 应该只有1个已过期项目
        assert "过期器械".equals(expiredItems.get(0).getProjectName());
        System.out.println("场景2通过：已过期的项目 - " + expiredItems.size() + " 个");
        
        // 场景3：查询甲类有效项目
        List<ThreeCatalogue> validClassAItems = testData.stream()
                .filter(catalogue -> "甲".equals(catalogue.getLevel()))
                .filter(catalogue -> Boolean.TRUE.equals(catalogue.getEffective()))
                .collect(Collectors.toList());
        
        assert validClassAItems.size() == 2; // 应该有2个甲类有效项目
        System.out.println("场景3通过：甲类有效项目 - " + validClassAItems.size() + " 个");
    }
}
