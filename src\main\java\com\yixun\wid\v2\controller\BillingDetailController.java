package com.yixun.wid.v2.controller;

import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.BillingDetailBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingDetailIn;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 待遇 电子清单-账单明细相关接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/billingDetail")
public class BillingDetailController {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 分页查询账单明细
     *
     * @param billingInfoId 关联的账单信息ID
     * @param projectName   项目名称（模糊查询）
     * @param projectCode   项目编码
     * @param feeType       费用类别
     * @param isWorkInjury  是否工伤
     * @param commonPage    分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<BillingDetail>> list(
            @RequestParam(required = false) Long billingInfoId,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String projectCode,
            @RequestParam(required = false) String feeType,
            @RequestParam(required = false) Boolean isWorkInjury,
            CommonPage commonPage) {
        
        Query query = new Query();
        
        // 账单信息ID查询
        if (billingInfoId != null) {
            query.addCriteria(Criteria.where("billingInfoId").is(billingInfoId));
        }
        
        // 项目名称模糊查询
        if (StringUtils.hasText(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }
        
        // 项目编码精确匹配
        if (StringUtils.hasText(projectCode)) {
            query.addCriteria(Criteria.where("projectCode").is(projectCode));
        }
        
        // 费用类别精确匹配
        if (StringUtils.hasText(feeType)) {
            query.addCriteria(Criteria.where("feeType").is(feeType));
        }
        
        // 是否工伤
        if (isWorkInjury != null) {
            query.addCriteria(Criteria.where("isWorkInjury").is(isWorkInjury));
        }
        
        // 添加排序：先按orderNum升序排序，再按创建时间降序
        query.with(Sort.by(Sort.Direction.ASC, "orderNum").and(Sort.by(Sort.Direction.DESC, "createTime")));
        
        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, BillingDetail.class, query, commonPage);
        
        // 执行查询
        List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);
        
        return CommonResult.successPageData(billingDetails, commonPage);
    }
    
    /**
     * 根据ID获取账单明细详情
     *
     * @param id 账单明细ID
     * @return 账单明细详情
     */
    @GetMapping("/detail")
    public CommonResult<BillingDetail> getDetail(@RequestParam Long id) {
        BillingDetail billingDetail = mongoTemplate.findById(id, BillingDetail.class);
        if (billingDetail == null) {
            throw new RuntimeException("账单明细不存在");
        }
        return CommonResult.successData(billingDetail);
    }

    /**
     * 新增账单明细
     *
     * @param billingDetailIn 账单明细输入参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<BillingDetail> add(@RequestBody BillingDetailIn billingDetailIn) {
        // 验证关联的账单信息是否存在
        Long billingInfoId = billingDetailIn.getBillingInfoId();
        BillingInfo billingInfo = mongoTemplate.findById(billingInfoId, BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "关联的账单信息不存在");
        }
        
        // 创建新的账单明细对象
        BillingDetail billingDetail = new BillingDetail();
        BeanUtils.copyProperties(billingDetailIn, billingDetail);
        
        // 处理orderNum，如果未提供序号，则根据已有明细数量自动设置
        if (billingDetail.getOrderNum() == null) {
            // 查询当前账单信息关联的明细数量
            Query countQuery = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
            long existingDetailsCount = mongoTemplate.count(countQuery, BillingDetail.class);
            billingDetail.setOrderNum((int)existingDetailsCount + 1);
        }
        
        // 生成唯一ID
        billingDetail.setId(SnGeneratorUtil.getId());
        
        // 设置创建时间和更新时间
        Date now = new Date();
        billingDetail.setCreateTime(now);
        billingDetail.setUpdateTime(now);
        
        // 保存到数据库
        mongoTemplate.save(billingDetail);
        
        return CommonResult.successData(billingDetail);
    }

    /**
     * 更新账单明细
     *
     * @param billingDetail 账单明细
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<BillingDetail> update(@RequestBody BillingDetail billingDetail) {
        // 检查ID是否存在
        if (billingDetail.getId() == null) {
            return CommonResult.failResult(10001, "账单明细ID不能为空");
        }

        // 查询原有数据
        BillingDetail existingDetail = mongoTemplate.findById(billingDetail.getId(), BillingDetail.class);
        if (existingDetail == null) {
            return CommonResult.failResult(10001, "账单明细不存在");
        }
        
        // 保留关联的账单信息ID
        if (billingDetail.getBillingInfoId() == null) {
            billingDetail.setBillingInfoId(existingDetail.getBillingInfoId());
        } 

        // 设置更新时间，保留创建时间
        billingDetail.setUpdateTime(new Date());
        billingDetail.setCreateTime(existingDetail.getCreateTime());
        
        // 更新到数据库
        mongoTemplate.save(billingDetail);

        return CommonResult.successData(billingDetail);
    }

    /**
     * 批量删除账单明细
     *
     * @param batchDeleteIn 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody BillingDetailBatchDeleteIn batchDeleteIn) {
        // 检查ID列表是否为空
        List<Long> ids = batchDeleteIn.getIds();
        if (ids == null || ids.isEmpty()) {
            return CommonResult.failResult(10001, "账单明细ID列表不能为空");
        }
        
        // 执行删除
        Query deleteQuery = Query.query(Criteria.where("_id").in(ids));
        DeleteResult result = mongoTemplate.remove(deleteQuery, BillingDetail.class);

        return CommonResult.successData(result.getDeletedCount());
    }

    /**
     * 根据账单信息ID查询关联的账单明细列表
     *
     * @param billingInfoId 账单信息ID
     * @return 账单明细列表
     */
    @GetMapping("/listByBillingInfo")
    public CommonResult<List<BillingDetail>> listByBillingInfo(@RequestParam Long billingInfoId) {
        // 验证账单信息是否存在
        BillingInfo billingInfo = mongoTemplate.findById(billingInfoId, BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "账单信息不存在");
        }
        
        // 查询关联的账单明细
        Query query = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
        query.with(Sort.by(Sort.Direction.ASC, "orderNum").and(Sort.by(Sort.Direction.DESC, "createTime")));
        
        List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);
        
        return CommonResult.successData(billingDetails);
    }
}