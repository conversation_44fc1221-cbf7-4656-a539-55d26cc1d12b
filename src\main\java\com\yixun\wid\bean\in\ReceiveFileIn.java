package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReceiveFileIn {

    @ApiModelProperty("申报id")
    private Long declarationId;

    @ApiModelProperty("环节 申报，补充资料，认定中止，撤销")
    private String step;

    @ApiModelProperty("类型 material-实物资料，virtual-虚拟/补充资料")
    private String type;

    @ApiModelProperty("申报人/经办人姓名")
    private String applicantName;

    @ApiModelProperty("是否已确认")
    private Boolean isConfirmed;

    @ApiModelProperty("文书编号")
    private String writSn;

    @ApiModelProperty("资料列表")
    private List fileList;
}
