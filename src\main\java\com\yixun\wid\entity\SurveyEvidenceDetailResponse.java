package com.yixun.wid.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SurveyEvidenceDetailResponse extends SurveyEvidenceDetail {

	@ApiModelProperty("调查对象姓名")
	private String surveyedUserName;

	@ApiModelProperty("调查对象类型")
	private String surveyedUserType;

	@ApiModelProperty("地点类型")
	private String addressType;

	@ApiModelProperty("地点类型名称")
	private String addressTypeDesc;

	@ApiModelProperty("地点")
	private String address;

	@ApiModelProperty("视频文件集合")
	private List<String> videoList;

	@ApiModelProperty("交查时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime submitTime;

}
