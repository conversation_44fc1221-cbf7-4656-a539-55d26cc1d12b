package com.yixun.wid.interceptor;

import com.alibaba.fastjson.JSON;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.UserHelper;
import com.yixun.wid.v2.enums.UserType;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * UserInterceptor - 检查用户是否登录
 */
@Slf4j
@Component
public class UserInterceptor extends HandlerInterceptorAdapter {

    @Value("${jwt.secret.account}")
    private String secret;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        boolean isLogin = true;
        String tokenValue = request.getHeader("Authorization");
        if (null == tokenValue)
            isLogin = false;

        if (isLogin) {
            try {
                String authToken = tokenValue.substring("Bearer ".length());
                Claims claims = Jwts.parser().setSigningKey(secret).parseClaimsJws(authToken).getBody();
                Object userIdObj = claims.get("userId");
            } catch (Exception e) {
                log.error(e.getMessage());
                isLogin = false;
            }
        }

        if (isLogin) {
	        Long currentUserId = UserHelper.getCurrentUserId();
	        request.setAttribute("userId", currentUserId);
	        request.setAttribute("userType", UserType.USER.getCode());
            return true;
        } else {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter()
                    .write(JSON.toJSONString(CommonResult.failResult(401, "未登录或登录已过期")));
            return false;
        }
    }

}
