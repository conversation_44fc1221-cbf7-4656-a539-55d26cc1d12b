package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.entity.MedicalCases;
import com.yixun.wid.v2.entity.MedicalCasesStatus;
import com.yixun.wid.v2.vo.HospitalPaymentStatVO;
import com.yixun.wid.v2.vo.HospitalPaymentDetailVO;
import com.yixun.wid.v2.vo.HospitalPaymentSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 待遇 医院支付统计相关接口
 */
@Slf4j
@RequestMapping("/v2/hospital-payment")
@RestController
public class HospitalPaymentStatController {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 医院支付统计接口
     * 按医院名称维度统计已办结案件的审核结果
     *
     * @param hospitalName    机构名称（可选，关键词搜索）
     * @param isAgreementHospital 是否协议机构（可选）
     * @param province    省份（可选）
     * @param city    城市（可选）
     * @param district    区域（可选）
     * @param startDate    统计开始时间（可选）
     * @param endDate    统计结束时间（可选）
     * @param commonPage    分页信息
     * @return 医院支付统计数据
     */
    @GetMapping("/stat")
    public CommonResult<List<HospitalPaymentStatVO>> getHospitalPaymentStat(
            @RequestParam(required = false) String hospitalName,
            @RequestParam(required = false) Boolean isAgreementHospital,
            @RequestParam(required = false) String province,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            CommonPage commonPage) {

        // 构建匹配条件
        List<Criteria> criteriaList = new ArrayList<>();

        // 只统计已办结的案件
        criteriaList.add(Criteria.where("status").is(MedicalCasesStatus.Done));

        // 根据参数添加筛选条件
        if (hospitalName != null && !hospitalName.isEmpty()) {
            criteriaList.add(Criteria.where("billingInfos.hospital").regex(".*" + hospitalName + ".*", "i"));
        }

        // 所在区域筛选
        if (province != null && !province.isEmpty()) {
            criteriaList.add(Criteria.where("billingInfos.hospitalProvince").is(province));
        }
        if (city != null && !city.isEmpty()) {
            criteriaList.add(Criteria.where("billingInfos.hospitalCity").is(city));
        }
        if (district != null && !district.isEmpty()) {
            criteriaList.add(Criteria.where("billingInfos.hospitalDistrict").is(district));
        }

        // 时间范围筛选
        if (startDate != null || endDate != null) {
            Criteria dateCriteria = Criteria.where("acceptDate");
            if (startDate != null && endDate != null) {
                dateCriteria.gte(startDate).lte(endDate);
            } else if (startDate != null) {
                dateCriteria.gte(startDate);
            } else if (endDate != null) {
                dateCriteria.lte(endDate);
            }
            criteriaList.add(dateCriteria);
        }

        Criteria criteria = new Criteria();
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
            // 匹配筛选条件
            Aggregation.match(criteria),
            // 展开账单信息
            Aggregation.unwind("billingInfos", true),
            // 按医院分组统计
            Aggregation.group("billingInfos.hospital")
                .first("billingInfos.hospital").as("hospitalName")
                .first("billingInfos.isAgreementHospital").as("isAgreementHospital")
                .first("billingInfos.hospitalProvince").as("province")
                .first("billingInfos.hospitalCity").as("city")
                .first("billingInfos.hospitalDistrict").as("district")
                .addToSet("idCard").as("patientIds")
                .sum("billingInfos.invoiceAmount").as("totalInvoiceAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("门诊"))
                    .then("$billingInfos.invoiceAmount")
                    .otherwise(0))
                .as("outpatientInvoiceAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("住院"))
                    .then("$billingInfos.invoiceAmount")
                    .otherwise(0))
                .as("inpatientInvoiceAmount")
                .sum("billingInfos.reimbursableAmount").as("totalReimbursableAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("门诊"))
                    .then("$billingInfos.reimbursableAmount")
                    .otherwise(0))
                .as("outpatientReimbursableAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("住院"))
                    .then("$billingInfos.reimbursableAmount")
                    .otherwise(0))
                .as("inpatientReimbursableAmount")
                .sum("billingInfos.hospitalDays").as("totalHospitalDays")
                .sum("billingInfos.hospitalFoodAllowance").as("totalHospitalFoodAllowance")
                .sum("billingInfos.actualPayAmount").as("totalActualPayAmount"),
            // 添加就诊人数字段（使用数组大小）
            Aggregation.project()
                .andExpression("{ $size: '$patientIds' }").as("patientCount")
                .andInclude("hospitalName", "isAgreementHospital", "province", "city", "district",
                            "totalInvoiceAmount", "outpatientInvoiceAmount", "inpatientInvoiceAmount",
                            "totalReimbursableAmount", "outpatientReimbursableAmount", "inpatientReimbursableAmount",
                            "totalHospitalDays", "totalHospitalFoodAllowance", "totalActualPayAmount"),
            // 排序
            Aggregation.sort(Sort.Direction.DESC, "totalInvoiceAmount")
        );

        // 执行聚合查询
        AggregationResults<HospitalPaymentStatVO> results = mongoTemplate.aggregate(
            aggregation, MedicalCases.class, HospitalPaymentStatVO.class);

        List<HospitalPaymentStatVO> statList = results.getMappedResults();

        // 计算总数据
        HospitalPaymentSummaryVO summary = calculateSummary(statList);

        // 应用分页
        int totalCount = statList.size();
        int pageSize = commonPage.getPageSize();
        int pageNum = commonPage.getPageNum();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);

        List<HospitalPaymentStatVO> pagedList = (startIndex < totalCount) ?
            statList.subList(startIndex, endIndex) : new ArrayList<>();

        // 设置分页信息
        commonPage.setTotal(totalCount);

        // 返回结果
        return CommonResult.successPageData(pagedList, commonPage);
    }

    /**
     * 计算汇总数据
     */
    private HospitalPaymentSummaryVO calculateSummary(List<HospitalPaymentStatVO> statList) {
        HospitalPaymentSummaryVO summary = new HospitalPaymentSummaryVO();

        int totalPatientCount = 0;
        double totalInvoiceAmount = 0;
        double outpatientInvoiceAmount = 0;
        double inpatientInvoiceAmount = 0;
        int totalHospitalDays = 0;

        for (HospitalPaymentStatVO stat : statList) {
            totalPatientCount += stat.getPatientCount();
            totalInvoiceAmount += stat.getTotalInvoiceAmount();
            outpatientInvoiceAmount += stat.getOutpatientInvoiceAmount();
            inpatientInvoiceAmount += stat.getInpatientInvoiceAmount();
            totalHospitalDays += stat.getTotalHospitalDays();
        }

        summary.setTotalPatientCount(totalPatientCount);
        summary.setTotalInvoiceAmount(totalInvoiceAmount);
        summary.setOutpatientInvoiceAmount(outpatientInvoiceAmount);
        summary.setInpatientInvoiceAmount(inpatientInvoiceAmount);
        summary.setTotalHospitalDays(totalHospitalDays);

        return summary;
    }

    /**
     * 统计明细接口
     * 按职工维度展示统计明细
     *
     * @param hospitalName 医院名称（必填）
     * @param workerName 职工姓名（可选）
     * @param idCard 身份证号（可选）
     * @param organization 单位名称（可选）
     * @param startDate 统计开始时间（可选）
     * @param endDate 统计结束时间（可选）
     * @param commonPage 分页信息
     * @return 统计明细数据
     */
    @GetMapping("/detail")
    public CommonResult<List<HospitalPaymentDetailVO>> getHospitalPaymentDetail(
            @RequestParam String hospitalName,
            @RequestParam(required = false) String workerName,
            @RequestParam(required = false) String idCard,
            @RequestParam(required = false) String organization,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            CommonPage commonPage) {

        // 构建匹配条件
        List<Criteria> criteriaList = new ArrayList<>();

        // 只统计已办结的案件
        criteriaList.add(Criteria.where("status").is(MedicalCasesStatus.Done));

        // 医院名称是必填的
        criteriaList.add(Criteria.where("billingInfos.hospital").is(hospitalName));

        // 根据参数添加筛选条件
        if (workerName != null && !workerName.isEmpty()) {
            criteriaList.add(Criteria.where("workerName").regex(".*" + workerName + ".*", "i"));
        }
        if (idCard != null && !idCard.isEmpty()) {
            criteriaList.add(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
        }
        if (organization != null && !organization.isEmpty()) {
            criteriaList.add(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
        }

        // 时间范围筛选
        if (startDate != null || endDate != null) {
            Criteria dateCriteria = Criteria.where("acceptDate");
            if (startDate != null && endDate != null) {
                dateCriteria.gte(startDate).lte(endDate);
            } else if (startDate != null) {
                dateCriteria.gte(startDate);
            } else if (endDate != null) {
                dateCriteria.lte(endDate);
            }
            criteriaList.add(dateCriteria);
        }

        Criteria criteria = new Criteria();
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
            // 匹配筛选条件
            Aggregation.match(criteria),
            // 展开账单信息
            Aggregation.unwind("billingInfos"),
            // 只保留指定医院的账单
            Aggregation.match(Criteria.where("billingInfos.hospital").is(hospitalName)),
            // 按职工信息分组
            Aggregation.group("idCard")
                .first("workerName").as("workerName")
                .first("idCard").as("idCard")
                .first("organization").as("organization")
                .count().as("visitCount")
                .sum("billingInfos.invoiceAmount").as("totalInvoiceAmount")
                .sum("billingInfos.reimbursableAmount").as("totalReimbursableAmount")
                .sum("billingInfos.nonReimbursableAmount").as("totalNonReimbursableAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("住院"))
                    .then("$billingInfos.hospitalDays")
                    .otherwise(0))
                .as("totalHospitalDays")
                .sum("billingInfos.hospitalFoodAllowance").as("totalHospitalFoodAllowance")
                .sum("billingInfos.actualPayAmount").as("totalActualPayAmount")
                .min("acceptDate").as("firstVisitDate")
                .max("acceptDate").as("lastVisitDate"),
            // 排序
            Aggregation.sort(Sort.Direction.DESC, "totalInvoiceAmount")
        );

        // 执行聚合查询
        AggregationResults<HospitalPaymentDetailVO> results = mongoTemplate.aggregate(
            aggregation, MedicalCases.class, HospitalPaymentDetailVO.class);

        List<HospitalPaymentDetailVO> detailList = results.getMappedResults();

        // 应用分页
        int totalCount = detailList.size();
        int pageSize = commonPage.getPageSize();
        int pageNum = commonPage.getPageNum();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);

        List<HospitalPaymentDetailVO> pagedList = (startIndex < totalCount) ?
            detailList.subList(startIndex, endIndex) : new ArrayList<>();

        // 设置分页信息
        commonPage.setTotal(totalCount);

        // 返回结果
        return CommonResult.successPageData(pagedList, commonPage);
    }

    /**
     * 总额明细接口
     * 展示查询范围内所有票据的总数量、金额等统计信息
     *
     * @param hospitalName 医院名称（必填）
     * @param startDate 统计开始时间（可选）
     * @param endDate 统计结束时间（可选）
     * @return 总额明细数据
     */
    @GetMapping("/total")
    public CommonResult<HospitalPaymentSummaryVO> getHospitalPaymentTotal(
            @RequestParam String hospitalName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        // 构建匹配条件
        List<Criteria> criteriaList = new ArrayList<>();

        // 只统计已办结的案件
        criteriaList.add(Criteria.where("status").is(MedicalCasesStatus.Done));

        // 医院名称是必填的
        criteriaList.add(Criteria.where("billingInfos.hospital").is(hospitalName));

        // 时间范围筛选
        if (startDate != null || endDate != null) {
            Criteria dateCriteria = Criteria.where("acceptDate");
            if (startDate != null && endDate != null) {
                dateCriteria.gte(startDate).lte(endDate);
            } else if (startDate != null) {
                dateCriteria.gte(startDate);
            } else if (endDate != null) {
                dateCriteria.lte(endDate);
            }
            criteriaList.add(dateCriteria);
        }

        Criteria criteria = new Criteria();
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
            // 匹配筛选条件
            Aggregation.match(criteria),
            // 展开账单信息
            Aggregation.unwind("billingInfos"),
            // 只保留指定医院的账单
            Aggregation.match(Criteria.where("billingInfos.hospital").is(hospitalName)),
            // 计算汇总数据
            Aggregation.group()
                .count().as("totalInvoiceCount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.reimbursableAmount").gt(0))
                    .then(1)
                    .otherwise(0))
                .as("normalInvoiceCount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.reimbursableAmount").lte(0))
                    .then(1)
                    .otherwise(0))
                .as("deductedInvoiceCount")
                .sum("billingInfos.invoiceAmount").as("totalInvoiceAmount")
                .sum("billingInfos.reimbursableAmount").as("totalReimbursableAmount")
                .sum("billingInfos.nonReimbursableAmount").as("totalNonReimbursableAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("住院"))
                    .then("$billingInfos.hospitalDays")
                    .otherwise(0))
                .as("totalHospitalDays")
                .sum("billingInfos.hospitalFoodAllowance").as("totalHospitalFoodAllowance")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("住院"))
                    .then("$billingInfos.invoiceAmount")
                    .otherwise(0))
                .as("inpatientInvoiceAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.treatmentType").is("门诊"))
                    .then("$billingInfos.invoiceAmount")
                    .otherwise(0))
                .as("outpatientInvoiceAmount")
                .sum(ConditionalOperators.when(Criteria.where("billingInfos.nonWorkInjuryDeduction").gt(0))
                    .then("$billingInfos.nonWorkInjuryDeduction")
                    .otherwise(0))
                .as("nonWorkInjuryDeduction")
        );

        // 执行聚合查询
        AggregationResults<HospitalPaymentSummaryVO> results = mongoTemplate.aggregate(
            aggregation, MedicalCases.class, HospitalPaymentSummaryVO.class);

        List<HospitalPaymentSummaryVO> summaryList = results.getMappedResults();

        // 如果没有数据，返回空对象
        HospitalPaymentSummaryVO summary = summaryList.isEmpty() ? new HospitalPaymentSummaryVO() : summaryList.get(0);

        return CommonResult.successData(summary);
    }
}
