package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.LongJsonSerializer;
import lombok.Data;

@Data
public class UserInfoOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    private String phone;
    private String realName;
    private String avatar;
    private String email;
    private String type;
    private Long organizationId;
    private String organization;
    private String position;
    private String openId;

}
