package com.yixun.wid.v2.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.wid.entity.em.DictType;
import com.yixun.wid.v2.dao.DataDictV2Mapper;
import com.yixun.wid.v2.entity.DataDict;
import com.yixun.wid.v2.service.DataDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class DataDictV2ServiceImpl implements DataDictService {

    @Resource
    private DataDictV2Mapper dataDictV2Mapper;


    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;;

    @Override
    public void insert(DataDict dataDict) {
        dataDictV2Mapper.insert(dataDict);
    }

    @Override
    public void update(DataDict dataDict) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nodecode", dataDict.getNodecode());
        queryWrapper.eq("datatype", dataDict.getDatatype());

        dataDictV2Mapper.update(dataDict, queryWrapper);
    }

    @Override
    public DataDict getOne(String nodecode, String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nodecode", nodecode);
        queryWrapper.eq("datatype", datatype);

        return dataDictV2Mapper.selectOne(queryWrapper);
    }

    @Override
    public List<DataDict> getDataDictList(String parentcode, String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentcode", parentcode);
        queryWrapper.eq("enable", "true");
        queryWrapper.eq("datatype", datatype);

        return dataDictV2Mapper.selectList(queryWrapper);
    }

    @Override
    public List<DataDict> getAllDataDictList(String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", "true");
        queryWrapper.eq("datatype", datatype);

        return dataDictV2Mapper.selectList(queryWrapper);
    }

    @Override
    public Page<DataDict> getAdminDataDictList(String parentcode, DictType datatype,String label, CommonPage commonPage) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        if (parentcode!=null){
            queryWrapper.eq("parentcode", parentcode);
        }
        if (datatype!=null){
            queryWrapper.eq("datatype", datatype.name());
        }
        if (label!=null){

            queryWrapper.like("label", label);
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<DataDict>) dataDictV2Mapper.selectPage(page, queryWrapper);
    }

    @Override
    public void deleteByType(String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("datatype", datatype);
        dataDictV2Mapper.delete(queryWrapper);
    }

    @Override
    public List<DataDict> getDataDictListByNodes(List<String> nodes, String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("nodecode", nodes);
        queryWrapper.eq("datatype", datatype);

        return dataDictV2Mapper.selectList(queryWrapper);
    }

    @Override
    public List<DataDict> getByLabelList(List<String> labelList, String datatype) {
        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("label", labelList);
        queryWrapper.eq("datatype", datatype);

        return dataDictV2Mapper.selectList(queryWrapper);
    }

//    @Override
//    public List<CodeName> getIndustryListByName(String industry, String industry2, String industry3) {
//        ArrayList<CodeName> codeNameList = new ArrayList<>();
//        if (industry != null && !industry.isEmpty()) {
//            CodeName industryByNameAndLevel = getIndustryByNameAndLevel(industry, 1);
//            if (industryByNameAndLevel != null) {
//                codeNameList.add(industryByNameAndLevel);
//                if (industry2 != null && !industry2.isEmpty()) {
//                    CodeName industryByNameAndLevel2 = getIndustryByNameAndLevel(industry2, 2);
//                    if (industryByNameAndLevel2 != null) {
//                        codeNameList.add(industryByNameAndLevel2);
//                        if (industry3 != null && !industry3.isEmpty()) {
//                            CodeName industryByNameAndLevel3 = getIndustryByNameAndLevel(industry3, 3);
//                            if (industryByNameAndLevel3 != null) {
//                                codeNameList.add(industryByNameAndLevel3);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return codeNameList;
//    }

//    @Override
//    public String getIndustry(String importantIndustry) {
//        if (importantIndustry == null || importantIndustry.isEmpty()) {
//            return null;
//        }
//        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("enable", "true");
//        queryWrapper.eq("datatype", "keyIndustry");
//        queryWrapper.eq("label", importantIndustry);
//        DataDict dataDict = dataDictMapper.selectOne(queryWrapper);
//        if (dataDict!=null){
//            return dataDict.getNodecode();
//        }
//        log.info("行业名不存在{}", importantIndustry);
//        return null;
//    }

//    /**
//     * 根据行业名称和层级获取行业
//     * @param industry 行业名
//     * @param level 层级
//     * @return CodeName
//     */
//    private CodeName getIndustryByNameAndLevel(String industry,int level) {
//        CodeName codeName = new CodeName();
//        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("enable", "true");
//        queryWrapper.eq("datatype", "nationalEconomyIndustry");
//        queryWrapper.eq("label", industry);
//        queryWrapper.eq("level", level);
//        DataDict dataDict = dataDictMapper.selectOne(queryWrapper);
//        if (dataDict!=null){
//            codeName.setCode(dataDict.getNodecode());
//            codeName.setName(dataDict.getLabel());
//            return codeName;
//        }
//      return null;
//    }


    /**
     * 获取行业层级列表。
     * 该方法基于传入的基础行业列表，递归查询其所有子行业，并返回所有行业路径的列表。
     *
     * @param baseIndustryList 基础行业列表
     * @return 所有行业路径的列表
     */
//    @Override
//    public List<List<CodeName>> getIndustryHierarchy(List<CodeName> baseIndustryList) {
//        List<List<CodeName>> industryHierarchy;
//        CodeName lastIndustry = getLastIndustry(baseIndustryList);
//        if (lastIndustry == null) {
//            return new ArrayList<>();
//        }
//
//        String cacheKey = "industryHierarchy:" + lastIndustry.getCode();
//        industryHierarchy = (List<List<CodeName>>) redisTemplate.opsForValue().get(cacheKey);
//        if (industryHierarchy != null) {
//            return industryHierarchy;
//        }
//
//        industryHierarchy = new ArrayList<>();
//        industryHierarchy.add(baseIndustryList);
//        fetchChildIndustries(lastIndustry.getCode(), new ArrayList<>(baseIndustryList), industryHierarchy);
//
//        redisTemplate.opsForValue().set(cacheKey, industryHierarchy, 1, TimeUnit.HOURS);
//        return industryHierarchy;
//    }

    /**
//     * 递归查询并构建子行业路径。
//     * 该方法查询指定父行业的所有子行业，并将其添加到行业层级列表中。
//     *
//     * @param parentCode 父行业编码
//     * @param currentPath 当前行业路径
//     * @param industryHierarchy 存储所有行业层级路径的列表
//     */
//    private void fetchChildIndustries(String parentCode, List<CodeName> currentPath, List<List<CodeName>> industryHierarchy) {
//        QueryWrapper<DataDict> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("enable", "true");
//        queryWrapper.eq("datatype", "nationalEconomyIndustry");
//        queryWrapper.eq("parentcode", parentCode);
//
//        List<DataDict> childIndustries = dataDictMapper.selectList(queryWrapper);
//        if (childIndustries == null || childIndustries.isEmpty()) {
//            return;
//        }
//
//        for (DataDict industry : childIndustries) {
//            List<CodeName> newPath = new ArrayList<>(currentPath);
//            CodeName newIndustry = new CodeName(industry.getNodecode(), industry.getLabel());
//            newPath.add(newIndustry);
//            industryHierarchy.add(newPath);
//
//            fetchChildIndustries(newIndustry.getCode(), newPath, industryHierarchy);
//        }
//    }
//
//
//    /**
//     * 获取行业列表中的最后一个行业。
//     *
//     * @param industries 行业列表
//     * @return 列表中的最后一个行业对象，如果列表为空，则返回 null
//     */
//    private CodeName getLastIndustry(List<CodeName> industries) {
//        return (industries == null || industries.isEmpty()) ? null : industries.get(industries.size() - 1);
//    }

}
