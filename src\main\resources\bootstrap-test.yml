spring:
  cloud:
    nacos:
      username: nacos
      password: Na0saD1n0v04
      config:
        # 开启nacos作为配置中心，默认值：true
        enabled: true
        # 主配置 开启注册监听器预加载配置服务（除非特殊业务需求，否则不推荐打开该参数）
        enable-remote-sync-config: false
        #允许nacos上的配置优先于本地配置; 意思是：如果nacos配置了某属性，然后在本地的application.yml配置了相同属性；那么会以nacos配置的优先;否则是本地的优先
        remote-first: true
        # 配置文件地址
        server-addr: 192.168.252.223:8848
        # 配置文件所在的group
        group: dev
        # 配置文件的文件类型
        file-extension: yaml
        refresh-enabled: true
        #共享配置
        shared-configs:
          - data-id: share-api:config.yml
            group: test
            refresh: true
        #扩展配置
        extension-configs:
          - data-id: ${spring.application.name}:config.yml
            group: test
            #namespace:  # 命名空间 该属性如果为空 或者注释该属性  会继承主属性的命名空间
            # 该属性不继承上面的主配置  是否开启自动刷新 (false:不监听属性变化,true:监听属性变化,但是对应的属性注解上也必须设置autoRefreshed = true才可以实时更新)
            refresh: true