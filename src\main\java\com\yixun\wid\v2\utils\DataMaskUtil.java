package com.yixun.wid.v2.utils;

import cn.hutool.core.util.StrUtil;

/**
 * 数据脱敏工具类
 */
public class DataMaskUtil {

    /**
     * 姓名脱敏
     * 规则：保留第一个字符，其余用*代替
     * 例：张三 -> 张*，张三丰 -> 张**
     */
    public static String maskName(String name) {
        if (StrUtil.isBlank(name)) {
            return name;
        }
        
        if (name.length() == 1) {
            return name;
        }
        
        StringBuilder masked = new StringBuilder();
        masked.append(name.charAt(0));
        for (int i = 1; i < name.length(); i++) {
            masked.append("*");
        }
        
        return masked.toString();
    }

    /**
     * 手机号脱敏
     * 规则：保留前3位和后4位，中间用****代替
     * 例：13812345678 -> 138****5678
     */
    public static String maskPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return phone;
        }
        
        if (phone.length() != 11) {
            // 如果不是11位手机号，只显示前3位，其余用*代替
            if (phone.length() <= 3) {
                return phone;
            }
            StringBuilder masked = new StringBuilder();
            masked.append(phone.substring(0, 3));
            for (int i = 3; i < phone.length(); i++) {
                masked.append("*");
            }
            return masked.toString();
        }
        
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 身份证号脱敏
     * 规则：保留前6位和后4位，中间用********代替
     * 例：110101199001011234 -> 110101********1234
     */
    public static String maskIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return idCard;
        }
        
        if (idCard.length() < 10) {
            // 如果身份证号长度不足，只显示前2位，其余用*代替
            if (idCard.length() <= 2) {
                return idCard;
            }
            StringBuilder masked = new StringBuilder();
            masked.append(idCard.substring(0, 2));
            for (int i = 2; i < idCard.length(); i++) {
                masked.append("*");
            }
            return masked.toString();
        }
        
        if (idCard.length() == 15) {
            // 15位身份证：保留前6位和后3位
            return idCard.substring(0, 6) + "******" + idCard.substring(12);
        } else if (idCard.length() == 18) {
            // 18位身份证：保留前6位和后4位
            return idCard.substring(0, 6) + "********" + idCard.substring(14);
        } else {
            // 其他长度：保留前6位，其余用*代替
            StringBuilder masked = new StringBuilder();
            masked.append(idCard.substring(0, Math.min(6, idCard.length())));
            for (int i = 6; i < idCard.length(); i++) {
                masked.append("*");
            }
            return masked.toString();
        }
    }
}
