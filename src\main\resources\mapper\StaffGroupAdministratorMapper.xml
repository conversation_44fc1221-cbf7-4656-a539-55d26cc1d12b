<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.wid.mapper.StaffGroupAdministratorMapper" >

    <select id="getGroupStaffList" resultType="com.yixun.wid.bean.out.GroupStaffOut">
        SELECT sg.staff_group_id, g.parent_id, g.group_name, sg.administrator_id, a.username, a.avatar
            FROM staff_group as g left join staff_group_administrator as sg on g.id=sg.staff_group_id
                left join administrator as a on sg.administrator_id=a.id
        WHERE g.is_del = false
        <if test="staffGroupId!=null">
            and sg.staff_group_id = #{staffGroupId}
        </if>
    </select>

    <select id="getAllGroupStaffList" resultType="com.yixun.wid.bean.out.GroupStaffOut">
        SELECT g.id as staffGroupId, g.parent_id, g.group_name, sg.administrator_id, a.username, a.avatar
        FROM staff_group as g left join staff_group_administrator as sg on g.id=sg.staff_group_id
        left join administrator as a on sg.administrator_id=a.id
        WHERE g.is_del = false
    </select>

    <select id="getGroupNameByUser" resultType="com.yixun.wid.entity.StaffGroup">
        SELECT g.id,g.group_name
        FROM staff_group_administrator as sg left join staff_group as g on g.id=sg.staff_group_id
        WHERE g.is_del = false and sg.administrator_id = #{administratorId}
    </select>
</mapper>