package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.MessageRecord;
import com.yixun.wid.service.MessageRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "admin消息发送记录")
@RestController
@RequestMapping(value = "/admin/messageRecord")
public class AdminMessageRecordController {

    @Resource
    private MessageRecordService messageRecordService;

    @GetMapping("/getList")
    @ApiOperation("获取消息发送记录列表")
    public CommonResult<List<MessageRecord>> getList(@RequestParam("businessId") String businessId, @RequestParam("busType") Integer busType) {

        List<MessageRecord> recordByBus = messageRecordService.getRecordByBus(businessId, busType);
        return CommonResult.successData(recordByBus);
    }

}
