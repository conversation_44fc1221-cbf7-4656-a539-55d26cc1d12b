package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.entity.ResponsibilityTree;
import com.yixun.wid.v2.service.ResponsibilityTreeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 待遇 责任列表树形结构接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/responsibility/tree")
public class ResponsibilityTreeController {

    @Resource
    private ResponsibilityTreeService responsibilityTreeService;

    /**
     * 获取完整树形结构
     *
     * @return 树形结构（顶层节点列表）
     */
    @GetMapping("/getTree")
    public CommonResult<List<ResponsibilityTree>> getTree() {
        List<ResponsibilityTree> tree = responsibilityTreeService.getTree();
        return CommonResult.successData(tree);
    }

    /**
     * 根据ID获取子树
     *
     * @param id 节点ID
     * @return 以指定节点为根的子树
     */
    @GetMapping("/getTreeById")
    public CommonResult<ResponsibilityTree> getTreeById(@RequestParam Long id) {
        ResponsibilityTree tree = responsibilityTreeService.getTreeById(id);
        return CommonResult.successData(tree);
    }

    /**
     * 获取所有节点（平铺结构）
     *
     * @return 所有节点列表
     */
    @GetMapping("/getAll")
    public CommonResult<List<ResponsibilityTree>> getAll() {
        List<ResponsibilityTree> nodes = responsibilityTreeService.getAll();
        return CommonResult.successData(nodes);
    }

    /**
     * 新增节点
     *
     * @param node 节点信息
     * @return 新增后的节点
     */
    @PostMapping("/add")
    public CommonResult<ResponsibilityTree> add(@RequestBody ResponsibilityTree node) {
        ResponsibilityTree result = responsibilityTreeService.add(node);
        return CommonResult.successData(result);
    }

    /**
     * 更新节点
     *
     * @param node 节点信息
     * @return 更新后的节点
     */
    @PostMapping("/update")
    public CommonResult<ResponsibilityTree> update(@RequestBody ResponsibilityTree node) {
        ResponsibilityTree result = responsibilityTreeService.update(node);
        return CommonResult.successData(result);
    }

    /**
     * 删除节点及其子节点
     *
     * @param id 节点ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    public CommonResult<Long> delete(@RequestParam Long id) {
        Long result = responsibilityTreeService.delete(id);
        return CommonResult.successData(result);
    }

    /**
     * 批量保存完整树形结构
     *
     * @param treeNodes 树形结构数据（包含根节点及其子节点）
     * @return 保存后的树形结构
     */
    @PostMapping("/batchSaveTree")
    public CommonResult<List<ResponsibilityTree>> batchSaveTree(@RequestBody List<ResponsibilityTree> treeNodes) {
        if (treeNodes == null || treeNodes.isEmpty()) {
            return CommonResult.failResult(1001, "树形结构数据不能为空");
        }
        
        // 保存每个根节点及其子节点
        List<ResponsibilityTree> savedTrees = new ArrayList<>();
        for (ResponsibilityTree rootNode : treeNodes) {
            savedTrees.add(saveBranchRecursively(rootNode));
        }
        
        return CommonResult.successData(savedTrees);
    }
    
    /**
     * 递归保存节点及其子节点
     *
     * @param node 当前节点
     * @return 保存后的节点及其子节点
     */
    private ResponsibilityTree saveBranchRecursively(ResponsibilityTree node) {
        // 1. 保存当前节点
        ResponsibilityTree savedNode = responsibilityTreeService.add(node);
        
        // 2. 递归保存子节点
        List<ResponsibilityTree> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            List<ResponsibilityTree> savedChildren = new ArrayList<>();
            for (ResponsibilityTree child : children) {
                // 设置父节点ID
                child.setParentId(savedNode.getId());
                // 递归保存子节点
                ResponsibilityTree savedChild = saveBranchRecursively(child);
                savedChildren.add(savedChild);
            }
            savedNode.setChildren(savedChildren);
        }
        
        return savedNode;
    }
}
