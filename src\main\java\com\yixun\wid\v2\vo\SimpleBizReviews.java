package com.yixun.wid.v2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lzhpo.sensitive.SensitiveStrategy;
import com.lzhpo.sensitive.annocation.Sensitive;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class SimpleBizReviews {

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 评价等级
	 */
	private Integer reviewsLevel;

	/**
	 * 评价内容
	 */
	private String reviewsContent;

	/**
	 * 评价时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date reviewsTime;

	/**
	 * 评价用户
	 */
	@Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
	private String reviewsUserName;

	/**
	 * 评价用户联系方式
	 */
	@Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
	private String reviewsUserPhone;

}
