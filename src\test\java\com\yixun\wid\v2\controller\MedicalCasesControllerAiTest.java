package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.ai.CalculatorRequest;
import com.yixun.wid.v2.vo.ai.CalculatorResponse;
import com.yixun.wid.v2.vo.ai.ProjectSearchRequest;
import com.yixun.wid.v2.vo.ai.ProjectSearchResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * MedicalCasesController AI接口测试
 */
@ExtendWith(MockitoExtension.class)
public class MedicalCasesControllerAiTest {

    @Mock
    private AiUtils aiUtils;

    @InjectMocks
    private MedicalCasesController medicalCasesController;

    @BeforeEach
    public void setUp() {
        // 初始化测试环境
    }

    @Test
    public void testCalculatorWithValidInput() {
        // 准备测试数据
        String input = "1+1";
        CalculatorRequest request = new CalculatorRequest();
        request.setInput(input);
        
        CalculatorResponse mockResponse = new CalculatorResponse();
        mockResponse.setStatus("success");
        mockResponse.setMessage("计算成功");
        CalculatorResponse.CalculatorData data = new CalculatorResponse.CalculatorData();
        data.setResult("2");
        mockResponse.setData(data);

        // 模拟AiUtils的返回
        when(aiUtils.calculator(anyString())).thenReturn(mockResponse);

        // 执行测试
        CommonResult<CalculatorResponse> result = medicalCasesController.calculator(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(200), result.getCode());
        assertNotNull(result.getData());
        assertEquals("success", result.getData().getStatus());
        assertEquals("计算成功", result.getData().getMessage());
        assertEquals("2", result.getData().getData().getResult());
    }

    @Test
    public void testCalculatorWithEmptyInput() {
        // 执行测试 - 空输入
        CalculatorRequest request = new CalculatorRequest();
        request.setInput("");
        CommonResult<CalculatorResponse> result = medicalCasesController.calculator(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("计算表达式不能为空", result.getMsg());
    }

    @Test
    public void testCalculatorWithNullInput() {
        // 执行测试 - null输入
        CalculatorRequest request = new CalculatorRequest();
        request.setInput(null);
        CommonResult<CalculatorResponse> result = medicalCasesController.calculator(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("计算表达式不能为空", result.getMsg());
    }

    @Test
    public void testCalculatorWithNullRequest() {
        // 执行测试 - null请求
        CommonResult<CalculatorResponse> result = medicalCasesController.calculator(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("计算表达式不能为空", result.getMsg());
    }

    @Test
    public void testCalculatorWithException() {
        // 准备测试数据
        String input = "1+1";
        CalculatorRequest request = new CalculatorRequest();
        request.setInput(input);

        // 模拟AiUtils抛出异常
        when(aiUtils.calculator(anyString())).thenThrow(new RuntimeException("计算服务异常"));

        // 执行测试
        CommonResult<CalculatorResponse> result = medicalCasesController.calculator(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertTrue(result.getMsg().contains("计算器运算失败"));
        assertTrue(result.getMsg().contains("计算服务异常"));
    }

    @Test
    public void testProjectSearchWithValidInput() {
        // 准备测试数据
        String projectName = "医疗项目";
        ProjectSearchRequest request = new ProjectSearchRequest();
        request.setProjectName(projectName);
        
        ProjectSearchResponse mockResponse = new ProjectSearchResponse();
        mockResponse.setStatus("success");
        mockResponse.setMessage("查询成功");
        ProjectSearchResponse.ProjectSearchData data = new ProjectSearchResponse.ProjectSearchData();
        data.setResult("查询到的医疗项目信息...");
        mockResponse.setData(data);

        // 模拟AiUtils的返回
        when(aiUtils.projectSearch(anyString())).thenReturn(mockResponse);

        // 执行测试
        CommonResult<ProjectSearchResponse> result = medicalCasesController.projectSearch(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(200), result.getCode());
        assertNotNull(result.getData());
        assertEquals("success", result.getData().getStatus());
        assertEquals("查询成功", result.getData().getMessage());
        assertEquals("查询到的医疗项目信息...", result.getData().getData().getResult());
    }

    @Test
    public void testProjectSearchWithEmptyInput() {
        // 执行测试 - 空输入
        ProjectSearchRequest request = new ProjectSearchRequest();
        request.setProjectName("");
        CommonResult<ProjectSearchResponse> result = medicalCasesController.projectSearch(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("项目名称不能为空", result.getMsg());
    }

    @Test
    public void testProjectSearchWithNullInput() {
        // 执行测试 - null输入
        ProjectSearchRequest request = new ProjectSearchRequest();
        request.setProjectName(null);
        CommonResult<ProjectSearchResponse> result = medicalCasesController.projectSearch(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("项目名称不能为空", result.getMsg());
    }

    @Test
    public void testProjectSearchWithNullRequest() {
        // 执行测试 - null请求
        CommonResult<ProjectSearchResponse> result = medicalCasesController.projectSearch(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertEquals("项目名称不能为空", result.getMsg());
    }

    @Test
    public void testProjectSearchWithException() {
        // 准备测试数据
        String projectName = "医疗项目";
        ProjectSearchRequest request = new ProjectSearchRequest();
        request.setProjectName(projectName);

        // 模拟AiUtils抛出异常
        when(aiUtils.projectSearch(anyString())).thenThrow(new RuntimeException("查询服务异常"));

        // 执行测试
        CommonResult<ProjectSearchResponse> result = medicalCasesController.projectSearch(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1001), result.getCode());
        assertTrue(result.getMsg().contains("项目查询失败"));
        assertTrue(result.getMsg().contains("查询服务异常"));
    }
}
