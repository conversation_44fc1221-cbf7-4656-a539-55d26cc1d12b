package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StaffGroupIn {

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("显示顺序")
    private Integer showOrder;

    @ApiModelProperty("禁用标志")
    private Boolean isDisable;

    @ApiModelProperty("类型(1人社部门 2辅助调查)")
    private Integer type;

}
