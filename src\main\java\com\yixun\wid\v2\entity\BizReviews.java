package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class BizReviews {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 办事项目id
	 */
//	@NotNull(message = "办事项目id不能为空")
	private Long bizItemId;

	/**
	 * 办事项目名称
	 */
	private String bizItemName;

	/**
	 * 被评价用户id
	 */
	private Long evaluatedUserId;

	/**
	 * 评价等级
	 */
	@NotNull(message = "评价等级不能为空")
	private Integer reviewsLevel;

	/**
	 * 评价内容
	 */
	@NotNull(message = "评价内容不能为空")
	private String reviewsContent;

	/**
	 * 评价时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date reviewsTime;

	/**
	 * 评价用户id
	 */
	private Long reviewsUserId;

	/**
	 * 评价用户
	 */
	private String reviewsUserName;

	/**
	 * 评价用户联系方式
	 */
	private String reviewsUserPhone;

	/**
	 * 评价状态 0未回复 1已回复
	 */
	private Integer status;

	/**
	 * 评价回复
	 */
	private String reviewsRespContent;

	/**
	 * 评价回复时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date reviewsRespTime;

	/**
	 * 评价回复用户id
	 */
	private Long reviewsRespUserId;

	/**
	 * 评价回复用户
	 */
	private String reviewsRespUserName;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date updateTime;

	/**
	 * 是否删除（0：未删除，1：已删除）
	 */
	private Integer deleted;

}
