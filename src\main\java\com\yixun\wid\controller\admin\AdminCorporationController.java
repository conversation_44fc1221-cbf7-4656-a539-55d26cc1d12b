package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CorpAuthIn;
import com.yixun.wid.bean.in.CorporationGetIn;
import com.yixun.wid.bean.in.CorporationIn;
import com.yixun.wid.bean.out.CorporationOut;
import com.yixun.wid.entity.CorpInfo;
import com.yixun.wid.entity.CorpInfoDetail;
import com.yixun.wid.entity.Corporation;
import com.yixun.wid.entity.User;
import com.yixun.wid.entity.em.CorpAuthStatus;
import com.yixun.wid.entity.em.CorpCertifiedType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.CorpInfoService;
import com.yixun.wid.service.CorporationService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "admin企业信息")
@RestController
@RequestMapping(value = "/admin/corporation")
public class AdminCorporationController {

    @Resource
    private CorporationService corporationService;

    @Resource
    private CorpInfoService corpInfoService;

    @Resource
    private CasesService casesService;

    @Resource
    private UserService userService;

    @GetMapping("/getList")
    @ApiOperation("获取企业信息列表")
    public CommonResult<List<CorporationOut>> getList(CorporationGetIn corporationGetIn, CommonPage commonPage) {

        List<Corporation> corporationList = corporationService.getCorporationList(corporationGetIn, commonPage);
        List<CorporationOut> outList = BeanUtils.copyToOutList(corporationList, CorporationOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{corporationId}")
    @ApiOperation("获取企业信息详情")
    public CommonResult<CorporationOut> getDetail(@PathVariable("corporationId") Long corporationId) {

        Corporation corporation = corporationService.getById(corporationId);
        if (corporation==null){
            throw new DataErrorException("该企业信息不存在");
        }
        CorporationOut out = new CorporationOut();
        BeanUtils.copyProperties(corporation, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/setCorpAuth/{corporationId}")
    @ApiOperation("设置企业认证信息")
    public CommonResult<Void> setCorpAuth(@PathVariable("corporationId") Long corporationId,
                                      @RequestBody CorpAuthIn corpAuthIn) {

        Corporation corporation = corporationService.getById(corporationId);
        if (corporation==null){
            throw new DataErrorException("企业信息不存在");
        }
        corporation.setStatus(corpAuthIn.getStatus().name());
        if (corpAuthIn.getStatus().equals(CorpAuthStatus.Rejected)){
            corporation.setReason(corpAuthIn.getReason());
        }else if (corpAuthIn.getStatus().equals(CorpAuthStatus.ManualAuthenticated)){
            casesService.updateCorpName(corporation.getCompanyName(), corporation.getId());
            corporation.setReason("");
        }else {
            corporation.setReason("");
        }

        corporationService.update(corporation);

        if (corpAuthIn.getStatus().equals(CorpAuthStatus.ManualAuthenticated)){
            User user = userService.getUserById(corporation.getUserId());
            user.setOrganizationId(corporation.getId());
            user.setOrganization(corporation.getCompanyName());
            user.setOrgCreditCode(corporation.getCreditCode());
            user.setHasInsurance(corporation.getHasInsurance());
            user.setInsuranceAddress(corporation.getInsuranceAddress());
            user.setCorpCertifiedType(CorpCertifiedType.Host.name());
            userService.updateById(user);
        }

        return CommonResult.successResult("设置成功");
    }

    @PostMapping("/setMailable/{corporationId}")
    @ApiOperation("设置是否可邮寄材料")
    public CommonResult<Void> setMailable(@PathVariable("corporationId") Long corporationId,
                                      @RequestBody CorporationIn corporationIn) {

        Corporation corporation = corporationService.getById(corporationId);
        if (corporation==null){
            throw new DataErrorException("企业信息不存在");
        }
        corporation.setIsMailable(corporationIn.getIsMailable());

        corporationService.update(corporation);

        return CommonResult.successResult("设置成功");
    }

    @ApiOperation(value = "天眼查搜索公司信息")
    @GetMapping(value = "/getCorpInfo")
    public CommonResult<List<CorpInfo.ItemsBean>> getCorpInfo(@RequestParam String corpName){

        List<CorpInfo.ItemsBean> corpInfoList = corpInfoService.getCorpInfoList(corpName);

        return CommonResult.successData(corpInfoList);
    }

	@ApiOperation(value = "天眼查搜索公司详情")
	@GetMapping(value = "/getCorpDetail")
	public CommonResult<CorpInfoDetail> getCorpDetail(@RequestParam String corpName){

		CorpInfoDetail corpInfoDetail = corpInfoService.getCorpInfoDetail(corpName);

		return CommonResult.successData(corpInfoDetail);
	}

}
