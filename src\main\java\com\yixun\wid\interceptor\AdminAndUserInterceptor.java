package com.yixun.wid.interceptor;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
@AllArgsConstructor
public class AdminAndUserInterceptor extends HandlerInterceptorAdapter {

	private final AdminInterceptor adminInterceptor;

	private final UserInterceptor userInterceptor;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		String authorization = request.getHeader("Authorization");
		if (JSONUtil.isTypeJSONObject(authorization)) {
			return adminInterceptor.preHandle(request, response, handler);
		} else {
			return userInterceptor.preHandle(request, response, handler);
		}
	}
}
