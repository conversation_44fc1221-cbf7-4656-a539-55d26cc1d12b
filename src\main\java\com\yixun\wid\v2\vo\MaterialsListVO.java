package com.yixun.wid.v2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class MaterialsListVO {

	/**
	 * 清单类型
	 */
	@NotNull(message = "清单类型不能为空")
	private String materialsListType;

	/**
	 * 开始时间
	 */
	@NotNull(message = "开始时间不能为空")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date startTime;

	/**
	 * 结束时间
	 */
	@NotNull(message = "结束时间不能为空")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date endTime;

	/**
	 * 区域
	 */
	@NotNull(message = "区域不能为空")
	private List<String> area;

}
