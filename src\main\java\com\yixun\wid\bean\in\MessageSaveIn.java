package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "消息添加输入对象")
@Data
public class MessageSaveIn {

    @ApiModelProperty(value = "消息标题", required = true)
    @NotBlank(message = "消息标题不能为空")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty("业务类型（用户自定义）")
    private String businessType;

    @ApiModelProperty("业务参数")
    private String businessParam;

    @ApiModelProperty("跳转地址")
    private String redirectUrl;

    @ApiModelProperty(value = "接收用户id", required = true)
    @NotBlank(message = "接收用户id不能为空")
    private String userId;

    @ApiModelProperty(value = "所属应用程序", required = true)
    @NotBlank(message = "所属应用程序不能为空")
    private String appId;

    @ApiModelProperty(value = "类型，1消息 2待办", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

}
