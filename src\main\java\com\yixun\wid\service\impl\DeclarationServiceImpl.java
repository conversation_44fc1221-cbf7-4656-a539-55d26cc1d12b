package com.yixun.wid.service.impl;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.DeclarationGetIn;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.DeclarationLog;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.utils.AdminUserHelper;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class DeclarationServiceImpl implements DeclarationService{

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AdministratorService administratorService;

    @Override
    public void save(Declaration declaration) {
        mongoTemplate.save(declaration);
    }

    @Override
    public void update(Declaration declaration) {
        declaration.setUpdateTime(new Date());
        mongoTemplate.save(declaration);
    }

    @Override
    public List<Declaration> getDeclarationStatistic(Date startTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").gte(startTime).lte(new Date()));
        return mongoTemplate.find(query, Declaration.class);
    }

    @Override
    public void setDeclarationLog(Long declarationId, String type) {
        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        DeclarationLog declarationLog = new DeclarationLog();
        declarationLog.setId(SnGeneratorUtil.getId());
        declarationLog.setDeclarationId(declarationId);
        declarationLog.setCreateTime(new Date());
        declarationLog.setType(type);
        declarationLog.setName(administrator.getRealName());
        mongoTemplate.save(declarationLog);
    }

    @Override
    public List<DeclarationLog> getDeclarationLogList(Long declarationId, CommonPage commonPage) {
        Query query = new Query();
        query.addCriteria(Criteria.where("declarationId").is(declarationId));

        MongoUtil.setPageInfo(mongoTemplate, DeclarationLog.class, query, commonPage);
        return mongoTemplate.find(query, DeclarationLog.class);
    }

    @Override
    public List<Declaration> getDeclarationList(DeclarationGetIn declarationGetIn, CommonPage commonPage) {
        Query query = new Query();
        if (declarationGetIn.getName()!=null){
            query.addCriteria(Criteria.where("name").regex(declarationGetIn.getName()));
        }
        if (declarationGetIn.getIdCard()!=null){
            query.addCriteria(Criteria.where("idCard").is(declarationGetIn.getIdCard()));
        }
        if (declarationGetIn.getIsQueryAll()==null || !declarationGetIn.getIsQueryAll()){
            if (declarationGetIn.getStatusList()!=null){
                query.addCriteria(Criteria.where("status").in(declarationGetIn.getStatusList()));
            }
            if (declarationGetIn.getSubStatusList()!=null){
                query.addCriteria(Criteria.where("subStatus").in(declarationGetIn.getSubStatusList()));
            }else {
                query.addCriteria(Criteria.where("subStatus").isNull());
            }
        }
        if (declarationGetIn.getInjuredPart()!=null){
            query.addCriteria(Criteria.where("injuredPart").is(declarationGetIn.getInjuredPart()));
        }
        if (declarationGetIn.getStartDate()!=null&&declarationGetIn.getEndDate()!=null){
            Calendar c = Calendar.getInstance();
            c.setTime(declarationGetIn.getEndDate());
            c.add(Calendar.SECOND,86399); //结束时间加到当天的23:59:59
            query.addCriteria(Criteria.where("accidentTime").gte(declarationGetIn.getStartDate()).lte(c.getTime()));
        }
        if (declarationGetIn.getOrganization()!=null){
            query.addCriteria(Criteria.where("organization").regex(declarationGetIn.getOrganization()));
        }
        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, Declaration.class, query, commonPage);
        return mongoTemplate.find(query, Declaration.class);
    }

    @Override
    public Declaration getById(Long declarationId) {
        return mongoTemplate.findById(declarationId, Declaration.class);
    }
}




