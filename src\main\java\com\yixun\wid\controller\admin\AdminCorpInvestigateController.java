package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.out.CorpInvestigateOut;
import com.yixun.wid.entity.CorpInvestigate;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CorpInvestigateService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "admin第三方企业调研")
@RestController
@RequestMapping(value = "/admin/corpInvestigate")
public class AdminCorpInvestigateController {

    @Resource
    private CorpInvestigateService corpInvestigateService;

    @GetMapping("/getList")
    @ApiOperation("获取企业调研列表")
    public CommonResult<List<CorpInvestigateOut>> getList(String companyName, CommonPage commonPage) {

        List<CorpInvestigate> corpInvestigateList = corpInvestigateService.getList(null, companyName, commonPage);
        List<CorpInvestigateOut> outList = BeanUtils.copyToOutList(corpInvestigateList, CorpInvestigateOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/get/{corpInvestigateId}")
    @ApiOperation("获取企业调研详情")
    public CommonResult<CorpInvestigateOut> getDetail(@PathVariable("corpInvestigateId") Long corpInvestigateId) {

        CorpInvestigate corpInvestigate = corpInvestigateService.getById(corpInvestigateId);
        if (corpInvestigate==null){
            throw new DataErrorException("该企业调研信息不存在");
        }
        CorpInvestigateOut out = new CorpInvestigateOut();
        BeanUtils.copyProperties(corpInvestigate, out);

        return CommonResult.successData(out);
    }

}