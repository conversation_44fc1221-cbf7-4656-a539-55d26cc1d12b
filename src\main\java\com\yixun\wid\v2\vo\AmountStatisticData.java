package com.yixun.wid.v2.vo;

import lombok.Data;

/**
 * 金额统计数据
 */
@Data
public class AmountStatisticData {
    /**
     * 申请总金额（单位：分）
     */
    private Integer totalAmountInCent = 0;

    /**
     * 可报销总金额（单位：分）
     */
    private Integer totalReimbursableAmountInCent = 0;

    /**
     * 不可报销总金额（单位：分）
     */
    private Integer totalNonReimbursableAmountInCent = 0;

    /**
     * 伙食补助总金额（单位：分）
     */
    private Integer totalFoodAllowanceInCent = 0;
} 