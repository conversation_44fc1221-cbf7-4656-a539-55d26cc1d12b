package com.yixun.wid.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.wid.bean.weixin.AccessToken;
import com.yixun.wid.bean.weixin.MiniSession;
import com.yixun.wid.entity.em.UserType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.WeixinService;
import com.yixun.wid.utils.OkHttpKit;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.WxaCodeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class WeixinServiceImpl implements WeixinService {

    private static final Logger log = LoggerFactory.getLogger(WeixinServiceImpl.class);

    private String baseUrl = "https://api.weixin.qq.com/sns";

    @Value("${weixin.appId}")
    private String appId;

    @Value("${weixin.appSecret}")
    private String appSecret;

    @Resource(name="customRedisTemplate")
    private RedisTemplate redisTemplate;

	@Value("${weixin.page}")
	private String page;


    @Override
    public MiniSession getMiniSessionByCode(String weixinCode) {
        String sessionKey = "MiniSession:"+weixinCode;
        MiniSession miniSession = (MiniSession) redisTemplate.opsForValue().get(sessionKey);
        if (miniSession == null) {
            miniSession = getMiniSession(weixinCode);
            redisTemplate.opsForValue().set(sessionKey, miniSession, 2, TimeUnit.HOURS);
            redisTemplate.opsForValue().set("MiniOpenId:"+miniSession.getOpenid(), miniSession, 2, TimeUnit.HOURS);
        }
        return miniSession;
    }

    @Override
    public MiniSession getMiniSessionByOpenId(String openId) {
        MiniSession miniSession = (MiniSession) redisTemplate.opsForValue().get("MiniOpenId:"+openId);
        if (miniSession == null) {
            throw new DataErrorException("获取微信信息错误，请重新登录。");
        }
        return miniSession;
    }

	@Override
	public AccessToken getJSAccessToken(UserType userType) {
		String accessTokenKey = "WeixinAccessToken:" + userType.name();
		AccessToken accessToken = (AccessToken) redisTemplate.opsForValue().get(accessTokenKey);
		if (accessToken == null) {
			Map<String, String> params = new HashMap<>();
			if (userType.equals(UserType.corporation)){
				params.put("appid", appId);
				params.put("secret", appSecret);
			}
//			else if (userType.equals(UserType.government)){
//				params.put("appid", govAppId);
//				params.put("secret", govAppSecret);
//			} else if (userType.equals(UserType.secureCloudApplet)) {
//				params.put("appid", secureCloudAppId);
//				params.put("secret", secureCloudSecret);
//			}
			params.put("grant_type", "client_credential");
			try {
				log.info("获取accessToken参数: {}", params);
				JSONObject connGet = OkHttpKit.connGet("https://api.weixin.qq.com/cgi-bin/token", params);
				log.info("获取accessToken结果: {}", connGet);
				if (connGet.containsKey("errcode")) {
					throw new DataErrorException("获取accessToken出错: " + connGet.getString("errmsg"));
				} else {
					accessToken = connGet.toJavaObject(AccessToken.class);
				}
			} catch (IOException e) {

				throw new DataErrorException(e.getMessage());
			}
		redisTemplate.opsForValue().set(accessTokenKey, accessToken, 6000, TimeUnit.SECONDS);
		}

		return accessToken;
	}

	@Override
	public WxaCodeVO getWxaCode(String id) {
		Map<String, String> params = new HashMap<>();
		params.put("scene", id);
		params.put("page", page);
		try {
			String connPost = OkHttpKit.connPostInJsonBase64("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="
				+ getJSAccessToken(UserType.corporation).getAccess_token(), JSON.toJSONString(params));
			String body = new String(Base64.getDecoder().decode(connPost));
			if (body.startsWith("{")) {
				JSONObject jsonObject = JSON.parseObject(body);
				throw new RuntimeException(jsonObject.getString("errmsg"));
			}
			WxaCodeVO wxaCodeVO = new WxaCodeVO();
			wxaCodeVO.setCode(connPost);
			return wxaCodeVO;
		} catch (IOException e) {
			log.error(e.getMessage());
			throw new RuntimeException("获取小程序码失败");
		}
	}


	private MiniSession getMiniSession(String weixinCode) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("js_code", weixinCode);
        params.put("grant_type", "authorization_code");
        try {
            JSONObject connGet = OkHttpKit.connGet("https://api.weixin.qq.com/sns/jscode2session", params);
            if (connGet.containsKey("errcode") && !connGet.getString("errcode").equals("0")) {
                throw new DataErrorException("获取MiniSession出错: " + connGet.getString("errmsg"));
            } else {
                return connGet.toJavaObject(MiniSession.class);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

}
