package com.yixun.wid.v2.vo;

import lombok.Data;

/**
 * 材料分类进度跟踪VO
 */
@Data
public class MaterialClassificationProgressVO {
    
    /**
     * 进度状态码
     * 0: 处理中
     * 1: 成功
     * -1: 失败
     */
    private Integer code;
    
    /**
     * 数据内容
     * 成功时为AI响应数据，失败时为错误信息
     */
    private Object data;
    
    /**
     * 构造函数 - 处理中状态
     */
    public MaterialClassificationProgressVO() {
        this.code = 0;
        this.data = "正在处理中...";
    }
    
    /**
     * 构造函数 - 指定状态和数据
     * @param code 状态码
     * @param data 数据
     */
    public MaterialClassificationProgressVO(Integer code, Object data) {
        this.code = code;
        this.data = data;
    }
    
    /**
     * 创建处理中状态的进度对象
     * @return 处理中状态的进度对象
     */
    public static MaterialClassificationProgressVO processing() {
        return new MaterialClassificationProgressVO(0, "正在处理中...");
    }
    
    /**
     * 创建成功状态的进度对象
     * @param data AI响应数据
     * @return 成功状态的进度对象
     */
    public static MaterialClassificationProgressVO success(Object data) {
        return new MaterialClassificationProgressVO(1, data);
    }
    
    /**
     * 创建失败状态的进度对象
     * @param errorMessage 错误信息
     * @return 失败状态的进度对象
     */
    public static MaterialClassificationProgressVO failed(String errorMessage) {
        return new MaterialClassificationProgressVO(-1, errorMessage);
    }
}
