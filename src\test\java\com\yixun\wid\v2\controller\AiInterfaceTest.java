package com.yixun.wid.v2.controller;

import com.yixun.wid.v2.entity.MedicalCases;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisRequest;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisResponse;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisVO;
import com.yixun.wid.v2.vo.ai.BillInformationResponse;
import com.yixun.wid.v2.vo.ai.SurgicalInformationResponse;
import com.yixun.wid.v2.vo.ai.SurgicalInformationVO;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import cn.hutool.core.util.StrUtil;

/**
 * AI接口测试类
 * 测试新添加的受理信息和临床诊断、手术信息接口
 */
public class AiInterfaceTest {

    @Test
    public void testAcceptedInformationDiagnosisRequestCreation() {
        // 测试创建AcceptedInformationDiagnosisRequest对象
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();
        
        // 创建申请材料
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        request.setApplication(application);

        // 创建就诊材料
        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();
        
        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历子类.pdf"));
        medicalRecord.setMedicalConditionCertificate(Collections.singletonList("病情证明.pdf"));
        materials.setMedicalRecord(medicalRecord);
        
        materials.setElectronicInvoice(Collections.singletonList("电子发票.pdf"));
        visit.setMaterial(materials);
        request.setMedical(Collections.singletonList(visit));

        // 验证对象创建成功
        assert request.getApplication() != null;
        assert request.getMedical() != null;
        assert !request.getMedical().isEmpty();
        assert request.getMedical().get(0).getMaterial().getMedicalRecord() != null;
        
        System.out.println("AcceptedInformationDiagnosisRequest对象创建测试通过");
    }

    @Test
    public void testSurgicalInformationResponseCreation() {
        // 测试创建SurgicalInformationResponse对象
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Collections.singletonList("阑尾切除术"));
        response.setData(data);

        // 验证对象创建成功
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().getSurgicalName() != null;
        assert !response.getData().getSurgicalName().isEmpty();
        assert "阑尾切除术".equals(response.getData().getSurgicalName().get(0));
        
        System.out.println("SurgicalInformationResponse对象创建测试通过");
    }

    @Test
    public void testAcceptedInformationDiagnosisResponseCreation() {
        // 测试创建AcceptedInformationDiagnosisResponse对象
        AcceptedInformationDiagnosisResponse response = new AcceptedInformationDiagnosisResponse();
        response.setStatus("success");
        response.setMessage("受理信息和临床诊断识别成功");

        // 验证对象创建成功
        assert "success".equals(response.getStatus());
        assert "受理信息和临床诊断识别成功".equals(response.getMessage());
        
        System.out.println("AcceptedInformationDiagnosisResponse对象创建测试通过");
    }

    @Test
    public void testBillInformationResponseCreation() {
        // 测试创建BillInformationResponse对象
        BillInformationResponse response = new BillInformationResponse();
        response.setStatus("success");
        response.setMessage("账单信息识别成功");

        BillInformationResponse.BillData data =
            new BillInformationResponse.BillData();

        BillInformationResponse.BillInformation billInfo =
            new BillInformationResponse.BillInformation();
        billInfo.setBillNumber("INV20240101001");
        billInfo.setHospital("测试医院");
        billInfo.setTreatmentType("住院");
        billInfo.setBillAmount(new BigDecimal("1500.00"));

        data.setBillInformation(Collections.singletonList(billInfo));
        response.setData(data);

        // 验证对象创建成功
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().getBillInformation() != null;
        assert !response.getData().getBillInformation().isEmpty();
        assert "INV20240101001".equals(response.getData().getBillInformation().get(0).getBillNumber());
        assert "测试医院".equals(response.getData().getBillInformation().get(0).getHospital());

        System.out.println("BillInformationResponse对象创建测试通过");
    }

    /**
     * 测试接口路径和参数验证
     * 这个测试主要验证接口的基本结构，不进行实际的HTTP调用
     */
    @Test
    public void testInterfaceStructure() {
        // 验证接口路径
        String acceptedInfoPath = "/v2/medical/cases/accepted-information-diagnosis";
        String surgicalInfoPath = "/v2/medical/cases/surgical-information";
        String billInfoPath = "/v2/medical/cases/bill-information";

        // 验证路径格式正确
        assert acceptedInfoPath.startsWith("/v2/medical/cases/");
        assert surgicalInfoPath.startsWith("/v2/medical/cases/");
        assert billInfoPath.startsWith("/v2/medical/cases/");

        // 验证参数类型
        Long testId = 123456L;
        assert testId instanceof Long;

        System.out.println("接口结构验证测试通过");
        System.out.println("受理信息和临床诊断接口路径: " + acceptedInfoPath);
        System.out.println("手术信息接口路径: " + surgicalInfoPath);
        System.out.println("账单信息接口路径: " + billInfoPath);
    }

    @Test
    public void testInterfaceParameterOptions() {
        // 测试接口支持两种参数方式

        // 方式1：通过ID调用（模拟）
        Long testId = 123456L;
        assert testId != null;
        System.out.println("方式1 - 通过ID调用: id=" + testId);

        // 方式2：直接传入Request对象（模拟）
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();

        // 创建申请材料
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        request.setApplication(application);

        // 创建就诊材料
        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();

        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历子类.pdf"));
        materials.setMedicalRecord(medicalRecord);

        visit.setMaterial(materials);
        request.setMedical(Collections.singletonList(visit));

        assert request != null;
        assert request.getApplication() != null;
        assert request.getMedical() != null;

        System.out.println("方式2 - 直接传入Request对象: 对象创建成功");
        System.out.println("接口参数选项验证测试通过");
    }

    /**
     * 测试数据映射功能
     * 验证AcceptedInformationDiagnosisResponse.DiagnosisData到MedicalCases的映射
     */
    @Test
    public void testDataMappingFromResponseToMedicalCases() {
        // 创建AcceptedInformationDiagnosisResponse对象
        AcceptedInformationDiagnosisResponse response = new AcceptedInformationDiagnosisResponse();
        response.setStatus("success");
        response.setMessage("受理信息和临床诊断识别成功");

        // 创建DiagnosisData对象
        AcceptedInformationDiagnosisResponse.DiagnosisData data =
            new AcceptedInformationDiagnosisResponse.DiagnosisData();
        data.setAccidentDate("2024-01-15");
        data.setEmployeeName("李四");
        data.setGender("女");
        data.setIdNumber("110101199002021234");
        data.setEmployerName("测试企业有限公司");
        data.setClinicalDiagnosis(Arrays.asList("右腿骨折", "软组织损伤", "轻微脑震荡"));

        response.setData(data);

        // 创建MedicalCases对象用于映射测试
        MedicalCases medicalCases = new MedicalCases();
        medicalCases.setId(123456L);
        medicalCases.setCaseNumber("BX101420250721001");

        // 验证映射前的状态
        assert medicalCases.getWorkerName() == null;
        assert medicalCases.getGender() == null;
        assert medicalCases.getIdCard() == null;
        assert medicalCases.getOrganization() == null;
        assert medicalCases.getAccidentDate() == null;
        assert medicalCases.getInjuryDiagnoses() == null;

        // 模拟数据映射过程（这里只是验证数据结构，实际映射在Controller中进行）
        // 在实际应用中，这些映射会通过mapDiagnosisDataToMedicalCases方法完成

        // 验证响应数据的完整性
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert "2024-01-15".equals(response.getData().getAccidentDate());
        assert "李四".equals(response.getData().getEmployeeName());
        assert "女".equals(response.getData().getGender());
        assert "110101199002021234".equals(response.getData().getIdNumber());
        assert "测试企业有限公司".equals(response.getData().getEmployerName());
        assert response.getData().getClinicalDiagnosis() != null;
        assert response.getData().getClinicalDiagnosis().size() == 3;
        assert response.getData().getClinicalDiagnosis().contains("右腿骨折");
        assert response.getData().getClinicalDiagnosis().contains("软组织损伤");
        assert response.getData().getClinicalDiagnosis().contains("轻微脑震荡");

        System.out.println("数据映射测试通过");
        System.out.println("AI识别数据:");
        System.out.println("  事故日期: " + data.getAccidentDate());
        System.out.println("  职工姓名: " + data.getEmployeeName());
        System.out.println("  性别: " + data.getGender());
        System.out.println("  身份证号: " + data.getIdNumber());
        System.out.println("  用人单位: " + data.getEmployerName());
        System.out.println("  临床诊断: " + data.getClinicalDiagnosis());
    }

    /**
     * 测试优化后的接口返回类型
     * 验证接口现在返回AcceptedInformationDiagnosisVO而不是MedicalCases
     * 注意：接口仅返回识别结果，不会自动保存到数据库
     */
    @Test
    public void testOptimizedInterfaceReturnType() {
        // 验证新的返回类型结构
        AcceptedInformationDiagnosisVO vo = new AcceptedInformationDiagnosisVO();
        vo.setId(123456L);
        vo.setWorkerName("张三");
        vo.setGender("男");
        vo.setIdCard("110101199001011234");
        vo.setOrganization("测试公司");
        vo.setInjuryDiagnoses(Arrays.asList("左手骨折", "软组织挫伤"));

        // 验证AcceptedInformationDiagnosisVO对象包含所有必要的字段
        assert vo.getId() != null;
        assert vo.getWorkerName() != null;
        assert vo.getGender() != null;
        assert vo.getIdCard() != null;
        assert vo.getOrganization() != null;
        assert vo.getInjuryDiagnoses() != null;
        assert !vo.getInjuryDiagnoses().isEmpty();

        // 验证字段值的正确性
        assert Long.valueOf(123456L).equals(vo.getId());
        assert "张三".equals(vo.getWorkerName());
        assert "男".equals(vo.getGender());
        assert "110101199001011234".equals(vo.getIdCard());
        assert "测试公司".equals(vo.getOrganization());
        assert vo.getInjuryDiagnoses().contains("左手骨折");
        assert vo.getInjuryDiagnoses().contains("软组织挫伤");

        System.out.println("优化后接口返回类型测试通过");
        System.out.println("返回的AcceptedInformationDiagnosisVO对象包含:");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  职工姓名: " + vo.getWorkerName());
        System.out.println("  性别: " + vo.getGender());
        System.out.println("  身份证号: " + vo.getIdCard());
        System.out.println("  用人单位: " + vo.getOrganization());
        System.out.println("  工伤诊断: " + vo.getInjuryDiagnoses());
        System.out.println("注意：此对象仅用于展示AI识别结果，未自动保存到数据库");
    }

    /**
     * 测试接口不自动保存数据的特性
     * 验证接口仅返回识别结果，不会修改数据库
     */
    @Test
    public void testInterfaceDoesNotAutoSave() {
        // 创建模拟的AI识别结果
        AcceptedInformationDiagnosisResponse response = new AcceptedInformationDiagnosisResponse();
        response.setStatus("success");

        AcceptedInformationDiagnosisResponse.DiagnosisData data =
            new AcceptedInformationDiagnosisResponse.DiagnosisData();
        data.setEmployeeName("测试用户");
        data.setGender("男");
        data.setIdNumber("123456789012345678");
        response.setData(data);

        // 创建原始MedicalCases对象
        MedicalCases originalCase = new MedicalCases();
        originalCase.setId(999L);
        originalCase.setWorkerName("原始姓名");
        originalCase.setGender("女");
        originalCase.setIdCard("987654321098765432");

        // 创建用于返回的新VO对象（模拟接口行为）
        AcceptedInformationDiagnosisVO resultVO = new AcceptedInformationDiagnosisVO();
        resultVO.setId(originalCase.getId()); // 保留原有ID

        // 映射AI识别数据
        if (response.getData() != null) {
            resultVO.setWorkerName(response.getData().getEmployeeName());
            resultVO.setGender(response.getData().getGender());
            resultVO.setIdCard(response.getData().getIdNumber());
        }

        // 验证：原始对象未被修改
        assert "原始姓名".equals(originalCase.getWorkerName());
        assert "女".equals(originalCase.getGender());
        assert "987654321098765432".equals(originalCase.getIdCard());

        // 验证：返回VO对象包含AI识别数据
        assert "测试用户".equals(resultVO.getWorkerName());
        assert "男".equals(resultVO.getGender());
        assert "123456789012345678".equals(resultVO.getIdCard());
        assert originalCase.getId().equals(resultVO.getId()); // ID保持一致

        System.out.println("接口不自动保存数据特性测试通过");
        System.out.println("原始数据未被修改，返回新的包含AI识别结果的VO对象");
    }

    /**
     * 测试AI响应状态判断逻辑
     * 验证不同状态下的处理逻辑
     */
    @Test
    public void testAiResponseStatusValidation() {
        // 测试成功状态
        AcceptedInformationDiagnosisResponse successResponse = new AcceptedInformationDiagnosisResponse();
        successResponse.setStatus("success");
        successResponse.setMessage("识别成功");

        AcceptedInformationDiagnosisResponse.DiagnosisData data =
            new AcceptedInformationDiagnosisResponse.DiagnosisData();
        data.setEmployeeName("测试用户");
        successResponse.setData(data);

        // 验证成功状态
        assert "success".equals(successResponse.getStatus());
        assert successResponse.getData() != null;
        System.out.println("成功状态验证通过: " + successResponse.getStatus());

        // 测试失败状态
        AcceptedInformationDiagnosisResponse failResponse = new AcceptedInformationDiagnosisResponse();
        failResponse.setStatus("error");
        failResponse.setMessage("识别失败：文件格式不支持");

        // 验证失败状态
        assert "error".equals(failResponse.getStatus());
        assert !"success".equals(failResponse.getStatus());
        System.out.println("失败状态验证通过: " + failResponse.getStatus());

        // 测试空状态
        AcceptedInformationDiagnosisResponse emptyStatusResponse = new AcceptedInformationDiagnosisResponse();
        emptyStatusResponse.setStatus("");
        emptyStatusResponse.setMessage("状态为空");

        // 验证空状态
        assert "".equals(emptyStatusResponse.getStatus());
        assert !"success".equals(emptyStatusResponse.getStatus());
        System.out.println("空状态验证通过");

        // 测试null状态
        AcceptedInformationDiagnosisResponse nullStatusResponse = new AcceptedInformationDiagnosisResponse();
        nullStatusResponse.setStatus(null);
        nullStatusResponse.setMessage("状态为null");

        // 验证null状态
        assert nullStatusResponse.getStatus() == null;
        assert !"success".equals(nullStatusResponse.getStatus());
        System.out.println("null状态验证通过");

        System.out.println("AI响应状态判断逻辑测试通过");
        System.out.println("支持的状态判断:");
        System.out.println("  - success: 调用成功，继续处理");
        System.out.println("  - error/其他: 调用失败，抛出异常");
        System.out.println("  - 空字符串/null: 调用失败，抛出异常");
    }

    /**
     * 测试错误处理逻辑
     * 模拟各种错误情况的处理
     */
    @Test
    public void testErrorHandlingLogic() {
        // 模拟不同的错误响应情况

        // 情况1：响应为null
        AcceptedInformationDiagnosisResponse nullResponse = null;
        boolean shouldThrowException1 = (nullResponse == null);
        assert shouldThrowException1;
        System.out.println("null响应检测通过");

        // 情况2：status为error
        AcceptedInformationDiagnosisResponse errorResponse = new AcceptedInformationDiagnosisResponse();
        errorResponse.setStatus("error");
        errorResponse.setMessage("AI服务内部错误");
        boolean shouldThrowException2 = !"success".equals(errorResponse.getStatus());
        assert shouldThrowException2;
        System.out.println("error状态检测通过: " + errorResponse.getMessage());

        // 情况3：status为failed
        AcceptedInformationDiagnosisResponse failedResponse = new AcceptedInformationDiagnosisResponse();
        failedResponse.setStatus("failed");
        failedResponse.setMessage("文档解析失败");
        boolean shouldThrowException3 = !"success".equals(failedResponse.getStatus());
        assert shouldThrowException3;
        System.out.println("failed状态检测通过: " + failedResponse.getMessage());

        // 情况4：status为空字符串
        AcceptedInformationDiagnosisResponse emptyResponse = new AcceptedInformationDiagnosisResponse();
        emptyResponse.setStatus("");
        emptyResponse.setMessage("状态为空");
        boolean shouldThrowException4 = "".equals(emptyResponse.getStatus()) || !"success".equals(emptyResponse.getStatus());
        assert shouldThrowException4;
        System.out.println("空状态检测通过");

        System.out.println("错误处理逻辑测试通过");
        System.out.println("所有非success状态都会触发异常处理");
    }

    /**
     * 测试完整的状态判断流程
     * 模拟接口中的状态判断逻辑
     */
    @Test
    public void testCompleteStatusValidationFlow() {
        System.out.println("=== 完整状态判断流程测试 ===");

        // 模拟接口中的状态判断逻辑
        AcceptedInformationDiagnosisResponse[] testResponses = {
            null,  // 情况1：响应为null
            createResponse(null, "状态为null"),  // 情况2：status为null
            createResponse("", "状态为空字符串"),  // 情况3：status为空字符串
            createResponse("error", "AI服务内部错误"),  // 情况4：status为error
            createResponse("failed", "文档解析失败"),  // 情况5：status为failed
            createResponse("timeout", "请求超时"),  // 情况6：status为timeout
            createResponse("success", "识别成功")  // 情况7：status为success
        };

        String[] expectedResults = {
            "应抛出异常：AI服务返回空响应",
            "应抛出异常：status=null",
            "应抛出异常：status=空字符串",
            "应抛出异常：status=error",
            "应抛出异常：status=failed",
            "应抛出异常：status=timeout",
            "应继续处理：status=success"
        };

        for (int i = 0; i < testResponses.length; i++) {
            AcceptedInformationDiagnosisResponse response = testResponses[i];
            String expected = expectedResults[i];

            boolean shouldThrowException = shouldThrowExceptionForResponse(response);

            System.out.println(String.format("测试 %d: %s - %s",
                i + 1,
                expected,
                shouldThrowException ? "✓ 正确" : "✗ 错误"));

            if (i < testResponses.length - 1) {
                assert shouldThrowException : "前6种情况都应该抛出异常";
            } else {
                assert !shouldThrowException : "success状态不应该抛出异常";
            }
        }

        System.out.println("完整状态判断流程测试通过");
    }

    /**
     * 创建测试用的AcceptedInformationDiagnosisResponse对象
     */
    private AcceptedInformationDiagnosisResponse createResponse(String status, String message) {
        AcceptedInformationDiagnosisResponse response = new AcceptedInformationDiagnosisResponse();
        response.setStatus(status);
        response.setMessage(message);
        return response;
    }

    /**
     * 模拟接口中的状态判断逻辑
     * 返回是否应该抛出异常
     */
    private boolean shouldThrowExceptionForResponse(AcceptedInformationDiagnosisResponse response) {
        // 检查AI调用状态（模拟接口中的逻辑）
        if (response == null) {
            return true; // 应抛出异常：AI服务返回空响应
        }

        if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
            return true; // 应抛出异常：status不为success
        }

        return false; // 不应抛出异常：status为success
    }

    /**
     * 测试手术信息识别接口的优化返回类型
     * 验证接口现在返回SurgicalInformationVO而不是SurgicalInformationResponse
     * 注意：接口仅返回识别结果，不会自动保存到数据库
     */
    @Test
    public void testSurgicalInformationOptimizedReturnType() {
        // 验证新的返回类型结构
        SurgicalInformationVO vo = new SurgicalInformationVO();
        vo.setId(123456L);
        vo.setSurgicalNames(Arrays.asList("阑尾切除术", "胆囊切除术", "疝气修补术"));

        // 验证SurgicalInformationVO对象包含所有必要的字段
        assert vo.getId() != null;
        assert vo.getSurgicalNames() != null;
        assert !vo.getSurgicalNames().isEmpty();

        // 验证字段值的正确性
        assert Long.valueOf(123456L).equals(vo.getId());
        assert vo.getSurgicalNames().size() == 3;
        assert vo.getSurgicalNames().contains("阑尾切除术");
        assert vo.getSurgicalNames().contains("胆囊切除术");
        assert vo.getSurgicalNames().contains("疝气修补术");

        System.out.println("手术信息识别接口优化返回类型测试通过");
        System.out.println("返回的SurgicalInformationVO对象包含:");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  手术名称: " + vo.getSurgicalNames());
        System.out.println("注意：此对象仅用于展示AI识别结果，未自动保存到数据库");
    }

    /**
     * 测试手术信息识别接口不自动保存数据的特性
     * 验证接口仅返回识别结果，不会修改数据库
     */
    @Test
    public void testSurgicalInformationInterfaceDoesNotAutoSave() {
        // 创建模拟的AI识别结果
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data =
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("心脏搭桥手术", "瓣膜置换术"));
        response.setData(data);

        // 创建原始MedicalCases对象
        MedicalCases originalCase = new MedicalCases();
        originalCase.setId(999L);
        originalCase.setWorkerName("原始患者");

        // 创建用于返回的新VO对象（模拟接口行为）
        SurgicalInformationVO resultVO = new SurgicalInformationVO();
        resultVO.setId(originalCase.getId()); // 保留原有ID

        // 映射AI识别数据
        if (response.getData() != null && response.getData().getSurgicalName() != null) {
            resultVO.setSurgicalNames(response.getData().getSurgicalName());
        }

        // 验证：原始对象未被修改
        assert "原始患者".equals(originalCase.getWorkerName());
        assert originalCase.getId().equals(999L);

        // 验证：返回VO对象包含AI识别数据
        assert resultVO.getSurgicalNames() != null;
        assert resultVO.getSurgicalNames().size() == 2;
        assert resultVO.getSurgicalNames().contains("心脏搭桥手术");
        assert resultVO.getSurgicalNames().contains("瓣膜置换术");
        assert originalCase.getId().equals(resultVO.getId()); // ID保持一致

        System.out.println("手术信息识别接口不自动保存数据特性测试通过");
        System.out.println("原始数据未被修改，返回新的包含AI识别结果的VO对象");
    }
}
