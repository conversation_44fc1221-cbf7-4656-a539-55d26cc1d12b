package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.AdminLoginIn;
import com.yixun.wid.bean.other.LoginToken;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.AsyncService;
import com.yixun.wid.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Api(tags = "admin管理员登录")
@RestController
@RequestMapping("/admin_login")
public class AdminLoginController {

    @Resource
    private AdministratorService administratorService;

    @Resource
    private AsyncService asyncService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @GetMapping("/get_image_code")
    @ApiOperation(value = "登录前获取验证图片")
    public CommonResult getVerifyCode(String mobileCode, HttpServletResponse resp){

        if (mobileCode==null)
            throw new ParameterErrorException("参数错误");

        //生成随机字串
        String verifyCode = ImageVerifyCodeUtil.generateVerifyCode(4);
        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminCode(mobileCode), verifyCode, 5, TimeUnit.MINUTES);

        //生成图片
        int width = 100;//宽
        int height = 40;//高
        try {
            ImageVerifyCodeUtil.outputImage(width, height, resp.getOutputStream(), verifyCode);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return CommonResult.successResult(null);
    }

    @PostMapping("/login")
    @ApiOperation(value = "系统登录")
    public CommonResult<LoginToken> login(@RequestBody AdminLoginIn adminLoginIn){

        if (!BeanFieldCheckingUtils.forAllFieldNotNull(adminLoginIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        //检查验证码
        String verifyCode = (String) redisTemplate.opsForValue().get(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));
        if (!adminLoginIn.getVerifyCode().toUpperCase().equals(verifyCode)){
            throw new DataErrorException("验证码错误或已过期");
        }
        redisTemplate.delete(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));

        Administrator administrator = administratorService.getAdministratorByName(adminLoginIn.getUsername());
        if (administrator==null){
            throw new DataErrorException("该用户不存在");
        }
        if (administrator.getIsDisable()){
            throw new DataErrorException("该用户已被禁用");
        }

        //5次密码错误锁定10分钟
        LoginLockoutUtil.loginCheck(redisTemplate, adminLoginIn.getUsername(),
                administrator.getPassword().equals(adminLoginIn.getPassword()));

        LoginToken loginToken = new LoginToken();
        loginToken.setToken(administrator.getToken());
        String random = Base64.encodeBase64String((Math.random() + "").substring(3, 10).getBytes());
        loginToken.setRandom(random);

        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminUserToken(administrator.getToken()),
                random + "#" + adminLoginIn.getMobileCode()+ "#" + administrator.getId(),
                60, TimeUnit.MINUTES);

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String realIP = getRealIP(request);
        administrator.setLastLoginIp(realIP);
        administrator.setLastLoginTime(new Date());
        administratorService.update(administrator);

        asyncService.recordLogLogin("/admin_login/login", administrator.getUsername(), administrator.getId(),
                adminLoginIn.getMobileCode(), realIP);

        return CommonResult.successData(loginToken);
    }

    @PostMapping("/logout")
    @ApiOperation(value = "退出登录")
    public CommonResult<Void> logout(){

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        redisTemplate.delete(RedisKeyResolver.getAdminUserToken(administrator.getToken()));

        return CommonResult.successResult(null);
    }

    String getRealIP(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
