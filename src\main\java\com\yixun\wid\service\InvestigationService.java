package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.InvestigationGetIn;
import com.yixun.wid.entity.Investigation;

import java.util.Date;
import java.util.List;

public interface InvestigationService {

    void save(Investigation investigation);

    Investigation getById(Long investigationId);

    void update(Investigation investigation);

    void delete(Investigation investigation);

    List<Investigation> getList(InvestigationGetIn getIn, CommonPage commonPage);

    List<Investigation> getInvestigationStatistic(Date startTime);
}
