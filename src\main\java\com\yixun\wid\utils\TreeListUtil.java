package com.yixun.wid.utils;

import com.yixun.wid.bean.out.GroupStaffOut;
import com.yixun.wid.entity.AreaDictMap;
import com.yixun.wid.entity.DataDictMap;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupMap;

import java.util.ArrayList;
import java.util.List;

public class TreeListUtil {

    public static void groupStaffTreeList(List<GroupStaffOut> treeListResult, List<GroupStaffOut> list, Long parentId) {
        for (GroupStaffOut each : list) {
            if (parentId.equals(each.getParentId())) {
                groupStaffTreeList(treeListResult, list, each.getStaffGroupId());
                if (each.getAdministratorId()!=null){
                    treeListResult.add(each);
                }
            }
        }
    }

    public static List<StaffGroupMap> staffGroupTreeList(List<StaffGroupMap> list, Long parentId, String parentName) {
        List<StaffGroupMap> treeListResult = new ArrayList<>();
        for (StaffGroupMap each : list) {
            if (parentId.equals(each.getParentId())) {
                each.setChildren(staffGroupTreeList(list,each.getId(),each.getGroupName()));
                each.setParentName(parentName);
                treeListResult.add(each);
            }
        }
        return treeListResult;
    }

    public static void subGroupTreeList(List<Long> treeListResult, List<StaffGroup> list, Long parentId) {
        for (StaffGroup each : list) {
            if (parentId.equals(each.getParentId())) {
                subGroupTreeList(treeListResult, list, each.getId());
                treeListResult.add(each.getId());
            }
        }
    }

    public static List<AreaDictMap> areaDictTreeList(List<AreaDictMap> list, Long parentId) {
        List<AreaDictMap> treeListResult = new ArrayList<>();
        for (AreaDictMap each : list) {
            if (parentId.equals(each.getPcode())) {
                each.setChildren(areaDictTreeList(list,each.getCode()));
                treeListResult.add(each);
            }
        }
        return treeListResult;
    }

    public static List<DataDictMap> dataDictTreeList(List<DataDictMap> list, String parentCode) {
        List<DataDictMap> treeListResult = new ArrayList<>();
        for (DataDictMap each : list) {
            if (parentCode.equals(each.getParentcode())) {
                each.setChildren(dataDictTreeList(list,each.getNodecode()));
                treeListResult.add(each);
            }
        }
        return treeListResult;
    }
}