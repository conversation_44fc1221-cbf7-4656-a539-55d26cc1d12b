package com.yixun.wid.v2.controller;

import com.yixun.wid.v2.vo.ai.SurgicalInformationResponse;
import com.yixun.wid.v2.vo.ai.SurgicalInformationVO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * 手术信息识别接口测试类
 * 测试Controller中的映射逻辑和返回类型
 */
public class SurgicalInformationControllerTest {

    /**
     * 模拟Controller中的映射方法
     * @param vo 目标VO对象
     * @param response AI识别响应结果
     */
    private void mapSurgicalDataToVO(SurgicalInformationVO vo, SurgicalInformationResponse response) {
        if (response == null || response.getData() == null) {
            return;
        }

        SurgicalInformationResponse.SurgicalData data = response.getData();

        // 映射手术名称列表
        if (data.getSurgicalName() != null && !data.getSurgicalName().isEmpty()) {
            vo.setSurgicalNames(data.getSurgicalName());
        }
    }

    @Test
    public void testSurgicalInformationMappingWithValidData() {
        // 测试：有效数据的映射
        
        // 创建模拟的AI识别结果
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("阑尾切除术", "胆囊切除术"));
        response.setData(data);

        // 创建VO对象并设置案件ID
        SurgicalInformationVO vo = new SurgicalInformationVO();
        vo.setId(12345L);

        // 执行映射
        mapSurgicalDataToVO(vo, response);

        // 验证映射结果
        assert vo.getId().equals(12345L);
        assert vo.getSurgicalNames() != null;
        assert vo.getSurgicalNames().size() == 2;
        assert vo.getSurgicalNames().contains("阑尾切除术");
        assert vo.getSurgicalNames().contains("胆囊切除术");

        System.out.println("测试通过：有效数据的映射");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  手术名称: " + vo.getSurgicalNames());
    }

    @Test
    public void testSurgicalInformationMappingWithEmptyData() {
        // 测试：空数据的映射
        
        // 创建空的AI识别结果
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList()); // 空列表
        response.setData(data);

        // 创建VO对象
        SurgicalInformationVO vo = new SurgicalInformationVO();
        vo.setId(54321L);

        // 执行映射
        mapSurgicalDataToVO(vo, response);

        // 验证映射结果
        assert vo.getId().equals(54321L);
        assert vo.getSurgicalNames() == null; // 空列表不会被映射

        System.out.println("测试通过：空数据的映射");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  手术名称: " + vo.getSurgicalNames() + " (应该为null)");
    }

    @Test
    public void testSurgicalInformationMappingWithNullResponse() {
        // 测试：null响应的映射
        
        // 创建VO对象
        SurgicalInformationVO vo = new SurgicalInformationVO();
        vo.setId(99999L);

        // 执行映射（传入null响应）
        mapSurgicalDataToVO(vo, null);

        // 验证映射结果
        assert vo.getId().equals(99999L);
        assert vo.getSurgicalNames() == null; // null响应不会影响现有数据

        System.out.println("测试通过：null响应的映射");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  手术名称: " + vo.getSurgicalNames() + " (应该为null)");
    }

    @Test
    public void testSurgicalInformationMappingWithNullData() {
        // 测试：null数据的映射
        
        // 创建响应但数据为null
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");
        response.setData(null); // 数据为null

        // 创建VO对象
        SurgicalInformationVO vo = new SurgicalInformationVO();
        vo.setId(77777L);

        // 执行映射
        mapSurgicalDataToVO(vo, response);

        // 验证映射结果
        assert vo.getId().equals(77777L);
        assert vo.getSurgicalNames() == null; // null数据不会被映射

        System.out.println("测试通过：null数据的映射");
        System.out.println("  案件ID: " + vo.getId());
        System.out.println("  手术名称: " + vo.getSurgicalNames() + " (应该为null)");
    }

    @Test
    public void testSurgicalInformationMappingWithoutId() {
        // 测试：无案件ID的映射（直接传入request对象的情况）
        
        // 创建模拟的AI识别结果
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("心脏搭桥手术"));
        response.setData(data);

        // 创建VO对象但不设置案件ID
        SurgicalInformationVO vo = new SurgicalInformationVO();

        // 执行映射
        mapSurgicalDataToVO(vo, response);

        // 验证映射结果
        assert vo.getId() == null; // 没有设置案件ID
        assert vo.getSurgicalNames() != null;
        assert vo.getSurgicalNames().size() == 1;
        assert vo.getSurgicalNames().contains("心脏搭桥手术");

        System.out.println("测试通过：无案件ID的映射");
        System.out.println("  案件ID: " + vo.getId() + " (应该为null)");
        System.out.println("  手术名称: " + vo.getSurgicalNames());
    }

    @Test
    public void testSurgicalInformationBusinessScenarios() {
        // 测试实际业务场景
        System.out.println("=== 业务场景测试 ===");
        
        // 场景1：单个复杂手术
        SurgicalInformationResponse response1 = new SurgicalInformationResponse();
        response1.setStatus("success");
        SurgicalInformationResponse.SurgicalData data1 = 
            new SurgicalInformationResponse.SurgicalData();
        data1.setSurgicalName(Arrays.asList("腹腔镜下胆囊切除术"));
        response1.setData(data1);

        SurgicalInformationVO vo1 = new SurgicalInformationVO();
        vo1.setId(2001L);
        mapSurgicalDataToVO(vo1, response1);

        assert vo1.getSurgicalNames().size() == 1;
        System.out.println("场景1通过：单个复杂手术 - " + vo1.getSurgicalNames().get(0));
        
        // 场景2：多个手术
        SurgicalInformationResponse response2 = new SurgicalInformationResponse();
        response2.setStatus("success");
        SurgicalInformationResponse.SurgicalData data2 = 
            new SurgicalInformationResponse.SurgicalData();
        data2.setSurgicalName(Arrays.asList(
            "阑尾切除术", 
            "疝气修补术", 
            "胆囊切除术"
        ));
        response2.setData(data2);

        SurgicalInformationVO vo2 = new SurgicalInformationVO();
        vo2.setId(2002L);
        mapSurgicalDataToVO(vo2, response2);

        assert vo2.getSurgicalNames().size() == 3;
        System.out.println("场景2通过：多个手术");
        vo2.getSurgicalNames().forEach(surgery -> 
            System.out.println("  - " + surgery)
        );
        
        // 场景3：识别失败的情况
        SurgicalInformationResponse response3 = new SurgicalInformationResponse();
        response3.setStatus("failed");
        response3.setMessage("手术信息识别失败");
        response3.setData(null);

        SurgicalInformationVO vo3 = new SurgicalInformationVO();
        vo3.setId(2003L);
        mapSurgicalDataToVO(vo3, response3);

        assert vo3.getId().equals(2003L);
        assert vo3.getSurgicalNames() == null;
        System.out.println("场景3通过：识别失败的情况 - 手术名称为null");
    }

    @Test
    public void testOptimizedInterfaceReturnType() {
        // 测试优化后的接口返回类型
        // 验证接口现在返回SurgicalInformationVO而不是SurgicalInformationResponse
        
        // 模拟完整的接口调用流程
        Long caseId = 123456L;
        
        // 模拟AI识别结果
        SurgicalInformationResponse aiResponse = new SurgicalInformationResponse();
        aiResponse.setStatus("success");
        aiResponse.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("骨折内固定术", "关节镜手术"));
        aiResponse.setData(data);

        // 创建返回的VO对象
        SurgicalInformationVO resultVO = new SurgicalInformationVO();
        resultVO.setId(caseId);
        mapSurgicalDataToVO(resultVO, aiResponse);

        // 验证结果
        assert resultVO.getId().equals(caseId);
        assert resultVO.getSurgicalNames() != null;
        assert resultVO.getSurgicalNames().size() == 2;
        assert resultVO.getSurgicalNames().contains("骨折内固定术");
        assert resultVO.getSurgicalNames().contains("关节镜手术");

        System.out.println("测试通过：优化后接口返回类型");
        System.out.println("返回的SurgicalInformationVO对象包含:");
        System.out.println("  案件ID: " + resultVO.getId());
        System.out.println("  手术名称: " + resultVO.getSurgicalNames());
        System.out.println("注意：此对象仅用于展示AI识别结果，未自动保存到数据库");
    }
}
