package com.yixun.wid.service;

import org.aspectj.lang.ProceedingJoinPoint;

public interface AsyncService {

    void recordLogAdmin(ProceedingJoinPoint invocation, String servletPath, Long userId);

    void recordLogLogin(String path, String username, Long userId, String mobileCode, String realIP);

	void recordLogLogin(String path, String username, Long userId, String mobileCode, String realIP, String funcName);

}
