package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.vo.IdsVO;
import com.yixun.wid.v2.vo.ThreeCatalogueWithPriceLimitsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 待遇 社保三目录相关接口
 */
@Slf4j
@RequestMapping("/v2/catalogue")
@RestController
public class ThreeCatalogueController {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 检查日期有效性
     * 确保开始日期不晚于结束日期
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 如果日期有效返回true，否则返回false
     */
    private boolean isDateValid(Date startDate, Date endDate) {
        // 只有当两个日期都不为空时才进行比较
        if (startDate != null && endDate != null) {
            // 如果开始日期晚于结束日期，返回false
            return !startDate.after(endDate);
        }
        // 如果有一个日期为空，则认为日期有效
        return true;
    }

    /**
     * 批量新增三目录
     *
     * @param list 三目录列表
     */
    @PostMapping("/batch/add")
    public CommonResult<Void> batchAddThreeCatalogueV2(@RequestBody List<ThreeCatalogue> list) {
        if (list == null || list.isEmpty()) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "列表不能为空");
        }

        // 检查每个三目录的开始日期和结束日期
        for (ThreeCatalogue threeCatalogue : list) {
            if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
                return CommonResult.failResult(CommonErrorInfo.code_1001, 
                    String.format("三目录 [%s] 的开始日期不能晚于结束日期", threeCatalogue.getProjectName()));
            }
        }

        // 当前时间
        Date now = new Date();

        // 为每个条目生成ID和设置时间字段
        for (ThreeCatalogue threeCatalogue : list) {
            threeCatalogue.setId(SnGeneratorUtil.getId());
            threeCatalogue.setCreateTime(now);
            threeCatalogue.setUpdateTime(now);
        }

        // 批量保存
        mongoTemplate.insertAll(list);
        log.info("批量新增三目录数量: {}", list.size());

        return CommonResult.successResult("操作成功");
    }

    /**
     * 批量删除三目录
     * 同时删除关联的限价目录
     *
     * @param idsVO id列表
     */
    @PostMapping("/batch/delete")
    public CommonResult<Void> batchDeleteThreeCatalogue(@Validated @RequestBody IdsVO idsVO) {
        List<Long> ids = idsVO.getIds();

        // 1. 先删除三目录
        Query threeCatalogueQuery = Query.query(Criteria.where("_id").in(ids));
        long deletedCatalogues = mongoTemplate.remove(threeCatalogueQuery, ThreeCatalogue.class).getDeletedCount();
        log.info("删除三目录数量: {}", deletedCatalogues);

        // 2. 再删除关联的限价目录
        Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").in(ids));
        long deletedPriceLimits = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
        log.info("删除关联的限价目录数量: {}", deletedPriceLimits);

        return CommonResult.successResult("操作成功");
    }

    /**
     * 更新三目录
     *
     * @param threeCatalogue 三目录信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public CommonResult<Void> updateThreeCatalogue(@Validated @RequestBody ThreeCatalogue threeCatalogue) {
        if (threeCatalogue.getId() == null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
        }
        ThreeCatalogue existingCatalogue = mongoTemplate.findById(threeCatalogue.getId(), ThreeCatalogue.class);
        if (existingCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
        }
        
        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }
        
        // 保留创建时间
        Date createTime = existingCatalogue.getCreateTime();
        BeanUtil.copyProperties(threeCatalogue, existingCatalogue);
        existingCatalogue.setCreateTime(createTime);
        existingCatalogue.setUpdateTime(new Date());
        
        mongoTemplate.save(existingCatalogue);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 保存三目录
     *
     * @param threeCatalogue 三目录信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public CommonResult<Void> saveThreeCatalogue(@Validated @RequestBody ThreeCatalogue threeCatalogue) {
        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }
        
        threeCatalogue.setId(SnGeneratorUtil.getId());
        Date now = new Date();
        threeCatalogue.setCreateTime(now);
        threeCatalogue.setUpdateTime(now);
        mongoTemplate.save(threeCatalogue);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询三目录列表
     *
     * @param sn          三目录编号（模糊搜索）
     * @param type        三目录类型
     * @param projectName 项目名称（模糊搜索）
     * @param level       费用等级
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param commonPage  分页信息
     * @return 三目录列表
     */
    @GetMapping("/list")
    public CommonResult<List<ThreeCatalogue>> list(
            @RequestParam(required = false) String sn,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            CommonPage commonPage) {

        Query query = new Query();

        // 三目录编号模糊搜索
        if (StrUtil.isNotBlank(sn)) {
            query.addCriteria(Criteria.where("sn").regex(".*" + sn + ".*", "i"));
        }

        // 三目录类型精确匹配
        if (StrUtil.isNotBlank(type)) {
            query.addCriteria(Criteria.where("type").is(type));
        }

        // 项目名称模糊搜索
        if (StrUtil.isNotBlank(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 费用等级精确匹配
        if (StrUtil.isNotBlank(level)) {
            query.addCriteria(Criteria.where("level").is(level));
        }

        // 开始日期过滤
        if (startDate != null) {
            query.addCriteria(Criteria.where("startDate").gte(startDate));
        }

        // 结束日期过滤
        if (endDate != null) {
            query.addCriteria(Criteria.where("endDate").lte(endDate));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 设置分页信息
        com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, ThreeCatalogue.class, query, commonPage);

        // 执行查询
        List<ThreeCatalogue> list = mongoTemplate.find(query, ThreeCatalogue.class);

        return CommonResult.successPageData(list, commonPage);
    }

    /**
     * 新增三目录记录并同时处理关联的限价目录
     * 如果包含限价目录数据，会先清空该三目录关联的所有限价目录，然后批量新增
     *
     * @param vo 包含三目录信息和限价目录列表的VO对象
     * @return 操作结果
     */
    @PostMapping("/addWithPriceLimits")
    public CommonResult<ThreeCatalogue> addThreeCatalogueWithPriceLimits(@Validated @RequestBody ThreeCatalogueWithPriceLimitsVO vo) {
        ThreeCatalogue threeCatalogue = vo.getThreeCatalogue();
        List<PriceLimit> priceLimits = vo.getPriceLimits();

        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }

        // 生成ID
        threeCatalogue.setId(SnGeneratorUtil.getId());
        
        // 设置创建时间和更新时间
        Date now = new Date();
        threeCatalogue.setCreateTime(now);
        threeCatalogue.setUpdateTime(now);

        // 保存三目录到数据库
        mongoTemplate.save(threeCatalogue);
        log.info("新增三目录: {}", threeCatalogue.getProjectName());

        // 处理关联的限价目录
        if (priceLimits != null && !priceLimits.isEmpty()) {
            // 删除可能存在的关联限价目录（虽然是新建，但以防万一）
            Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(threeCatalogue.getId()));
            long deletedCount = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
            if (deletedCount > 0) {
                log.info("清除旧的限价目录数量: {}", deletedCount);
            }

            // 处理新的限价目录数据
            List<PriceLimit> newPriceLimits = new ArrayList<>();

            for (PriceLimit priceLimit : priceLimits) {
                // 设置ID
                priceLimit.setId(SnGeneratorUtil.getId());
                // 关联到三目录
                priceLimit.setThreeCatalogueId(threeCatalogue.getId());
                // 设置项目名称
                priceLimit.setProjectName(threeCatalogue.getProjectName());
                // 设置时间
                priceLimit.setCreateTime(now);
                priceLimit.setUpdateTime(now);

                newPriceLimits.add(priceLimit);
            }

            // 批量保存限价目录
            if (!newPriceLimits.isEmpty()) {
                mongoTemplate.insertAll(newPriceLimits);
                log.info("批量新增限价目录数量: {}", newPriceLimits.size());
            }
        }

        return CommonResult.successData(threeCatalogue);
    }

    /**
     * 更新三目录并同时处理关联的限价目录
     * 如果包含限价目录数据，会先清空该三目录关联的所有限价目录，然后批量新增
     *
     * @param vo 包含三目录信息和限价目录列表的VO对象
     * @return 操作结果
     */
    @PostMapping("/updateWithPriceLimits")
    public CommonResult<ThreeCatalogue> updateThreeCatalogueWithPriceLimits(@Validated @RequestBody ThreeCatalogueWithPriceLimitsVO vo) {
        ThreeCatalogue threeCatalogue = vo.getThreeCatalogue();
        List<PriceLimit> priceLimits = vo.getPriceLimits();

        // 验证三目录是否存在
        if (threeCatalogue.getId() == null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "三目录ID不能为空");
        }

        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }

        ThreeCatalogue existingCatalogue = mongoTemplate.findById(threeCatalogue.getId(), ThreeCatalogue.class);
        if (existingCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "三目录数据不存在");
        }

        // 保留创建时间
        Date createTime = existingCatalogue.getCreateTime();
        Date now = new Date();
        
        // 更新三目录信息
        BeanUtil.copyProperties(threeCatalogue, existingCatalogue);
        existingCatalogue.setCreateTime(createTime);
        existingCatalogue.setUpdateTime(now);
        
        mongoTemplate.save(existingCatalogue);
        log.info("更新三目录: {}", existingCatalogue.getProjectName());

        // 处理关联的限价目录
        if (priceLimits != null) {
            // 先删除已有的关联限价目录
            Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(threeCatalogue.getId()));
            long deletedCount = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
            log.info("删除关联的限价目录数量: {}", deletedCount);

            // 如果有新的限价目录数据，则批量新增
            if (!priceLimits.isEmpty()) {
                List<PriceLimit> newPriceLimits = new ArrayList<>();

                for (PriceLimit priceLimit : priceLimits) {
                    // 设置ID
                    priceLimit.setId(SnGeneratorUtil.getId());
                    // 关联到三目录
                    priceLimit.setThreeCatalogueId(threeCatalogue.getId());
                    // 设置项目名称
                    priceLimit.setProjectName(existingCatalogue.getProjectName());
                    // 设置时间
                    priceLimit.setCreateTime(now);
                    priceLimit.setUpdateTime(now);

                    newPriceLimits.add(priceLimit);
                }

                // 批量保存限价目录
                mongoTemplate.insertAll(newPriceLimits);
                log.info("批量新增限价目录数量: {}", newPriceLimits.size());
            }
        }

        return CommonResult.successData(existingCatalogue);
    }

    /**
     * 获取三目录及其关联的限价目录
     *
     * @param id 三目录ID
     * @return 三目录及其关联的限价目录
     */
    @GetMapping("/getWithPriceLimits")
    public CommonResult<ThreeCatalogueWithPriceLimitsVO> getThreeCatalogueWithPriceLimits(@RequestParam Long id) {
        // 查询三目录
        ThreeCatalogue threeCatalogue = mongoTemplate.findById(id, ThreeCatalogue.class);
        if (threeCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "三目录数据不存在");
        }

        // 查询关联的限价目录
        Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(id));
        List<PriceLimit> priceLimits = mongoTemplate.find(priceLimitQuery, PriceLimit.class);

        // 封装返回数据
        ThreeCatalogueWithPriceLimitsVO result = new ThreeCatalogueWithPriceLimitsVO();
        result.setThreeCatalogue(threeCatalogue);
        result.setPriceLimits(priceLimits);

        return CommonResult.successData(result);
    }

}
