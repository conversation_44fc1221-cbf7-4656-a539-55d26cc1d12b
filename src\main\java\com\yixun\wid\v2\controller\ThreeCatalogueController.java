package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.EasyExcel;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.ThreeCatalogueSimilarSearchIn;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.listener.ThreeCatalogueImportListener;
import com.yixun.wid.v2.service.ThreeCatalogueService;
import com.yixun.wid.v2.vo.IdsVO;
import com.yixun.wid.v2.vo.ThreeCatalogueImportVO;
import com.yixun.wid.v2.vo.ThreeCatalogueWithPriceLimitsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 待遇 社保三目录相关接口
 */
@Slf4j
@RequestMapping("/v2/catalogue")
@RestController
public class ThreeCatalogueController {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ThreeCatalogueService threeCatalogueService;

    /**
     * 检查日期有效性
     * 确保开始日期不晚于结束日期
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 如果日期有效返回true，否则返回false
     */
    private boolean isDateValid(Date startDate, Date endDate) {
        // 只有当两个日期都不为空时才进行比较
        if (startDate != null && endDate != null) {
            // 如果开始日期晚于结束日期，返回false
            return !startDate.after(endDate);
        }
        // 如果有一个日期为空，则认为日期有效
        return true;
    }

    /**
     * 检查三目录唯一性
     * 根据目录编码、项目名称、费用等级、目录类别、开始日期、结束日期这6个字段判断唯一性
     *
     * @param catalogue 要检查的三目录
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 如果存在重复记录返回重复记录，否则返回null
     */
    private ThreeCatalogue checkThreeCatalogueUniqueness(ThreeCatalogue catalogue, Long excludeId) {
        Query query = new Query();

        // 构建查询条件 - 6个字段均相同才认为是同一条记录
        // 1. 目录编码 - 需要考虑null值的情况
        if (StrUtil.isNotBlank(catalogue.getSn())) {
            query.addCriteria(Criteria.where("sn").is(catalogue.getSn()));
        } else {
            query.addCriteria(Criteria.where("sn").is(null));
        }

        // 2. 项目名称 - 必填字段
        query.addCriteria(Criteria.where("projectName").is(catalogue.getProjectName()));

        // 3. 费用等级 - 必填字段
        query.addCriteria(Criteria.where("level").is(catalogue.getLevel()));

        // 4. 目录类别 - 必填字段
        query.addCriteria(Criteria.where("type").is(catalogue.getType()));

        // 5. 开始日期 - 需要考虑null值的情况
        if (catalogue.getStartDate() != null) {
            query.addCriteria(Criteria.where("startDate").is(catalogue.getStartDate()));
        } else {
            query.addCriteria(Criteria.where("startDate").is(null));
        }

        // 6. 结束日期 - 需要考虑null值的情况
        if (catalogue.getEndDate() != null) {
            query.addCriteria(Criteria.where("endDate").is(catalogue.getEndDate()));
        } else {
            query.addCriteria(Criteria.where("endDate").is(null));
        }

        // 排除指定ID（用于更新时排除自身）
        if (excludeId != null) {
            query.addCriteria(Criteria.where("_id").ne(excludeId));
        }

        return mongoTemplate.findOne(query, ThreeCatalogue.class);
    }

    /**
     * 批量新增三目录
     *
     * @param list 三目录列表
     */
    @PostMapping("/batch/add")
    public CommonResult<Void> batchAddThreeCatalogueV2(@RequestBody List<ThreeCatalogue> list) {
        if (list == null || list.isEmpty()) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "列表不能为空");
        }

        // 检查每个三目录的开始日期和结束日期，以及唯一性
        for (ThreeCatalogue threeCatalogue : list) {
            if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
                return CommonResult.failResult(CommonErrorInfo.code_1001,
                    String.format("三目录 [%s] 的开始日期不能晚于结束日期", threeCatalogue.getProjectName()));
            }

            // 检查唯一性
            ThreeCatalogue duplicate = checkThreeCatalogueUniqueness(threeCatalogue, null);
            if (duplicate != null) {
                return CommonResult.failResult(CommonErrorInfo.code_1001,
                    String.format("三目录 [%s] 已存在相同的记录（目录编码：%s，费用等级：%s，目录类别：%s）",
                        threeCatalogue.getProjectName(),
                        StrUtil.isBlank(threeCatalogue.getSn()) ? "无" : threeCatalogue.getSn(),
                        threeCatalogue.getLevel(),
                        threeCatalogue.getType()));
            }
        }

        // 当前时间
        Date now = new Date();

        // 为每个条目生成ID和设置时间字段
        for (ThreeCatalogue threeCatalogue : list) {
            threeCatalogue.setId(SnGeneratorUtil.getId());
            threeCatalogue.setCreateTime(now);
            threeCatalogue.setUpdateTime(now);
        }

        // 批量保存
        mongoTemplate.insertAll(list);
        log.info("批量新增三目录数量: {}", list.size());

        return CommonResult.successResult("操作成功");
    }

    /**
     * 批量删除三目录
     * 同时删除关联的限价目录
     *
     * @param idsVO id列表
     */
    @PostMapping("/batch/delete")
    public CommonResult<Void> batchDeleteThreeCatalogue(@Validated @RequestBody IdsVO idsVO) {
        List<Long> ids = idsVO.getIds();

        // 1. 先删除三目录
        Query threeCatalogueQuery = Query.query(Criteria.where("_id").in(ids));
        long deletedCatalogues = mongoTemplate.remove(threeCatalogueQuery, ThreeCatalogue.class).getDeletedCount();
        log.info("删除三目录数量: {}", deletedCatalogues);

        // 2. 再删除关联的限价目录
        Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").in(ids));
        long deletedPriceLimits = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
        log.info("删除关联的限价目录数量: {}", deletedPriceLimits);

        return CommonResult.successResult("操作成功");
    }

    /**
     * 更新三目录
     *
     * @param threeCatalogue 三目录信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public CommonResult<Void> updateThreeCatalogue(@Validated @RequestBody ThreeCatalogue threeCatalogue) {
        if (threeCatalogue.getId() == null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
        }
        ThreeCatalogue existingCatalogue = mongoTemplate.findById(threeCatalogue.getId(), ThreeCatalogue.class);
        if (existingCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
        }
        
        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }

        // 检查唯一性（排除当前记录）
        ThreeCatalogue duplicate = checkThreeCatalogueUniqueness(threeCatalogue, threeCatalogue.getId());
        if (duplicate != null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001,
                String.format("三目录 [%s] 已存在相同的记录（目录编码：%s，费用等级：%s，目录类别：%s）",
                    threeCatalogue.getProjectName(),
                    StrUtil.isBlank(threeCatalogue.getSn()) ? "无" : threeCatalogue.getSn(),
                    threeCatalogue.getLevel(),
                    threeCatalogue.getType()));
        }

        // 保留创建时间
        Date createTime = existingCatalogue.getCreateTime();
        BeanUtil.copyProperties(threeCatalogue, existingCatalogue);
        existingCatalogue.setCreateTime(createTime);
        existingCatalogue.setUpdateTime(new Date());

        mongoTemplate.save(existingCatalogue);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 保存三目录
     *
     * @param threeCatalogue 三目录信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public CommonResult<Void> saveThreeCatalogue(@Validated @RequestBody ThreeCatalogue threeCatalogue) {
        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }
        
        threeCatalogue.setId(SnGeneratorUtil.getId());
        Date now = new Date();
        threeCatalogue.setCreateTime(now);
        threeCatalogue.setUpdateTime(now);
        mongoTemplate.save(threeCatalogue);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询三目录列表
     *
     * @param sn          三目录编号（模糊搜索）
     * @param type        三目录类型
     * @param projectName 项目名称（模糊搜索）
     * @param level       费用等级
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param effective   是否有效（true=有效，false=无效，null=全部）
     * @param commonPage  分页信息
     * @return 三目录列表
     */
    @GetMapping("/list")
    public CommonResult<List<ThreeCatalogue>> list(
            @RequestParam(required = false) String sn,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) Boolean effective,
            CommonPage commonPage) {

        Query query = new Query();

        // 三目录编号模糊搜索
        if (StrUtil.isNotBlank(sn)) {
            query.addCriteria(Criteria.where("sn").regex(".*" + sn + ".*", "i"));
        }

        // 三目录类型精确匹配
        if (StrUtil.isNotBlank(type)) {
            query.addCriteria(Criteria.where("type").is(type));
        }

        // 项目名称模糊搜索
        if (StrUtil.isNotBlank(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 费用等级精确匹配
        if (StrUtil.isNotBlank(level)) {
            query.addCriteria(Criteria.where("level").is(level));
        }

        // 开始日期过滤
        if (startDate != null) {
            query.addCriteria(Criteria.where("startDate").gte(startDate));
        }

        // 结束日期过滤
        if (endDate != null) {
            query.addCriteria(Criteria.where("endDate").lte(endDate));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 设置分页信息
        com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, ThreeCatalogue.class, query, commonPage);

        // 执行查询
        List<ThreeCatalogue> list = mongoTemplate.find(query, ThreeCatalogue.class);

        // 如果指定了有效性筛选条件，则进行过滤
        if (effective != null) {
            list = list.stream()
                    .filter(catalogue -> effective.equals(catalogue.getEffective()))
                    .collect(Collectors.toList());

            // 重新设置分页信息（因为过滤后数量可能变化）
            commonPage.setTotal((long) list.size());
        }

        return CommonResult.successPageData(list, commonPage);
    }

    /**
     * 新增三目录记录并同时处理关联的限价目录
     * 如果包含限价目录数据，会先清空该三目录关联的所有限价目录，然后批量新增
     *
     * @param vo 包含三目录信息和限价目录列表的VO对象
     * @return 操作结果
     */
    @PostMapping("/addWithPriceLimits")
    public CommonResult<ThreeCatalogue> addThreeCatalogueWithPriceLimits(@Validated @RequestBody ThreeCatalogueWithPriceLimitsVO vo) {
        ThreeCatalogue threeCatalogue = vo.getThreeCatalogue();
        List<PriceLimit> priceLimits = vo.getPriceLimits();

        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }

        // 检查唯一性
        ThreeCatalogue duplicate = checkThreeCatalogueUniqueness(threeCatalogue, null);
        if (duplicate != null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001,
                String.format("三目录 [%s] 已存在相同的记录（目录编码：%s，费用等级：%s，目录类别：%s）",
                    threeCatalogue.getProjectName(),
                    StrUtil.isBlank(threeCatalogue.getSn()) ? "无" : threeCatalogue.getSn(),
                    threeCatalogue.getLevel(),
                    threeCatalogue.getType()));
        }

        // 生成ID
        threeCatalogue.setId(SnGeneratorUtil.getId());
        
        // 设置创建时间和更新时间
        Date now = new Date();
        threeCatalogue.setCreateTime(now);
        threeCatalogue.setUpdateTime(now);

        // 保存三目录到数据库
        mongoTemplate.save(threeCatalogue);
        log.info("新增三目录: {}", threeCatalogue.getProjectName());

        // 处理关联的限价目录
        if (priceLimits != null && !priceLimits.isEmpty()) {
            // 删除可能存在的关联限价目录（虽然是新建，但以防万一）
            Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(threeCatalogue.getId()));
            long deletedCount = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
            if (deletedCount > 0) {
                log.info("清除旧的限价目录数量: {}", deletedCount);
            }

            // 处理新的限价目录数据
            List<PriceLimit> newPriceLimits = new ArrayList<>();

            for (PriceLimit priceLimit : priceLimits) {
                // 设置ID
                priceLimit.setId(SnGeneratorUtil.getId());
                // 关联到三目录
                priceLimit.setThreeCatalogueId(threeCatalogue.getId());
                // 设置项目名称
                priceLimit.setProjectName(threeCatalogue.getProjectName());
                // 设置时间
                priceLimit.setCreateTime(now);
                priceLimit.setUpdateTime(now);

                newPriceLimits.add(priceLimit);
            }

            // 批量保存限价目录
            if (!newPriceLimits.isEmpty()) {
                mongoTemplate.insertAll(newPriceLimits);
                log.info("批量新增限价目录数量: {}", newPriceLimits.size());
            }
        }

        return CommonResult.successData(threeCatalogue);
    }

    /**
     * 更新三目录并同时处理关联的限价目录
     * 如果包含限价目录数据，会先清空该三目录关联的所有限价目录，然后批量新增
     *
     * @param vo 包含三目录信息和限价目录列表的VO对象
     * @return 操作结果
     */
    @PostMapping("/updateWithPriceLimits")
    public CommonResult<ThreeCatalogue> updateThreeCatalogueWithPriceLimits(@Validated @RequestBody ThreeCatalogueWithPriceLimitsVO vo) {
        ThreeCatalogue threeCatalogue = vo.getThreeCatalogue();
        List<PriceLimit> priceLimits = vo.getPriceLimits();

        // 验证三目录是否存在
        if (threeCatalogue.getId() == null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "三目录ID不能为空");
        }

        // 检查开始日期是否在结束日期之前
        if (!isDateValid(threeCatalogue.getStartDate(), threeCatalogue.getEndDate())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "开始日期不能晚于结束日期");
        }

        ThreeCatalogue existingCatalogue = mongoTemplate.findById(threeCatalogue.getId(), ThreeCatalogue.class);
        if (existingCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "三目录数据不存在");
        }

        // 检查唯一性（排除当前记录）
        ThreeCatalogue duplicate = checkThreeCatalogueUniqueness(threeCatalogue, threeCatalogue.getId());
        if (duplicate != null) {
            return CommonResult.failResult(CommonErrorInfo.code_1001,
                String.format("三目录 [%s] 已存在相同的记录（目录编码：%s，费用等级：%s，目录类别：%s）",
                    threeCatalogue.getProjectName(),
                    StrUtil.isBlank(threeCatalogue.getSn()) ? "无" : threeCatalogue.getSn(),
                    threeCatalogue.getLevel(),
                    threeCatalogue.getType()));
        }

        // 保留创建时间
        Date createTime = existingCatalogue.getCreateTime();
        Date now = new Date();
        
        // 更新三目录信息
        BeanUtil.copyProperties(threeCatalogue, existingCatalogue);
        existingCatalogue.setCreateTime(createTime);
        existingCatalogue.setUpdateTime(now);
        
        mongoTemplate.save(existingCatalogue);
        log.info("更新三目录: {}", existingCatalogue.getProjectName());

        // 处理关联的限价目录
        if (priceLimits != null) {
            // 先删除已有的关联限价目录
            Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(threeCatalogue.getId()));
            long deletedCount = mongoTemplate.remove(priceLimitQuery, PriceLimit.class).getDeletedCount();
            log.info("删除关联的限价目录数量: {}", deletedCount);

            // 如果有新的限价目录数据，则批量新增
            if (!priceLimits.isEmpty()) {
                List<PriceLimit> newPriceLimits = new ArrayList<>();

                for (PriceLimit priceLimit : priceLimits) {
                    // 设置ID
                    priceLimit.setId(SnGeneratorUtil.getId());
                    // 关联到三目录
                    priceLimit.setThreeCatalogueId(threeCatalogue.getId());
                    // 设置项目名称
                    priceLimit.setProjectName(existingCatalogue.getProjectName());
                    // 设置时间
                    priceLimit.setCreateTime(now);
                    priceLimit.setUpdateTime(now);

                    newPriceLimits.add(priceLimit);
                }

                // 批量保存限价目录
                mongoTemplate.insertAll(newPriceLimits);
                log.info("批量新增限价目录数量: {}", newPriceLimits.size());
            }
        }

        return CommonResult.successData(existingCatalogue);
    }

    /**
     * 获取三目录及其关联的限价目录
     *
     * @param id 三目录ID
     * @return 三目录及其关联的限价目录
     */
    @GetMapping("/getWithPriceLimits")
    public CommonResult<ThreeCatalogueWithPriceLimitsVO> getThreeCatalogueWithPriceLimits(@RequestParam Long id) {
        // 查询三目录
        ThreeCatalogue threeCatalogue = mongoTemplate.findById(id, ThreeCatalogue.class);
        if (threeCatalogue == null) {
            return CommonResult.failResult(CommonErrorInfo.code_6001, "三目录数据不存在");
        }

        // 查询关联的限价目录
        Query priceLimitQuery = Query.query(Criteria.where("threeCatalogueId").is(id));
        List<PriceLimit> priceLimits = mongoTemplate.find(priceLimitQuery, PriceLimit.class);

        // 为每个PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
        Boolean threeCatalogueEffective = threeCatalogue.getEffective();
        for (PriceLimit priceLimit : priceLimits) {
            priceLimit.setEffective(threeCatalogueEffective);
        }

        // 封装返回数据
        ThreeCatalogueWithPriceLimitsVO result = new ThreeCatalogueWithPriceLimitsVO();
        result.setThreeCatalogue(threeCatalogue);
        result.setPriceLimits(priceLimits);

        return CommonResult.successData(result);
    }

    /**
     * 三目录相似搜索接口
     * 基于AiUtils中的相似查找功能，实现三目录的智能匹配
     *
     * @param searchIn 相似搜索参数
     * @return 相似度最高的一条三目录数据的完整详情
     */
    @PostMapping("/similarSearch")
    public CommonResult<ThreeCatalogue> similarSearch(@Validated @RequestBody ThreeCatalogueSimilarSearchIn searchIn) {
        ThreeCatalogue result = threeCatalogueService.similarSearch(searchIn);

        if (result == null) {
            return CommonResult.failResult(10001, "未找到匹配的三目录数据");
        }

        return CommonResult.successData(result);
    }

    /**
     * 验证三目录是否重复
     * 根据目录编码、项目名称、费用等级、目录类别、开始日期、结束日期这6个字段判断是否存在重复记录
     *
     * @param catalogue 要验证的三目录信息
     * @return 验证结果
     */
    @PostMapping("/checkDuplicate")
    public CommonResult<DuplicateCheckResult> checkThreeCatalogueDuplicate(@RequestBody ThreeCatalogue catalogue) {

        // 验证必填字段
        if (StrUtil.isBlank(catalogue.getProjectName())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "项目名称不能为空");
        }
        if (StrUtil.isBlank(catalogue.getLevel())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "费用等级不能为空");
        }
        if (StrUtil.isBlank(catalogue.getType())) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "目录类别不能为空");
        }

        // 检查唯一性
        ThreeCatalogue duplicate = checkThreeCatalogueUniqueness(catalogue, catalogue.getId());

        DuplicateCheckResult result = new DuplicateCheckResult();
        if (duplicate != null) {
            result.setDuplicate(true);
            result.setDuplicateRecord(duplicate);
            result.setMessage(String.format("三目录 [%s] 已存在相同的记录（目录编码：%s，费用等级：%s，目录类别：%s）",
                catalogue.getProjectName(),
                StrUtil.isBlank(catalogue.getSn()) ? "无" : catalogue.getSn(),
                catalogue.getLevel(),
                catalogue.getType()));
        } else {
            result.setDuplicate(false);
            result.setMessage("该三目录记录不重复，可以保存");
        }

        return CommonResult.successData(result);
    }

    /**
     * 批量导入三目录
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/batchImport")
    public CommonResult<BatchImportResult> batchImport(@RequestParam("file") MultipartFile file) {

        BatchImportResult result = new BatchImportResult();

        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "请上传文件");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return CommonResult.failResult(CommonErrorInfo.code_1001, "请上传Excel文件(.xlsx或.xls格式)");
        }

        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));

        try {
            // 保存上传文件到临时文件
            File tempFile = FileUtil.createTempFile(extension, true);
            file.transferTo(tempFile);

            // 创建EasyExcel导入监听器
            ThreeCatalogueImportListener listener = new ThreeCatalogueImportListener(mongoTemplate);

            // 设置原始文件
            listener.setOriginalFile(tempFile);

            log.info("开始导入三目录数据");

            // 读取Excel文件
            EasyExcel.read()
                    .file(tempFile)
                    .head(ThreeCatalogueImportVO.class)
                    .sheet()
                    .headRowNumber(3) // 从第3行开始读取（标题行）
                    .registerReadListener(listener)
                    .doRead();

            // 处理导入结果
            result.setTotalCount(listener.getSuccessCount() + listener.getFailItems().size());
            result.setSuccessCount(listener.getSuccessCount());
            result.setFailCount(listener.getFailItems().size());
            result.setFailItems(listener.getFailItems());

            // 设置失败行号列表
            if (CollUtil.isEmpty(listener.getFailRowNums())) {
                List<Integer> rowNums = listener.getFailItems().stream()
                        .map(BatchImportResult.FailItem::getRowNum)
                        .collect(Collectors.toList());
                result.setFailRowNums(rowNums);
            } else {
                result.setFailRowNums(listener.getFailRowNums());
            }

            // 如果有导入错误，生成结果文件
            if (!listener.getFailItems().isEmpty()) {
                try {
                    File resultFile = listener.generateResultFile();
                    if (resultFile != null && resultFile.exists()) {
                        String downloadUrl = resultFile.getName();
                        result.setErrorResultUrl(downloadUrl);
                    }
                } catch (Exception e) {
                    log.error("生成结果文件失败", e);
                }
            }

            log.info("三目录导入完成，总数：{}，成功：{}，失败：{}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailCount());

            return CommonResult.successData(result);

        } catch (IOException e) {
            log.error("处理上传文件失败", e);
            return CommonResult.failResult(CommonErrorInfo.code_3001, "处理文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入结果文件，包含错误信息
     */
    @GetMapping("/download-import-result")
    public void downloadImportResult(@RequestParam("fileName") String fileName, HttpServletResponse response) throws IOException {
        // 从临时目录获取文件
        File file = new File(System.getProperty("java.io.tmpdir"), fileName);

        if (!file.exists()) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("文件不存在或已过期");
            return;
        }

        String displayFileName = "三目录导入结果.xlsx";

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(displayFileName, StandardCharsets.UTF_8.name()));

        try {
            IoUtil.copy(FileUtil.getInputStream(file), response.getOutputStream());
        } finally {
            // 下载完成后删除临时文件
            // file.deleteOnExit();
        }
    }

    /**
     * 批量导入结果
     */
    @lombok.Data
    public static class BatchImportResult {
        /**
         * 总数量
         */
        private int totalCount;

        /**
         * 成功数量
         */
        private int successCount;

        /**
         * 失败数量
         */
        private int failCount;

        /**
         * 失败项目列表
         */
        private List<FailItem> failItems;

        /**
         * 失败行号列表
         */
        private List<Integer> failRowNums;

        /**
         * 错误结果文件下载URL
         */
        private String errorResultUrl;

        /**
         * 失败项目
         */
        @lombok.Data
        public static class FailItem {
            /**
             * 行号
             */
            private Integer rowNum;

            /**
             * 失败原因
             */
            private String reason;
        }
    }

    /**
     * 重复检查结果
     */
    @lombok.Data
    public static class DuplicateCheckResult {
        /**
         * 是否重复
         */
        private boolean duplicate;

        /**
         * 重复的记录（如果存在）
         */
        private ThreeCatalogue duplicateRecord;

        /**
         * 提示信息
         */
        private String message;
    }

}
