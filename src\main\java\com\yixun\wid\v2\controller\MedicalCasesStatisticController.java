package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.entity.MedicalCasesStatus;
import com.yixun.wid.v2.vo.AmountStatisticData;
import com.yixun.wid.v2.vo.CountPoint;
import com.yixun.wid.v2.vo.DoneCountByMonthResp;
import com.yixun.wid.v2.vo.MedicalCasesStatusStatisticReq;
import com.yixun.wid.v2.vo.MedicalCasesStatusStatisticResp;
import com.yixun.wid.v2.vo.BizInfoStatisticReq;
import com.yixun.wid.v2.vo.BizInfoStatisticResp;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 待遇 工伤待遇业务统计看板接口
 */
@Slf4j
@RequestMapping("/v2/medical/cases/statistic")
@RestController
public class MedicalCasesStatisticController {

	@Resource
	private MongoTemplate mongoTemplate;

	/**
	 * 待办业务 （业务办理统计）
	 */
	@GetMapping("/pending-biz")
	public CommonResult<MedicalCasesStatusStatisticResp> getPendingBiz(MedicalCasesStatusStatisticReq medicalCasesStatusStatisticReq) {
		Criteria criteria = Criteria.where("status").ne(null);
		if (medicalCasesStatusStatisticReq.getStartTime() != null && medicalCasesStatusStatisticReq.getEndTime() != null) {
			criteria = criteria.and("createTime").gte(medicalCasesStatusStatisticReq.getStartTime())
					.lt(medicalCasesStatusStatisticReq.getEndTime());
		} else if (medicalCasesStatusStatisticReq.getStartTime() != null) {
			criteria = criteria.and("createTime").gte(medicalCasesStatusStatisticReq.getStartTime());
		} else if (medicalCasesStatusStatisticReq.getEndTime() != null) {
			criteria = criteria.and("createTime").lt(medicalCasesStatusStatisticReq.getEndTime());
		}
		
		Aggregation aggregation = Aggregation.newAggregation(
			Aggregation.match(criteria),
			Aggregation.group("status").count().as("count")
		);
		AggregationResults<Document> results = mongoTemplate.aggregate(
			aggregation,
			"medicalCases",
			Document.class
		);
		MedicalCasesStatusStatisticResp medicalCasesStatusStatisticResp = new MedicalCasesStatusStatisticResp();
		for (Document doc : results) {
			String status = doc.getString("_id");
			if (status == null) {
				continue;
			}
			Long count = Long.valueOf(doc.getInteger("count"));
			try {
			MedicalCasesStatus medicalCasesStatus = MedicalCasesStatus.valueOf(status);
			switch (medicalCasesStatus) {
				case Applying:
					medicalCasesStatusStatisticResp.setApplying(count);
						break;
				case PreReviewing:
					medicalCasesStatusStatisticResp.setPreReviewing(count);
						break;
				case Reviewing:
					medicalCasesStatusStatisticResp.setReviewing(count);
						break;
				case FinalReviewing:
					medicalCasesStatusStatisticResp.setFinalReviewing(count);
						break;
				case Done:
					medicalCasesStatusStatisticResp.setDone(count);
						break;
				}
			} catch (IllegalArgumentException e) {
				log.warn("未知的案件状态: {}", status);
			}
		}
		return CommonResult.successData(medicalCasesStatusStatisticResp);
	}

	/**
	 * 业务情况统计 （业务办理统计、业务办结统计、金额统计）
	 * 
	 * @param bizInfoStatisticReq 业务情况统计请求参数
	 * @return 业务情况统计结果
	 */
	@GetMapping("/biz-info")
	public CommonResult<BizInfoStatisticResp> getBizInfo(BizInfoStatisticReq bizInfoStatisticReq) {
		// 初始化返回结果
		BizInfoStatisticResp resp = new BizInfoStatisticResp();
		
		// 处理查询参数
		Integer year = bizInfoStatisticReq.getYear();
		Integer month = bizInfoStatisticReq.getMonth();
		
		if (year == null) {
			// 如果未提供年份，使用当前年份
			Calendar cal = Calendar.getInstance();
			year = cal.get(Calendar.YEAR);
		}
		
		// 构建时间范围条件
		Date startDate, endDate;
		Calendar startCal = Calendar.getInstance();
		Calendar endCal = Calendar.getInstance();
		
		if (month != null && month >= 1 && month <= 12) {
			// 按年月查询
			startCal.clear();
			startCal.set(Calendar.YEAR, year);
			startCal.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
			startCal.set(Calendar.DAY_OF_MONTH, 1);
			startCal.set(Calendar.HOUR_OF_DAY, 0);
			startCal.set(Calendar.MINUTE, 0);
			startCal.set(Calendar.SECOND, 0);
			startCal.set(Calendar.MILLISECOND, 0);
			startDate = startCal.getTime();
			
			endCal.clear();
			endCal.set(Calendar.YEAR, year);
			endCal.set(Calendar.MONTH, month - 1);
			endCal.set(Calendar.DAY_OF_MONTH, 1);
			endCal.add(Calendar.MONTH, 1);
			endCal.set(Calendar.HOUR_OF_DAY, 0);
			endCal.set(Calendar.MINUTE, 0);
			endCal.set(Calendar.SECOND, 0);
			endCal.set(Calendar.MILLISECOND, 0);
			endDate = endCal.getTime();
		} else {
			// 按整年查询
		startCal.clear();
		startCal.set(Calendar.YEAR, year);
		startCal.set(Calendar.MONTH, Calendar.JANUARY);
		startCal.set(Calendar.DAY_OF_MONTH, 1);
		startCal.set(Calendar.HOUR_OF_DAY, 0);
		startCal.set(Calendar.MINUTE, 0);
		startCal.set(Calendar.SECOND, 0);
		startCal.set(Calendar.MILLISECOND, 0);
			startDate = startCal.getTime();

		endCal.clear();
		endCal.set(Calendar.YEAR, year + 1);
		endCal.set(Calendar.MONTH, Calendar.JANUARY);
		endCal.set(Calendar.DAY_OF_MONTH, 1);
		endCal.set(Calendar.HOUR_OF_DAY, 0);
		endCal.set(Calendar.MINUTE, 0);
		endCal.set(Calendar.SECOND, 0);
		endCal.set(Calendar.MILLISECOND, 0);
			endDate = endCal.getTime();
		}

		// 调用getPendingBiz方法获取待办业务数据
		MedicalCasesStatusStatisticReq req = new MedicalCasesStatusStatisticReq();
		req.setStartTime(startDate);
		req.setEndTime(endDate);
		CommonResult<MedicalCasesStatusStatisticResp> pendingBizResult = getPendingBiz(req);
		MedicalCasesStatusStatisticResp pendingBizData = pendingBizResult.getData();
		
		// 将待办业务数据添加到返回结果中
		resp.setPendingBizData(pendingBizData);
		
		// 构建查询条件 - 使用finalReviewTime而不是createTime
		Criteria criteria = Criteria.where("status").is("Done")
				.and("finalReviewTime").gte(startDate).lt(endDate);

		// 用于统计的变量（只在方法内使用，不再设置到resp中）
		long totalDoneCount = 0;
		double totalAvgProcessingTime = 0;
		int dataPointsWithTime = 0;

		// 统计数据点列表
		List<CountPoint> countPoints = new ArrayList<>();
		
		if (month != null && month >= 1 && month <= 12) {
			// 按天统计
			int daysInMonth = getDaysInMonth(year, month);
			for (int i = 1; i <= daysInMonth; i++) {
				countPoints.add(new CountPoint(i, 0L, 0.0));
			}
			
			// 1. 获取每天的业务办结数量
			Aggregation countAggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
				Aggregation.project()
					.andExpression("year(finalReviewTime)").as("year")
					.andExpression("month(finalReviewTime)").as("month")
					.andExpression("dayOfMonth(finalReviewTime)").as("day"),
				Aggregation.group("day").count().as("count")
			);

			AggregationResults<Document> countResults = mongoTemplate.aggregate(
				countAggregation,
				"medicalCases",
				Document.class
			);

			for (Document doc : countResults) {
				Integer resultDay = doc.getInteger("_id"); // 1-31
				long count = doc.getInteger("count").longValue();
				totalDoneCount += count;
				
				if (resultDay != null && resultDay >= 1 && resultDay <= daysInMonth) {
					countPoints.get(resultDay - 1).setY(count);
				}
			}
			
			// 2. 获取每天的平均处理时长
			Aggregation avgTimeAggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
				Aggregation.project()
					.andExpression("dayOfMonth(finalReviewTime)").as("day")
					.andExpression("dateDiff(finalReviewTime, createTime, 'day')").as("processingTime"),
				Aggregation.group("day")
					.avg("processingTime").as("avgProcessingTime")
			);

			AggregationResults<Document> avgTimeResults = mongoTemplate.aggregate(
				avgTimeAggregation,
				"medicalCases",
				Document.class
			);

			for (Document doc : avgTimeResults) {
				Integer resultDay = doc.getInteger("_id"); // 1-31
				Double avgTime = doc.getDouble("avgProcessingTime");
				if (resultDay != null && resultDay >= 1 && resultDay <= daysInMonth && avgTime != null) {
					// 保留小数点后一位，这是平均处理天数
					avgTime = Math.round(avgTime * 10) / 10.0;
					countPoints.get(resultDay - 1).setAvgTime(avgTime);
					
					totalAvgProcessingTime += avgTime;
					dataPointsWithTime++;
				}
			}
		} else {
			// 按月统计
			for (int i = 1; i <= 12; i++) {
				countPoints.add(new CountPoint(i, 0L, 0.0));
			}

		// 1. 获取每月的业务办结数量
		Aggregation countAggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
			Aggregation.project()
					.andExpression("year(finalReviewTime)").as("year")
					.andExpression("month(finalReviewTime)").as("month"),
			Aggregation.group("month").count().as("count")
		);

		AggregationResults<Document> countResults = mongoTemplate.aggregate(
			countAggregation,
			"medicalCases",
			Document.class
		);

		for (Document doc : countResults) {
				Integer resultMonth = doc.getInteger("_id"); // 1-12
			long count = doc.getInteger("count").longValue();
				totalDoneCount += count;
				
				if (resultMonth != null && resultMonth >= 1 && resultMonth <= 12) {
					countPoints.get(resultMonth - 1).setY(count);
			}
		}
		
		// 2. 获取每月的平均处理时长
		Aggregation avgTimeAggregation = Aggregation.newAggregation(
				Aggregation.match(criteria),
			Aggregation.project()
					.andExpression("month(finalReviewTime)").as("month")
					.andExpression("dateDiff(finalReviewTime, createTime, 'day')").as("processingTime"),
			Aggregation.group("month")
				.avg("processingTime").as("avgProcessingTime")
		);

		AggregationResults<Document> avgTimeResults = mongoTemplate.aggregate(
			avgTimeAggregation,
			"medicalCases",
			Document.class
		);

		for (Document doc : avgTimeResults) {
				Integer resultMonth = doc.getInteger("_id"); // 1-12
			Double avgTime = doc.getDouble("avgProcessingTime");
				if (resultMonth != null && resultMonth >= 1 && resultMonth <= 12 && avgTime != null) {
				// 保留小数点后一位，这是平均处理天数
				avgTime = Math.round(avgTime * 10) / 10.0;
					countPoints.get(resultMonth - 1).setAvgTime(avgTime);
					
					totalAvgProcessingTime += avgTime;
					dataPointsWithTime++;
			}
		}
		}
		
		// 设置统计数据点
		resp.setDoneCountPoints(countPoints);
		
		// 3. 获取金额统计
		Aggregation amountAggregation = Aggregation.newAggregation(
			Aggregation.match(criteria),
			Aggregation.unwind("billingInfos", true),
			Aggregation.group()
				.sum("billingInfos.amountInCent").as("totalAmountInCent")
				.sum("billingInfos.reimbursableAmountInCent").as("totalReimbursableAmountInCent")
				.sum("billingInfos.nonReimbursableAmountInCent").as("totalNonReimbursableAmountInCent")
				.sum("billingInfos.hospitalFoodAllowance").as("totalFoodAllowanceInCent")
		);

		AggregationResults<Document> amountResults = mongoTemplate.aggregate(
			amountAggregation,
			"medicalCases",
			Document.class
		);
		
		// 设置金额统计结果
		AmountStatisticData amountData = new AmountStatisticData();
		if (!amountResults.getMappedResults().isEmpty()) {
			Document amountDoc = amountResults.getMappedResults().get(0);
			
			// 设置金额统计数据
			if (amountDoc.get("totalAmountInCent") != null) {
				amountData.setTotalAmountInCent(amountDoc.getInteger("totalAmountInCent"));
			}
			
			if (amountDoc.get("totalReimbursableAmountInCent") != null) {
				amountData.setTotalReimbursableAmountInCent(amountDoc.getInteger("totalReimbursableAmountInCent"));
			}
			
			if (amountDoc.get("totalNonReimbursableAmountInCent") != null) {
				amountData.setTotalNonReimbursableAmountInCent(amountDoc.getInteger("totalNonReimbursableAmountInCent"));
			}
			
			if (amountDoc.get("totalFoodAllowanceInCent") != null) {
				amountData.setTotalFoodAllowanceInCent(amountDoc.getInteger("totalFoodAllowanceInCent"));
			}
		}
		
		// 将金额统计结果添加到返回结果中
		resp.setAmountData(amountData);
		
		// 设置返回结果
		resp.setYear(year);
		resp.setMonth(month);
		
		return CommonResult.successData(resp);
	}
	
	/**
	 * 获取指定年月的天数
	 * @param year 年份
	 * @param month 月份（1-12）
	 * @return 该月的天数
	 */
	private int getDaysInMonth(int year, int month) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.YEAR, year);
		calendar.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
	}
}