package com.yixun.wid.v2.vo;

import com.yixun.wid.v2.entity.ConclusionReview;
import com.yixun.wid.v2.entity.LegalClause;
import lombok.Data;

import java.util.List;

/**
 * AI结论审核响应实体
 * 字段结构与AcceptConclusion实体类完全相同
 */
@Data
public class AiConclusionResponseVO {

    /**
     * 结论
     */
    private String conclusion;

    /**
     * 理由
     */
    private String reason;

    /**
     * 文书编号
     */
    private String writSn;

    /**
     * 受理时间
     */
    private String acceptDateTime;

    /**
     * 法规依据
     */
    private List<LegalClause> legalClauses;

    /**
     * AI结论审核
     */
    private ConclusionReview conclusionReview;
}
