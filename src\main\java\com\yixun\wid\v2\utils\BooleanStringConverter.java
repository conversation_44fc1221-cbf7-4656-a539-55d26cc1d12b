package com.yixun.wid.v2.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

public class BooleanStringConverter extends cn.idev.excel.converters.booleanconverter.BooleanStringConverter {

	@Override
	public WriteCellData<?> convertToExcelData(Boolean value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
		if (ObjectUtil.isNull(value)) {
			return new WriteCellData<>("否");
		}
		if (value) {
			return new WriteCellData<>("是");
		} else {
			return new WriteCellData<>("否");
		}
	}
}
