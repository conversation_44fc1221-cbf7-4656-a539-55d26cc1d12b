package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 账单信息输入参数
 */
@Data
public class BillingInfoIn {
    
    /**
     * 治疗医院
     */
    private String hospital;
    
    /**
     * 治疗类型
     */
    private String treatmentType;
    
    /**
     * 关联的工伤待遇业务ID
     */
    @NotNull(message = "工伤待遇业务ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medicalCasesId;
} 