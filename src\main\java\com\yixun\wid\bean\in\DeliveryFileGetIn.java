package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DeliveryFileGetIn {

    @ApiModelProperty("环节 受理，认定")
    private String step;

    @ApiModelProperty("职工姓名")
    private String name;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("送达类别 邮寄，自领")
    private String type;

    @ApiModelProperty("事故发生时间开始")
    private Date startDate;

    @ApiModelProperty("事故发生时间结束")
    private Date endDate;
}
