package com.yixun.wid.v2.entity;

import org.springframework.data.annotation.Transient;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 住院核销明细详情项
 * 默认折叠，点击"查看明细"，查看该账单的各分项的申报总金额、扣减总费用（审核扣减+非工伤扣减）、可报销金额
 */
@Data
public class HospitalClearingDetailItem {
    
    /**
     * 费用项目（该账单涵盖的费用项目）
     */
    private String expenseItem;
    
    /**
     * 费用项目编码
     */
    private String expenseItemCode;
    
    /**
     * 申报总金额（分，数据库存储，该账单各费用项目账单金额）
     */
    private Integer totalClaimAmountInCent;
    /**
     * 申报总金额（元，接口交互）
     */
    @Transient
    private BigDecimal totalClaimAmount;
    
    /**
     * 扣减总费用（分，数据库存储，审核扣减+非工伤扣减，该账单各费用项目对应审核扣减金额+非工伤扣减）
     */
    private Integer totalDeductionAmountInCent;
    /**
     * 扣减总费用（元，接口交互）
     */
    @Transient
    private BigDecimal totalDeductionAmount;
    
    /**
     * 应付金额（分，数据库存储，该账单合各费用项目对应合理费用）
     */
    private Integer payableAmountInCent;
    /**
     * 应付金额（元，接口交互）
     */
    @Transient
    private BigDecimal payableAmount;
    
    // 元分转换方法
    public BigDecimal getTotalClaimAmount() {
        if (totalClaimAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalClaimAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalClaimAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalClaimAmountInCent = null;
        } else {
            this.totalClaimAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalDeductionAmount() {
        if (totalDeductionAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalDeductionAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalDeductionAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalDeductionAmountInCent = null;
        } else {
            this.totalDeductionAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getPayableAmount() {
        if (payableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(payableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setPayableAmount(BigDecimal amount) {
        if (amount == null) {
            this.payableAmountInCent = null;
        } else {
            this.payableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
} 