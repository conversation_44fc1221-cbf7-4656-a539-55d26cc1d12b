package com.yixun.wid.v2.entity;

import lombok.Data;

import java.util.List;

@Data
public class TreatmentMaterials {

	private String nodecode;

	private String label;

	private String datatype;

	private List<TreatmentFile> fileList;

	@Data
	public static class TreatmentFile {

		private String url;

		private String name;

		private String origin;

		/**
		 * 文件类型
		 */
		private String fileType;

		/**
		 * 获取时间
		 */
		private String time;

		private String user;

		private String id;

		/**
		 * 分组类型 例如 就诊类型 （住院/门诊）
		 */
		private String groupType;

	}

}
