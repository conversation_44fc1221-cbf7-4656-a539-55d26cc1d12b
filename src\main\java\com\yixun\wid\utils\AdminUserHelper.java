package com.yixun.wid.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.other.LoginToken;
import com.yixun.wid.exception.UnLoginException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 获取当前管理员的 辅助类
 */
@Component
public class AdminUserHelper {

	/**
	 * 得到当前用户Id
	 * @return Long
	 */
	public static Long getCurrentUserId() {
        String env = SpringContextHolder.getContext().getEnvironment().getProperty("spring.profiles.active");
        if (env.equals("dev")){
            return 10000L;
        }

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes==null){
            throw new UnLoginException(ErrorMessage.not_logged_in+0);
        }
        HttpServletRequest request = requestAttributes.getRequest();
        String tokenValue = request.getHeader("Authorization");
        if (null == tokenValue)
            throw new UnLoginException(ErrorMessage.not_logged_in+1);
        LoginToken loginToken = JSON.parseObject(tokenValue, new TypeReference<LoginToken>(){});
        if (null == loginToken)
            throw new UnLoginException(ErrorMessage.not_logged_in+2);
        RedisTemplate redisTemplate = SpringContextHolder.getContext().getBean("customRedisTemplate", RedisTemplate.class);
        String token = (String) redisTemplate.opsForValue().get(RedisKeyResolver.getAdminUserToken(loginToken.getToken()));
        if (null == token)
            throw new UnLoginException(ErrorMessage.not_logged_in+3);
        String[] data = token.split("#"); //token的第3个值为userId
        return Long.parseLong(data[2]);
	}

}
