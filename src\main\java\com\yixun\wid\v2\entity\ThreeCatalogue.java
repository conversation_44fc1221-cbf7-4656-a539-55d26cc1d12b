package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 社保三目录
 */
@Data
public class ThreeCatalogue {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 目录编码 可重复
	 */
	private String sn;

	/**
	 * 目录类别
	 */
	@NotNull(message = "目录类别不能为空")
	private String type;

	/**
	 * 项目名称
	 */
	@NotNull(message = "项目名称不能为空")
	private String projectName;

	/**
	 * 费用等级 甲乙丙
	 */
	@NotNull(message = "甲乙丙不能为空")
	private String level;

	/**
	 * 开始日期
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date startDate;

	/**
	 * 结束日期
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date endDate;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date updateTime;

}
