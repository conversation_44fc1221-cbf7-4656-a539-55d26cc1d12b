package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EvidenceInfo {

    @ApiModelProperty("工作制度")
    private WorkRegime workRegime;

    @Data
    private static class WorkRegime {
        @ApiModelProperty("作息时间表")
        private EvidenceDetail dailySchedule;
        @ApiModelProperty("工作制度")
        private EvidenceDetail workRegime;
        @ApiModelProperty("岗位责任制度")
        private EvidenceDetail positionResponsibleRegime;
        @ApiModelProperty("通报通知")
        private EvidenceDetail bulletinNotice;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("上下班记录")
    private WorkRecord workRecord;

    @Data
    private static class WorkRecord {
        @ApiModelProperty("打卡记录")
        private EvidenceDetail clockInOutRecord;
        @ApiModelProperty("考勤登记表")
        private EvidenceDetail attendanceSheet;
        @ApiModelProperty("记工记件表")
        private EvidenceDetail workPointsSheet;
        @ApiModelProperty("监控视频")
        private EvidenceDetail monitorVideo;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("岗位作业")
    private PositionRecord positionRecord;

    @Data
    private static class PositionRecord {
        @ApiModelProperty("交接班表")
        private EvidenceDetail handoverRecord;
        @ApiModelProperty("值班登记表")
        private EvidenceDetail dutyRecordSheet;
        @ApiModelProperty("排班表")
        private EvidenceDetail schedulingSheet;
        @ApiModelProperty("监控视频")
        private EvidenceDetail monitorVideo;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("事故现场")
    private AccidentScene accidentScene;

    @Data
    private static class AccidentScene {
        @ApiModelProperty("监控视频")
        private EvidenceDetail monitorVideo;
        @ApiModelProperty("现场遗迹")
        private EvidenceDetail sceneRemains;
        @ApiModelProperty("肇事设备")
        private EvidenceDetail troubleMakeDevice;
        @ApiModelProperty("个人物品")
        private EvidenceDetail personalItems;
        @ApiModelProperty("目击证人")
        private EvidenceDetail witness;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("救护过程")
    private RescueProcess rescueProcess;

    @Data
    private static class RescueProcess {
        @ApiModelProperty("120记录")
        private EvidenceDetail recordOf120;
        @ApiModelProperty("门诊急救")
        private EvidenceDetail firstAid;
        @ApiModelProperty("住院病历")
        private EvidenceDetail inpatientRecord;
        @ApiModelProperty("参与工友")
        private EvidenceDetail participant;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("事故处置")
    private AccidentHandling accidentHandling;

    @Data
    private static class AccidentHandling {
        @ApiModelProperty("单位组织")
        private EvidenceDetail companyActions;
        @ApiModelProperty("报110")
        private EvidenceDetail reportTo110;
        @ApiModelProperty("报120")
        private EvidenceDetail reportTo120;
        @ApiModelProperty("报安监")
        private EvidenceDetail reportToSupervision;
        @ApiModelProperty("其它")
        private EvidenceDetail other;
    }

    @ApiModelProperty("其他证据")
    private OtherEvidence otherEvidence;

    @Data
    private static class OtherEvidence {
        @ApiModelProperty("其他证据")
        private EvidenceDetail other;
    }
}
