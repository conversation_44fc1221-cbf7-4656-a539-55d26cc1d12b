package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.service.WeixinService;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.BizItem;
import com.yixun.wid.v2.entity.BizType;
import com.yixun.wid.v2.enums.GeneralStatusEnum;
import com.yixun.wid.v2.enums.UserType;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.SortVO;
import com.yixun.wid.v2.vo.WxaCodeVO;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 办事项目相关接口
 */
@RequestMapping("/v2/biz/item")
@RestController
@AllArgsConstructor
public class BizItemController {

	private final MongoTemplate mongoTemplate;

	private final BizTypeController bizTypeController;

	private final WeixinService weixinService;

	/**
	 * 获取办事项目小程序码
	 * @param id 业务id
	 * @return 小程序码
	 */
	@GetMapping("/wxaCode")
	public CommonResult<WxaCodeVO> getWxaCode(@RequestParam String id) {
		return CommonResult.successData(weixinService.getWxaCode(id));
	}

	/**
	 * 新增办事项目
	 *
	 * @param bizItem 办事项目
	 */
	@PostMapping("/save")
	public CommonResult<Void> save(@RequestBody BizItem bizItem) {
		bizItem.setId(SnGeneratorUtil.getId());
		bizItem.setDeleted(GeneralStatusEnum.OFF.getCode());
		bizItem.setSort(SnGeneratorUtil.getId());
		if (ObjectUtil.isNull(bizItem.getStatus())) {
			bizItem.setStatus(GeneralStatusEnum.ON.getCode());
		}
		bizItem.setCreateTime(new Date());
		bizItem.setUpdateTime(new Date());
		mongoTemplate.save(bizItem);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 查询办事项目列表
	 *
	 * @param deleted    是否删除
	 * @param search     名字搜索
	 * @param commonPage 分页参数
	 * @param bizTypeId  业务类型id
	 * @return 办事项目列表
	 */
	@GetMapping("/list")
	public CommonResult<List<BizItem>> list(@RequestParam(required = false) Integer status, @RequestParam(defaultValue = "0", required = false) Integer deleted,
		@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Long bizTypeId) {
		Query query =
			Query.query(Criteria.where("deleted").is(deleted))
				.with(Sort.by(Sort.Direction.DESC, "sort"))
				.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		if (StrUtil.isNotBlank(search)) {
			UserType userType = ServletRequestUtils.getUserType();
			if (UserType.USER.equals(userType)) {
				CommonResult<List<BizType>> list = bizTypeController.list(status, 0, search);
				if (ObjectUtil.isNotNull(list.getData()) && !list.getData().isEmpty()) {
					List<Long> bizTypeIds = list.getData().stream().map(BizType::getId).collect(Collectors.toList());
					Criteria criteria = new Criteria();
					criteria.orOperator(
						Criteria.where("bizItemName").regex(".*" + search + ".*", "i"),
						Criteria.where("bizTypeId").in(bizTypeIds)
					);
					query.addCriteria(criteria);
				} else {
					query.addCriteria(Criteria.where("bizItemName").regex(".*" + search + ".*", "i"));
				}
			} else {
				query.addCriteria(Criteria.where("bizItemName").regex(".*" + search + ".*", "i"));
			}
		}
		if (ObjectUtil.isNotNull(bizTypeId)) {
			query.addCriteria(Criteria.where("bizTypeId").is(bizTypeId));
		}
		if (ObjectUtil.isNotNull(status)) {
			query.addCriteria(Criteria.where("status").is(status));
		}
		MongoUtil.setPageInfo(mongoTemplate, BizItem.class, query, commonPage);
		List<BizItem> bizItems = mongoTemplate.find(query, BizItem.class);
		CommonResult<List<BizType>> list = bizTypeController.list(status, 0, null);
		Map<Long, BizType> collect = list.getData().stream().collect(Collectors.toMap(BizType::getId, Function.identity()));
		for (BizItem bizItem : bizItems) {
			Long typeId = bizItem.getBizTypeId();
			if (ObjectUtil.isNotNull(typeId)) {
				BizType type = collect.get(typeId);
				if (ObjectUtil.isNotNull(type)) {
					bizItem.setBizTypeName(type.getBizTypeName());
				}
			}
		}
		return CommonResult.successPageData(bizItems, commonPage);
	}

	/**
	 * 查询办事项目列表 不鉴权
	 *
	 * @param deleted    是否删除
	 * @param search     名字搜索
	 * @param commonPage 分页参数
	 * @param bizTypeId  业务类型id
	 * @return 办事项目列表
	 */
	@GetMapping("/list2")
	public CommonResult<List<BizItem>> list2(@RequestParam(required = false) Integer status, @RequestParam(defaultValue = "0", required = false) Integer deleted,
		@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Long bizTypeId) {
		Query query =
			Query.query(Criteria.where("deleted").is(deleted))
				.with(Sort.by(Sort.Direction.DESC, "sort"))
				.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		// 一体机暂时不允许搜索
		search = null;
		if (StrUtil.isNotBlank(search)) {
			UserType userType = ServletRequestUtils.getUserType();
			if (UserType.USER.equals(userType)) {
				CommonResult<List<BizType>> list = bizTypeController.list(status, 0, search);
				if (ObjectUtil.isNotNull(list.getData()) && !list.getData().isEmpty()) {
					List<Long> bizTypeIds = list.getData().stream().map(BizType::getId).collect(Collectors.toList());
					Criteria criteria = new Criteria();
					criteria.orOperator(
						Criteria.where("bizItemName").regex(".*" + search + ".*", "i"),
						Criteria.where("bizTypeId").in(bizTypeIds)
					);
					query.addCriteria(criteria);
				} else {
					query.addCriteria(Criteria.where("bizItemName").regex(".*" + search + ".*", "i"));
				}
			} else {
				query.addCriteria(Criteria.where("bizItemName").regex(".*" + search + ".*", "i"));
			}
		}
		if (ObjectUtil.isNotNull(bizTypeId)) {
			query.addCriteria(Criteria.where("bizTypeId").is(bizTypeId));
		}
//		if (ObjectUtil.isNotNull(status)) {
			query.addCriteria(Criteria.where("status").is(1));
//		}
		MongoUtil.setPageInfo(mongoTemplate, BizItem.class, query, commonPage);
		List<BizItem> bizItems = mongoTemplate.find(query, BizItem.class);
		CommonResult<List<BizType>> list = bizTypeController.list(status, 0, null);
		Map<Long, BizType> collect = list.getData().stream().collect(Collectors.toMap(BizType::getId, Function.identity()));
		for (BizItem bizItem : bizItems) {
			Long typeId = bizItem.getBizTypeId();
			if (ObjectUtil.isNotNull(typeId)) {
				BizType type = collect.get(typeId);
				if (ObjectUtil.isNotNull(type)) {
					bizItem.setBizTypeName(type.getBizTypeName());
				}
			}
		}
		return CommonResult.successPageData(bizItems, commonPage);
	}

	/**
	 * 根据id查询
	 *
	 * @param id 主键
	 * @return 办事项目
	 */
	@GetMapping
	public CommonResult<BizItem> getById(@RequestParam("id") Long id) {
		BizItem byId = mongoTemplate.findById(id, BizItem.class);
		if (ObjectUtil.isNull(byId) || GeneralStatusEnum.ON.getValue().equals(byId.getDeleted())) {
			throw new RuntimeException("办事项目不存在");
		}
		Long bizTypeId = byId.getBizTypeId();
		if (ObjectUtil.isNotNull(bizTypeId)) {
			try {
				CommonResult<BizType> result = bizTypeController.getById(bizTypeId);
				BizType data = result.getData();
				byId.setBizTypeName(data.getBizTypeName());
			} catch (Exception ignore) {
			}
		}
		return CommonResult.successData(byId);
	}

	/**
	 * 根据id查询 不鉴权
	 *
	 * @param id 主键
	 * @return 办事项目
	 */
	@GetMapping("/getById2")
	public CommonResult<BizItem> getById2(@RequestParam("id") Long id) {
		BizItem byId = mongoTemplate.findById(id, BizItem.class);
		if (ObjectUtil.isNull(byId) || GeneralStatusEnum.ON.getValue().equals(byId.getDeleted())) {
			throw new RuntimeException("办事项目不存在");
		}
		Long bizTypeId = byId.getBizTypeId();
		if (ObjectUtil.isNotNull(bizTypeId)) {
			try {
				CommonResult<BizType> result = bizTypeController.getById(bizTypeId);
				BizType data = result.getData();
				byId.setBizTypeName(data.getBizTypeName());
			} catch (Exception ignore) {
			}
		}
		return CommonResult.successData(byId);
	}

	/**
	 * 更新办事项目
	 *
	 * @param bizItem 办事项目
	 * @return 更新结果
	 */
	@PostMapping("/update")
	public CommonResult<Void> update(@RequestBody BizItem bizItem) {
		Long id = bizItem.getId();
		if (ObjectUtil.isNull(id)) {
			throw new RuntimeException("id不能为空");
		}
		BizItem byId = mongoTemplate.findById(id, BizItem.class);
		if (ObjectUtil.isNull(byId)) {
			throw new RuntimeException("办事项目不存在");
		}
		BeanUtil.copyProperties(bizItem, byId, CopyOptions.create().ignoreNullValue());
		byId.setUpdateTime(new Date());
		mongoTemplate.save(byId);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 批量逻辑删除办事项目
	 *
	 * @param ids 办事项目ID列表
	 * @return 删除结果
	 */
	@PostMapping("/batchDelete")
	public CommonResult<Void> batchDelete(@RequestBody List<Long> ids) {
		if (ObjectUtil.isEmpty(ids)) {
			throw new RuntimeException("id列表不能为空");
		}

		BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, BizItem.class);
		for (Long id : ids) {
			Update update = Update.update("deleted", GeneralStatusEnum.ON.getCode())
				.set("updateTime", new Date());
			operations.updateOne(Query.query(Criteria.where("id").is(id)), update);
		}
		operations.execute();
		return CommonResult.successResult("删除成功");
	}

	/**
	 * 更新办事项目列表排序
	 *
	 * @param sort 列表排序
	 * @return 更新结果
	 */
	@PostMapping("/update/sort")
	public CommonResult<Void> updateSort(@RequestBody List<SortVO> sort) {
		BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, BizItem.class);
		for (SortVO sortVO : sort) {
			Update update = Update.update("id", sortVO.getId())
				.set("sort", sortVO.getSort())
				.set("updateTime", new Date());
			operations.updateOne(Query.query(Criteria.where("id").is(sortVO.getId())), update);
		}
		operations.execute();
		return CommonResult.successResult("操作成功");
	}

}
