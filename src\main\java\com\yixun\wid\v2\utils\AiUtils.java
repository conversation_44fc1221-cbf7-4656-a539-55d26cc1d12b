package com.yixun.wid.v2.utils;


import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.v2.vo.SimilarAiCheckIn;
import com.yixun.wid.v2.vo.SimilarAiCheckOut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
public class AiUtils {

//    @Resource
//    private OSSService osService;

    @Value("${api.aiCorpSimilarity}")
    private String url;

//    /**
//     *  根据企业名检查相似机构
//     * @param organizationName 企业名
//     * @return 带有相似度的相似企业名列表
//     */
//    public  ArrayList<String> checkSimilar( String organizationName) {
//        SimilarImportAiCheckIn similarImportAiCheckIn = new SimilarImportAiCheckIn("yqyc", "corporation", "corp_name", 100, organizationName);
//        //
//        String aiCorpSimilarity = "http://192.168.252.61:9216";
//        String body = HttpRequest.post(url + "/common_similar_search")
//                .header("Content-Type", "application/json")
//                .body(JSON.toJSONString(similarImportAiCheckIn))
//                .execute()
//                .body();
//        if (body != null && !body.isEmpty()) {
//            try {
//                SimilarImportAiCheckOut similarImportAiCheckOut = JSONObject.parseObject(body, SimilarImportAiCheckOut.class);
//                if (similarImportAiCheckOut.getResult() != null && !similarImportAiCheckOut.getResult().isEmpty()) {
//                    return getSimilarList(similarImportAiCheckOut);
//                }
//            } catch (Exception e) {
//                System.out.println(e.getMessage());
//                log.error("AI接口响应解析异常{}", body);
//            }
//
//        } else {
//            throw new RuntimeException("AI接口调用失败");
//        }
//        return new ArrayList<>();
//    }
    /**
     * 根据企业名检查相似列表
     * @param organizationName 企业名
     * @return 带有相似度的相似企业信息列表
     */
    public SimilarAiCheckOut checkSimilar(String organizationName, int num) {
        SimilarAiCheckIn similarImportAiCheckIn = new SimilarAiCheckIn(organizationName, num);
        String aiCorpSimilarity = "http://192.168.252.61:9216";
        String body = HttpRequest.post(url + "/search_company_by_name_from_corporation_2")
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(similarImportAiCheckIn))
                .execute()
                .body();
        if (body != null && !body.isEmpty()) {
            try {
                return JSONObject.parseObject(body, SimilarAiCheckOut.class);
            } catch (Exception e) {
                System.out.println(e.getMessage());
                log.error("AI接口响应解析异常{}", body);
            }

        } else {
            throw new DataErrorException("AI接口调用失败");
        }
        return null;
    }



//    /**
//     * 获取相似机构
//     * @param similarImportAiCheckOut AI相似机构响应
//     * @return 相似机构列表
//     */
//    private  ArrayList<String> getSimilarList(SimilarImportAiCheckOut similarImportAiCheckOut) {
//        List<SimilarImportAiCheckResult> result = similarImportAiCheckOut.getResult();
//        ArrayList<String> similarTarget = new ArrayList<>();
//        for (SimilarImportAiCheckResult similarImportAiCheckResult : result) {
//            String score = similarImportAiCheckResult.getScore();
//            if (Double.parseDouble(score) >= 0.8) {
//                String target = similarImportAiCheckResult.getTarget();
//                similarTarget.add(target);
//            }
//        }
//        return similarTarget;
//    }

//    /**
//     * 根据企业名获取相同字号的相似企业
//     * @param organizationName 企业名
//     * @return 相似字号的相似企业列表
//     *
//     */
//    public  ArrayList<String> checkSimilarByFont(String organizationName) {
//        ArrayList<String> similarTarget = new ArrayList<>();
//        CheckSimilarByFontIn similarImportAiCheckIn = new CheckSimilarByFontIn(organizationName, 5);
//        // @Value("${api.aiCorpSimilarity}")
//        String aiCorpSimilarity = "http://192.168.252.61:9216";
//        String body = HttpRequest.post(url + "/search_company_by_name")
//                .header("Content-Type", "application/json")
//                .body(JSON.toJSONString(similarImportAiCheckIn))
//                .execute()
//                .body();
//        if (body != null && !body.isEmpty()) {
//            try {
//                CheckSimilarByFontOut checkSimilarByFontOut = JSONObject.parseObject(body, CheckSimilarByFontOut.class);
//                String processName = checkSimilarByFontOut.getProcessName();
//                List<checkSimilarByFontOutResult> result = checkSimilarByFontOut.getResult();
//                if (result != null && !result.isEmpty()) {
//                    result.forEach(checkSimilarByFontOutResult -> {
//                        String rawName = checkSimilarByFontOutResult.getRawName();
//                        String rProcessName = checkSimilarByFontOutResult.getProcessName();
//                        // 如果result中有，且字号相同 则添加到相似字号列表中
//                        if (processName.equals(rProcessName)){
//                            similarTarget.add(rawName);
//                        }
//
//                    });
//                }
//            } catch (Exception e) {
//                System.out.println(e.getMessage());
//                log.error("AI接口响应解析异常{}", body);
//            }
//
//        } else {
//            throw new RuntimeException("AI接口调用失败");
//        }
//
//
//        return similarTarget;
//    }



//    /**
//     *  根据身份证正面 调用ai 提取用户头像 下载并上传到oss
//     * @param idCardFront 身份证正面
//     * @return 头像的oss地址
//     */
//    public  String getAvatarByIdCardFront(String idCardFront,Long userId) {
//        if (idCardFront == null || idCardFront.isEmpty()){
//            log.error("AI接口调用失败，参数为空");
//            throw new DataErrorException("AI接口调用失败，参数为空");
//        }
//        String aiCorpSimilarity = "http://192.168.252.61:9219";
//        String body = HttpRequest.get(aiCorpSimilarity + "/face_detect?image_url="+idCardFront)
//                .header("Content-Type", "application/json")
//                .execute()
//                .body();
//        if (body != null && !body.isEmpty()) {
//            try {
//                JSONObject jsonObject = JSON.parseObject(body);
//                if (jsonObject.getBoolean("success_flag")){
//                    String faceUrl = jsonObject.getString("face_url");
//                    if (faceUrl!=null && !faceUrl.isEmpty()){
//                        //下载头像
//                        File file = DownloadUtils.downloadImage(faceUrl);
//                        //文件名
//                        String fileName=userId+"."+("tmp".equals(getFileExtension(faceUrl))?getFileExtension(file.getName()):getFileExtension(faceUrl));
//                        //上传头像
//                        String aliCloudOssAvatarUrl = putFileToAliCloudOss(file,fileName);
//                        if (aliCloudOssAvatarUrl!=null){
//                            return aliCloudOssAvatarUrl;
//                        }else {
//                            log.error("aliCloudOss头像上传失败");
//                            throw new DataErrorException("aliCloudOss头像上传失败");
//                        }
//                    }else {
//                        log.error("AI接口响应头像地址为空");
//                        throw new DataErrorException("AI接口响应头像地址为空");
//                    }
//                }else {
//                    log.error("AI接口调用失败{}",jsonObject );
//                }
//            }catch (RuntimeException e){
//                log.error( e.getMessage());
//                throw new DataErrorException("提取用户头像 下载并上传到oss异常");
//            } catch (IOException e) {
//                log.error( e.getMessage());
//                throw new DataErrorException("AI头像文件下载异常");
//            }
//        }
//
//        return null;
//
//    }

//    private  String putFileToAliCloudOss(File file,String fileName) {
//        return osService.uploadFile(fileName, file);
//    }
//
//    public static String getFileExtension(String filePath) {
//        if (filePath == null) {
//            return null;
//        }
//        int lastIndexOfDot = filePath.lastIndexOf(".");
//        if (lastIndexOfDot == -1) {
//            // 没有找到"."，可能没有后缀或者URL不包含文件名
//            return "tmp";
//        }
//        // 提取最后一个"."之后的所有字符作为文件后缀
//        return filePath.substring(lastIndexOfDot + 1);
//    }

}
