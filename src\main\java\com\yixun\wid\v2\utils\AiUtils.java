package com.yixun.wid.v2.utils;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.v2.vo.SimilarAiCheckIn;
import com.yixun.wid.v2.vo.SimilarAiCheckOut;
import com.yixun.wid.v2.vo.ai.*;
import com.yixun.wid.v2.vo.ai.AiProjectSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollUtil;
import java.util.AbstractMap;
import com.yixun.wid.v2.vo.medical.SimilarInstitutionResult;
import com.yixun.wid.v2.vo.ai.SimilarityResult;

@Slf4j
@Component
public class AiUtils {

    @Value("${api.llmsBaseUrl}")
    private String llmsBaseUrl;

    @Value("${api.ai.querySimilarityUrl}")
    private String querySimilarityUrl;

    @Value("${api.ai.materialClassificationUrl}")
    private String materialClassificationUrl;

    @Value("${api.ai.acceptedInformationDiagnosisUrl}")
    private String acceptedInformationDiagnosisUrl;

    @Value("${api.ai.surgicalInformationUrl}")
    private String surgicalInformationUrl;

    @Value("${api.ai.billInformationUrl}")
    private String billInformationUrl;

    @Value("${api.ai.billOcrUrl}")
    private String billOcrUrl;

    @Value("${api.ai.listOcrUrl}")
    private String listOcrUrl;

    @Value("${api.ai.calculatorUrl}")
    private String calculatorUrl;

    @Value("${api.ai.projectSearchUrl}")
    private String projectSearchUrl;

    private static final String SECRET_HEADER = "SDx78cfbjn";

    // ==================== 新增AI接口方法 ====================

    /**
     * 相似查找
     * @param querys 输入查询列表
     * @param targets 目标对比列表
     * @param topK 返回前K个结果
     * @param rerank 是否重新排序
     * @return 相似查找结果
     */
    public QuerySimilarityResponse querySimilarity(List<String> querys, List<String> targets, Integer topK, Boolean rerank) {
        QuerySimilarityRequest request = new QuerySimilarityRequest();
        request.setQuerys(querys);
        request.setTargets(targets);
        request.setTopK(topK);
        request.setRerank(rerank);

        try {
            String body = HttpUtil.createPost(querySimilarityUrl)
                    .header("SECRET", SECRET_HEADER)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, QuerySimilarityResponse.class);
            } else {
                throw new DataErrorException("相似查找接口调用失败");
            }
        } catch (Exception e) {
            log.error("相似查找接口调用异常", e);
            throw new DataErrorException("相似查找接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 材料分类
     * @param files 文件URL列表
     * @param type 类型：application/medical
     * @return 材料分类结果
     */
    public MaterialClassificationResponse materialClassification(List<String> files, String type) {
        MaterialClassificationRequest request = new MaterialClassificationRequest();
        request.setFiles(files);
        request.setType(type);

        try {
            String body = HttpUtil.createPost(materialClassificationUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, MaterialClassificationResponse.class);
            } else {
                throw new DataErrorException("材料分类接口调用失败");
            }
        } catch (Exception e) {
            log.error("材料分类接口调用异常", e);
            throw new DataErrorException("材料分类接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 受理信息和临床诊断
     * @param request 请求参数
     * @return 受理信息和临床诊断结果
     */
    public AcceptedInformationDiagnosisResponse acceptedInformationDiagnosis(AcceptedInformationDiagnosisRequest request) {
        try {
            String body = HttpUtil.createPost(acceptedInformationDiagnosisUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, AcceptedInformationDiagnosisResponse.class);
            } else {
                throw new DataErrorException("受理信息和临床诊断接口调用失败");
            }
        } catch (Exception e) {
            log.error("受理信息和临床诊断接口调用异常", e);
            throw new DataErrorException("受理信息和临床诊断接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 手术信息
     * @param request 请求参数（与受理信息和临床诊断相同的请求结构）
     * @return 手术信息结果
     */
    public SurgicalInformationResponse surgicalInformation(AcceptedInformationDiagnosisRequest request) {
        try {
            String body = HttpUtil.createPost(surgicalInformationUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, SurgicalInformationResponse.class);
            } else {
                throw new DataErrorException("手术信息接口调用失败");
            }
        } catch (Exception e) {
            log.error("手术信息接口调用异常", e);
            throw new DataErrorException("手术信息接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 账单信息
     * @param request 请求参数（与受理信息和临床诊断相同的请求结构）
     * @return 账单信息结果
     */
    public BillInformationResponse billInformation(AcceptedInformationDiagnosisRequest request) {
        try {
            String body = HttpUtil.createPost(billInformationUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, BillInformationResponse.class);
            } else {
                throw new DataErrorException("账单信息接口调用失败");
            }
        } catch (Exception e) {
            log.error("账单信息接口调用异常", e);
            throw new DataErrorException("账单信息接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 账单OCR
     * @param fileUrl 文件URL
     * @return 账单OCR结果
     */
    public BillInformationResponse billOcr(String fileUrl) {
        BillOcrRequest request = new BillOcrRequest();
        request.setFile(fileUrl);

        try {
            String body = HttpUtil.createPost(billOcrUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, BillInformationResponse.class);
            } else {
                throw new DataErrorException("账单OCR接口调用失败");
            }
        } catch (Exception e) {
            log.error("账单OCR接口调用异常", e);
            throw new DataErrorException("账单OCR接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 清单OCR
     * @param fileUrl 文件URL
     * @return 清单OCR结果
     */
    public ListOcrResponse listOcr(String fileUrl) {
        ListOcrRequest request = new ListOcrRequest();
        request.setFile(fileUrl);

        try {
            String body = HttpUtil.createPost(listOcrUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, ListOcrResponse.class);
            } else {
                throw new DataErrorException("清单OCR接口调用失败");
            }
        } catch (Exception e) {
            log.error("清单OCR接口调用异常", e);
            throw new DataErrorException("清单OCR接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 计算器
     * @param input 输入内容
     * @return 计算结果
     */
    public CalculatorResponse calculator(String input) {
        CalculatorRequest request = new CalculatorRequest();
        request.setInput(input);

        try {
            String body = HttpUtil.createPost(calculatorUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, CalculatorResponse.class);
            } else {
                throw new DataErrorException("计算器接口调用失败");
            }
        } catch (Exception e) {
            log.error("计算器接口调用异常", e);
            throw new DataErrorException("计算器接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 项目查询
     * @param input 输入内容
     * @return 项目查询结果
     */
    public ProjectSearchResponse projectSearch(String input) {
        AiProjectSearchRequest request = new AiProjectSearchRequest();
        request.setInput(input);

        try {
            String body = HttpUtil.createPost(projectSearchUrl)
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .execute()
                    .body();

            if (body != null && !body.isEmpty()) {
                return JSONObject.parseObject(body, ProjectSearchResponse.class);
            } else {
                throw new DataErrorException("项目查询接口调用失败");
            }
        } catch (Exception e) {
            log.error("项目查询接口调用异常", e);
            throw new DataErrorException("项目查询接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 查询相似项并按相似度从大到小排序
     *
     * @param name 待查询的名称
     * @param targetList 目标名称列表
     * @param topK 返回结果数量
     * @param similarityThreshold 相似度阈值，大于等于该值的结果才会返回
     * @return 相似结果列表，按相似度从大到小排序
     */
    public List<SimilarityResult> findSimilarItems(String name, List<String> targetList, int topK, double similarityThreshold) {
        if (StrUtil.isBlank(name) || CollUtil.isEmpty(targetList)) {
            return new ArrayList<>();
        }

        // 构建查询参数
        List<String> queryList = new ArrayList<>();
        queryList.add(name);

        try {
            // 调用相似度查询接口
            QuerySimilarityResponse response = querySimilarity(queryList, targetList, topK, true);

            if (response != null && "success".equals(response.getStatus()) &&
                response.getData() != null && !response.getData().isEmpty()) {

                QuerySimilarityResponse.SimilarityData data = response.getData().get(0);

                if (data != null && CollUtil.isNotEmpty(data.getIndices()) && CollUtil.isNotEmpty(data.getSimilarity())) {
                    // 创建结果列表
                    List<SimilarityResult> result = new ArrayList<>();

                    // 确保indices和similarity数组大小一致，取较小的值作为循环边界
                    int maxSize = Math.min(data.getIndices().size(), data.getSimilarity().size());

                    // 提取相似项和相似度
                    for (int i = 0; i < maxSize; i++) {
                        int index = data.getIndices().get(i);
                        double similarity = data.getSimilarity().get(i);

                        // 只添加相似度大于等于阈值的结果
                        if (similarity >= similarityThreshold && index < targetList.size()) {
                            result.add(new SimilarityResult(targetList.get(index), similarity));
                        }
                    }

                    // 按相似度从大到小排序
                    result.sort((e1, e2) -> Double.compare(e2.getSimilarity(), e1.getSimilarity()));

                    return result;
                }
            }
        } catch (Exception e) {
            log.error("查询相似项异常", e);
        }

        return new ArrayList<>();
    }

     /**
     * 获取结论审核推荐
     * 当前阶段模拟实现，返回示例数据
     *
     * @param requestVO AI请求实体
     * @return AI响应实体
     */
    public AiConclusionResponseVO getConclusionRecommendation(AiConclusionRequestVO requestVO) {
        log.info("调用AI结论审核推荐，事故经过：{}", requestVO.getAccidentDetail());

        // 当前阶段模拟实现，返回示例数据
        AiConclusionResponseVO responseVO = new AiConclusionResponseVO();

        // 模拟结论
        responseVO.setConclusion("予以受理");
        responseVO.setReason("根据《工伤保险条例》相关规定，该事故符合工伤认定条件，建议予以受理。");
        responseVO.setWritSn("AI-" + DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        responseVO.setAcceptDateTime(DateUtil.now());

        // 模拟法规依据
        List<LegalClause> legalClauses = new ArrayList<>();
        LegalClause clause1 = new LegalClause();
        clause1.setId(1L);
        clause1.setRegulationId(1L);
        clause1.setRegulationName("工伤保险条例");
        clause1.setClauseNumber(14);
        clause1.setClauseContent("职工有下列情形之一的，应当认定为工伤");
        clause1.setSubClauseNumber(1);
        clause1.setSubClauseContent("在工作时间和工作场所内，因工作原因受到事故伤害的");
        legalClauses.add(clause1);

        responseVO.setLegalClauses(legalClauses);

        // 模拟AI结论审核
        ConclusionReview conclusionReview = new ConclusionReview();
        conclusionReview.setConclusion("建议受理");
        conclusionReview.setReason("AI分析认为该事故符合工伤认定标准");
        conclusionReview.setLegalClauses(legalClauses);
        responseVO.setConclusionReview(conclusionReview);

        log.info("AI结论审核推荐完成，结论：{}", responseVO.getConclusion());
        return responseVO;
    }

}
