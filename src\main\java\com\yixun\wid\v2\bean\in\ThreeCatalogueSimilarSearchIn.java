package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 三目录相似搜索输入参数
 */
@Data
public class ThreeCatalogueSimilarSearchIn {
    
    /**
     * 目录类别（可选参数，可为空）
     */
    private String type;
    
    /**
     * 项目名称（必填参数）
     */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    /**
     * 返回结果数量（可选，默认为1）
     */
    private Integer topK = 1;
    
    /**
     * 相似度阈值（可选，默认为0.0）
     */
    private Double similarityThreshold = 0.0;
}
