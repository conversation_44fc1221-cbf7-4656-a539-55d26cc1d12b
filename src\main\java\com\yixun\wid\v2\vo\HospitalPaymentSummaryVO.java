package com.yixun.wid.v2.vo;

import lombok.Data;

/**
 * 医院支付汇总VO
 */
@Data
public class HospitalPaymentSummaryVO {

    /**
     * 总就诊人数
     */
    private Integer totalPatientCount;
    
    /**
     * 总发票数量
     */
    private Integer totalInvoiceCount;
    
    /**
     * 正常发票数量（合理费用≠0的）
     */
    private Integer normalInvoiceCount;
    
    /**
     * 核减发票数量（合理费用=0的）
     */
    private Integer deductedInvoiceCount;
    
    /**
     * 总金额（元）
     */
    private Double totalInvoiceAmount;
    
    /**
     * 门诊总金额（元）
     */
    private Double outpatientInvoiceAmount;
    
    /**
     * 住院总金额（元）
     */
    private Double inpatientInvoiceAmount;
    
    /**
     * 报销金额（元）
     */
    private Double totalReimbursableAmount;
    
    /**
     * 核减金额（元）
     */
    private Double totalNonReimbursableAmount;
    
    /**
     * 住院总天数
     */
    private Integer totalHospitalDays;
    
    /**
     * 住院伙食费总金额（元）
     */
    private Double totalHospitalFoodAllowance;
    
    /**
     * 非工伤扣减费用（元）
     */
    private Double nonWorkInjuryDeduction;
    
    public HospitalPaymentSummaryVO() {
        // 初始化默认值为0
        this.totalPatientCount = 0;
        this.totalInvoiceCount = 0;
        this.normalInvoiceCount = 0;
        this.deductedInvoiceCount = 0;
        this.totalInvoiceAmount = 0.0;
        this.outpatientInvoiceAmount = 0.0;
        this.inpatientInvoiceAmount = 0.0;
        this.totalReimbursableAmount = 0.0;
        this.totalNonReimbursableAmount = 0.0;
        this.totalHospitalDays = 0;
        this.totalHospitalFoodAllowance = 0.0;
        this.nonWorkInjuryDeduction = 0.0;
    }
} 