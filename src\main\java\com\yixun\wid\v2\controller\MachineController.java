package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.other.LoginToken;
import com.yixun.wid.bean.out.UserInfoCache;
import com.yixun.wid.entity.User;
import com.yixun.wid.entity.em.LoginType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AsyncService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.service.WeixinService;
import com.yixun.wid.utils.IpUtil;
import com.yixun.wid.utils.JwtTokenUtils;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.BizItem;
import com.yixun.wid.v2.entity.MachineBanner;
import com.yixun.wid.v2.entity.MachineVideo;
import com.yixun.wid.v2.utils.RSAUtils;
import com.yixun.wid.v2.vo.BatchStatusVO;
import com.yixun.wid.v2.vo.IdCardLoginVO;
import com.yixun.wid.v2.vo.IdsVO;
import com.yixun.wid.v2.vo.WxaCodeVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一体机相关接口
 */
@RequestMapping("/v2/machine")
@RestController
public class MachineController {

	@Resource
	private UserService userService;

	@Resource
	private ObjectMapper objectMapper;

	@Resource
	private AsyncService asyncService;

	@Value("${jwt.secret.account}")
	private String secret;

	@Value("${jwt.expiration}")
	private Long expiration;

	@Resource
	private MongoTemplate mongoTemplate;

	@Resource
	private WeixinService weixinService;

	/**
	 * 批量更新一体机video的enabled状态
	 * @param batchStatusVO 包含要更新的video ID列表和新的enabled状态
	 * @return 操作结果
	 */
	@PostMapping("/video/batch/enabled")
	public CommonResult<Void> batchUpdateVideoEnabled(@Validated @RequestBody BatchStatusVO batchStatusVO) {
		List<Long> ids = batchStatusVO.getIds();
		Query query = Query.query(Criteria.where("_id").in(ids));
		Update update = new Update().set("enabled", batchStatusVO.getEnabled());
		mongoTemplate.updateMulti(query, update, MachineVideo.class);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 更新一体机video
	 * @param machineVideo 一体机video信息
	 * @return 操作结果
	 */
	@PostMapping("/video/update")
	public CommonResult<Void> updateMachineVideo(@Validated @RequestBody MachineVideo machineVideo) {
		if (machineVideo.getId() == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
		}
		MachineVideo existingVideo = mongoTemplate.findById(machineVideo.getId(), MachineVideo.class);
		if (existingVideo == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
		}
		BeanUtil.copyProperties(machineVideo, existingVideo);
		mongoTemplate.save(existingVideo);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 保存一体机video
	 * @param machineVideo 一体机video信息
	 * @return 操作结果
	 */
	@PostMapping("/video/save")
	public CommonResult<Void> saveMachineBanner(@Validated @RequestBody MachineVideo machineVideo) {
		machineVideo.setId(SnGeneratorUtil.getId());
		mongoTemplate.save(machineVideo);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 查询一体机video列表
	 * @param search 搜索关键字
	 * @param commonPage 分页信息
	 * @return 一体机video列表
	 */
	@GetMapping("/video/list")
	public CommonResult<List<MachineVideo>> listVideo(@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Boolean enabled) {
		Query query = new Query();
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("name").regex(".*" + search + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(enabled)) {
			query.addCriteria(Criteria.where("enabled").is(enabled));
		}
		// Add sorting by id in descending order
		query.with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"));
		MongoUtil.setPageInfo(mongoTemplate, MachineVideo.class, query, commonPage);
		List<MachineVideo> machineVideos = mongoTemplate.find(query, MachineVideo.class);
		return CommonResult.successPageData(machineVideos, commonPage);
	}

	/**
	 * 查询一体机video列表 不鉴权
	 * @param search 搜索关键字
	 * @param commonPage 分页信息
	 * @return 一体机video列表
	 */
	@GetMapping("/video/list2")
	public CommonResult<List<MachineVideo>> listVideo2(@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Boolean enabled) {
		Query query = new Query();
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("name").regex(".*" + search + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(enabled)) {
			query.addCriteria(Criteria.where("enabled").is(enabled));
		}
		query.with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"));
		MongoUtil.setPageInfo(mongoTemplate, MachineVideo.class, query, commonPage);
		List<MachineVideo> machineVideos = mongoTemplate.find(query, MachineVideo.class);
		return CommonResult.successPageData(machineVideos, commonPage);
	}

	/**
	 * 批量删除一体机banner
	 * @param idsVO 包含要删除的banner ID列表
	 * @return 操作结果
	 */
	@PostMapping("/video/delete")
	public  CommonResult<Void> batchDeleteVideo(@Validated @RequestBody IdsVO idsVO) {
		List<Long> ids = idsVO.getIds();
		Query query = Query.query(Criteria.where("_id").in(ids));
		mongoTemplate.remove(query, MachineVideo.class);
		return CommonResult.successResult("操作成功");
	}


	/**
	 * 更新一体机banner
	 * @param machineBanner 一体机banner信息
	 * @return 操作结果
	 */
	@PostMapping("/banner/update")
	public CommonResult<Void> updateMachineBanner(@Validated @RequestBody MachineBanner machineBanner) {
		if (machineBanner.getId() == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
		}
		MachineBanner existingBanner = mongoTemplate.findById(machineBanner.getId(), MachineBanner.class);
		if (existingBanner == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
		}
		BeanUtil.copyProperties(machineBanner, existingBanner);
//		machineBanner.setUpdateTime(new Date());
		mongoTemplate.save(existingBanner);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 保存一体机banner
	 * @param machineBanner 一体机banner信息
	 * @return 操作结果
	 */
	@PostMapping("/banner/save")
	public CommonResult<Void> saveMachineBanner(@Validated @RequestBody MachineBanner machineBanner) {
		machineBanner.setId(SnGeneratorUtil.getId());
		mongoTemplate.save(machineBanner);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 查询一体机banner列表
	 * @param search 搜索关键字
	 * @param commonPage 分页信息
	 * @return 一体机banner列表
	 */
	@GetMapping("/banner/list")
	public CommonResult<List<MachineBanner>> list(@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Boolean enabled) {
		Query query = new Query();
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("name").regex(".*" + search + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(enabled)) {
			query.addCriteria(Criteria.where("enabled").is(enabled));
		}
		query.with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"));
		MongoUtil.setPageInfo(mongoTemplate, MachineBanner.class, query, commonPage);
		List<MachineBanner> machineBanners = mongoTemplate.find(query, MachineBanner.class);
		return CommonResult.successPageData(machineBanners, commonPage);
	}

	/**
	 * 查询一体机banner列表 不鉴权
	 * @param search 搜索关键字
	 * @param commonPage 分页信息
	 * @return 一体机banner列表
	 */
	@GetMapping("/banner/list2")
	public CommonResult<List<MachineBanner>> list2(@RequestParam(required = false) String search, CommonPage commonPage, @RequestParam(required = false) Boolean enabled) {
		Query query = new Query();
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("name").regex(".*" + search + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(enabled)) {
			query.addCriteria(Criteria.where("enabled").is(enabled));
		}
		query.with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"));
		MongoUtil.setPageInfo(mongoTemplate, MachineBanner.class, query, commonPage);
		List<MachineBanner> machineBanners = mongoTemplate.find(query, MachineBanner.class);
		return CommonResult.successPageData(machineBanners, commonPage);
	}

	/**
	 * 批量删除一体机banner
	 * @param idsVO 包含要删除的banner ID列表
	 * @return 操作结果
	 */
	@PostMapping("/banner/delete")
	public  CommonResult<Void> batchDelete(@Validated @RequestBody IdsVO idsVO) {
		List<Long> ids = idsVO.getIds();
		Query query = Query.query(Criteria.where("_id").in(ids));
		mongoTemplate.remove(query, MachineBanner.class);
		return CommonResult.successResult("操作成功");
	}



//	@PostMapping("/idcard_login")
	@ApiOperation(value = "身份证登录")
	public CommonResult<LoginToken> smsLogin(@Validated @RequestBody IdCardLoginVO loginIn){

		String encryptedStr = loginIn.getEncryptedStr();

		IdCardLoginVO.DecryptedData decryptedData;
		try {
			String data = RSAUtils.decryptDataOnJava(encryptedStr, RSAUtils.PRIVATEKEY);
			decryptedData = objectMapper.readValue(data, IdCardLoginVO.DecryptedData.class);
		} catch (Exception e) {
			throw new DataErrorException("登录数据异常");
		}

		String idCard = decryptedData.getIdCard();
		if (StrUtil.isBlank(idCard)) {
			throw new DataErrorException("登录数据异常");
		}

		//用户检查
		User user = userService.getByIdCardType(idCard, loginIn.getUserType().name());
		if (user ==null){
			throw new DataErrorException(ErrorMessage.account_not_exist);
		}else {
			if (user.getIsDisable()){
				throw new ParameterErrorException(ErrorMessage.account_is_disabled);
			}
			if (!user.getType().equals(loginIn.getUserType().name())){
				throw new DataErrorException("账号类型错误");
			}
		}

		setUserInfoCache(user);

		recordLogin(user, "一体机身份证登录");

		//设置当前登录token
		LoginToken loginToken = getLoginToken(user.getId(), loginIn.getClientId(), loginIn.getLoginType());

		return CommonResult.successData(loginToken);
	}

	private LoginToken getLoginToken(Long userId, String clientId, LoginType loginType) {
		Map<String, Object> claims = new HashMap<>();
		claims.put("userId", userId);
		claims.put("clientId", clientId);
		claims.put("loginType", loginType.name());
		LoginToken loginToken = new LoginToken();
		loginToken.setToken(JwtTokenUtils.createToken(secret, claims, expiration));
		loginToken.setGeneratedTime(String.valueOf(new Date().getTime()));
		return loginToken;
	}

	private void setUserInfoCache(User user) {
		UserInfoCache userInfoCache = new UserInfoCache();
		userInfoCache.setId(user.getId());
		userInfoCache.setAvatar(user.getAvatar());
		userInfoCache.setPhone(user.getPhone());
		userInfoCache.setRealName(user.getRealName());
		userInfoCache.setType(user.getType());
		userInfoCache.setOpenId(user.getWeixinMiniOpenId());
		userInfoCache.setOrganization(user.getOrganization());
		userInfoCache.setPosition(user.getPosition());
		userService.saveUserInfoCache(userInfoCache);
	}

	private void recordLogin(User user, String funcName) {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		String realIP = IpUtil.getRealIP(request);
		user.setLastLoginIp(realIP);
		user.setLastLoginTime(new Date());
		userService.updateById(user);

		asyncService.recordLogLogin(request.getServletPath(), user.getRealName(), user.getId(),
			user.getType(), realIP, funcName);
	}

}
