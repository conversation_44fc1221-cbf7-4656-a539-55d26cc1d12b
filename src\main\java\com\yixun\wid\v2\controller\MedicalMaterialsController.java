package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.support.ExcelTypeEnum;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.fill.FillConfig;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.*;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 待遇 医疗待遇材料相关接口
 */
@SuppressWarnings({"DuplicatedCode", "rawtypes"})
@Slf4j
@RequestMapping("/v2/medical/materials")
@Controller
public class MedicalMaterialsController {

    public static final ClassPathResource MEDICAL_TREATMENT_RESULT_RESOURCE = new ClassPathResource("file/医疗待遇审核结果.xlsx");

    public static final ClassPathResource MEDICAL_TREATMENT_DETAIL_RESOURCE = new ClassPathResource("file/医疗待遇审核明细.xlsx");

    @Value("${filePath}")
    private String filePath;

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 查询清单列表
     * @param commonPage 分页参数
     * @return 清单列表
     */
    @ResponseBody
    @GetMapping("/list")
    public CommonResult<List<MedicalMaterialsListPO>> getMaterialsList(CommonPage commonPage) {
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        MongoUtil.setPageInfo(mongoTemplate, MedicalMaterialsListPO.class, query, commonPage);
        List<MedicalMaterialsListPO> materialsListPOS = mongoTemplate.find(query, MedicalMaterialsListPO.class);
        return CommonResult.successPageData(materialsListPOS, commonPage);
    }

    /**
     * 根据条件生成清单
     * @param materialsListVO 清单信息
     * @return 操作结果
     */
    @ResponseBody
    @PostMapping("/export/list")
    public CommonResult<Void> exportMaterialsList(@RequestBody MedicalMaterialsListVO materialsListVO) {
        String materialsListType = materialsListVO.getMaterialsListType();
        Date startTime = materialsListVO.getStartTime();
        String startTimeFormat = DateUtil.format(startTime, DatePattern.PURE_DATE_PATTERN);
        Date endTime = materialsListVO.getEndTime();
        String endTimeFormat = DateUtil.format(endTime, DatePattern.PURE_DATE_PATTERN);

        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setWrapped(true);

        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setWrapped(true);

        HorizontalCellStyleStrategy styleStrategy =
            new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 待遇审核结果
        if (materialsListType.equals("待遇审核结果")) {
            Query query = new Query();
            query.addCriteria(Criteria.where("acceptDate").gte(startTime).lte(endTime));
            query.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Done));

            List<MedicalCases> medicalCases = mongoTemplate.find(query, MedicalCases.class);
            if (medicalCases.isEmpty()) {
                return CommonResult.failResult(10001, "未查询到符合条件的数据");
            }

            Long id = SnGeneratorUtil.getId();
            File file = new File(filePath + "materialsList/" + id + "-" + "待遇审核结果" + startTimeFormat + "-" + endTimeFormat + ".xlsx");
            FileUtil.touch(file);

            List<MedicalTreatmentResultExport> exports = new ArrayList<>();
            // 转换数据
            for (MedicalCases medicalCase : medicalCases) {
                // 查询账单信息
                Query billingQuery = Query.query(Criteria.where("medicalCasesId").is(medicalCase.getId()));
                billingQuery.with(Sort.by(Sort.Direction.ASC, "createTime"));
                List<BillingInfo> billingInfos = mongoTemplate.find(billingQuery, BillingInfo.class);

                // 按就诊次数(同医院同就诊方式同就诊时间)分组
                Map<String, List<BillingInfo>> treatmentMap = new HashMap<>();
                for (BillingInfo billingInfo : billingInfos) {
                    String key = billingInfo.getHospital() + "_" + billingInfo.getTreatmentType() + "_" +
                                 DateUtil.format(billingInfo.getVisitDate(), DatePattern.NORM_DATE_PATTERN);
                    if (!treatmentMap.containsKey(key)) {
                        treatmentMap.put(key, new ArrayList<>());
                    }
                    treatmentMap.get(key).add(billingInfo);
                }

                // 对每次就诊生成一条导出记录
                for (Map.Entry<String, List<BillingInfo>> entry : treatmentMap.entrySet()) {
                    MedicalTreatmentResultExport export = new MedicalTreatmentResultExport();
                    BeanUtil.copyProperties(medicalCase, export);

                    List<BillingInfo> visitBillings = entry.getValue();
                    if (!visitBillings.isEmpty()) {
                        BillingInfo firstBilling = visitBillings.get(0);
                        export.setHospital(firstBilling.getHospital());
                        export.setTreatmentType(firstBilling.getTreatmentType());
                        export.setVisitDate(firstBilling.getVisitDate());

                        // 计算就诊费用详情
                        long totalAmount = 0;
                        long totalReimbursable = 0;
                        long totalDeductible = 0;

                        for (BillingInfo billing : visitBillings) {
                            totalAmount += billing.getAmountInCent();
                            totalReimbursable += billing.getReimbursableAmountInCent();
                            totalDeductible += billing.getNonReimbursableAmountInCent();
                        }

                        export.setTotalAmountInCent(totalAmount);
                        export.setReimbursableAmountInCent(totalReimbursable);
                        export.setNonReimbursableAmountInCent(totalDeductible);
                    }

                    // 获取理算结果
                    if (medicalCase.getClaimsInformations() != null) {
                        for (Map.Entry<String, MedicalCases.ClaimsInformation> claimEntry :
                             medicalCase.getClaimsInformations().entrySet()) {
                            MedicalCases.ClaimsInformation claim = claimEntry.getValue();
                            if (claim.getClaimsResult() != null) {
                                export.setThirdPartyPayAmountInCent(claim.getClaimsResult().getThirdPartyPayAmountInCent().longValue());
                                export.setActualPayAmountInCent(claim.getClaimsResult().getActualPayAmountInCent().longValue());
                            }

                            // 根据治疗类型设置餐补信息
                            if ("HOSPITALIZATION_COST".equals(claim.getResponsibilityTreeCode()) &&
                                "住院".equals(export.getTreatmentType()) &&
                                claim.getClaimsData() != null) {
                                export.setFoodAllowanceAmountInCent(claim.getClaimsData().getFoodAllowanceAmountInCent().longValue());
                                export.setTotalPayDays(claim.getClaimsData().getTotalPayDays());
                            }
                        }
                    }

                    exports.add(export);
                }
            }

            try (ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(file), MedicalTreatmentResultExport.class)
                .needHead(false)
                .registerWriteHandler(styleStrategy)
                .withTemplate(MEDICAL_TREATMENT_RESULT_RESOURCE.getInputStream())
                .excelType(ExcelTypeEnum.XLSX).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                excelWriter.fill(exports, fillConfig, writeSheet);
                excelWriter.finish();
            } catch (IOException e) {
                log.error("导出待遇审核结果失败", e);
                return CommonResult.failResult(10002, "导出失败: " + e.getMessage());
            }

            MedicalMaterialsListPO materialsListPO = new MedicalMaterialsListPO();
            materialsListPO.setId(id);
            materialsListPO.setName("待遇审核结果" + startTimeFormat + "-" + endTimeFormat);
            materialsListPO.setStartTime(startTime);
            materialsListPO.setEndTime(endTime);
            materialsListPO.setFile("materialsList/" + id + "-" + "待遇审核结果" + startTimeFormat + "-" + endTimeFormat + ".xlsx");
            materialsListPO.setCreateTime(new Date());
            materialsListPO.setFileExpired(false);

            mongoTemplate.save(materialsListPO);
        }

        // 待遇审核明细
        if (materialsListType.equals("待遇审核明细")) {
            Query query = new Query();
            query.addCriteria(Criteria.where("acceptDate").gte(startTime).lte(endTime));
            query.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Done));

            List<MedicalCases> medicalCases = mongoTemplate.find(query, MedicalCases.class);
            if (medicalCases.isEmpty()) {
                return CommonResult.failResult(10001, "未查询到符合条件的数据");
            }

            Long id = SnGeneratorUtil.getId();
            File dir = FileUtil.mkdir(filePath + "materialsList/" + id + "-" + "待遇审核明细" + startTimeFormat + "-" + endTimeFormat);

            for (MedicalCases medicalCase : medicalCases) {
                // 创建每个职工的目录
                String workerDir = medicalCase.getWorkerName() + "_" +
                                  (medicalCase.getIdCard() != null ?
                                   medicalCase.getIdCard().substring(Math.max(0, medicalCase.getIdCard().length() - 6)) : "");
                File workerFolder = FileUtil.mkdir(new File(dir, workerDir));

                // 查询账单信息
                Query billingQuery = Query.query(Criteria.where("medicalCasesId").is(medicalCase.getId()));
                billingQuery.with(Sort.by(Sort.Direction.ASC, "createTime"));
                List<BillingInfo> billingInfos = mongoTemplate.find(billingQuery, BillingInfo.class);

                // 门诊汇总处理
                List<BillingInfo> outpatientBillings = new ArrayList<>();
                // 住院分开处理
                Map<String, List<BillingInfo>> hospitalBillings = new HashMap<>();

                for (BillingInfo billing : billingInfos) {
                    if ("门诊".equals(billing.getTreatmentType())) {
                        outpatientBillings.add(billing);
                    } else if ("住院".equals(billing.getTreatmentType())) {
                        String key = billing.getHospital() + "_" +
                                    (billing.getVisitDate() != null ?
                                     DateUtil.format(billing.getVisitDate(), DatePattern.NORM_DATE_PATTERN) : "");
                        if (!hospitalBillings.containsKey(key)) {
                            hospitalBillings.put(key, new ArrayList<>());
                        }
                        hospitalBillings.get(key).add(billing);
                    }
                }

                // 处理门诊账单
                if (!outpatientBillings.isEmpty()) {
                    processTreatmentDetails(outpatientBillings, workerFolder, medicalCase, "门诊", styleStrategy);
                }

                // 处理住院账单
                for (Map.Entry<String, List<BillingInfo>> entry : hospitalBillings.entrySet()) {
                    processTreatmentDetails(entry.getValue(), workerFolder, medicalCase, "住院", styleStrategy);
                }
            }

            File zip = ZipUtil.zip(dir);

            MedicalMaterialsListPO materialsListPO = new MedicalMaterialsListPO();
            materialsListPO.setId(id);
            materialsListPO.setName("待遇审核明细" + startTimeFormat + "-" + endTimeFormat);
            materialsListPO.setStartTime(startTime);
            materialsListPO.setEndTime(endTime);
            materialsListPO.setFile("materialsList/" + zip.getName());
            materialsListPO.setCreateTime(new Date());
            materialsListPO.setFileExpired(false);

            mongoTemplate.save(materialsListPO);

            try {
                FileUtil.del(dir);
            } catch (Exception e) {
                log.error("清理临时文件夹失败：", e);
            }
        }

        return CommonResult.successResult("操作成功");
    }

    /**
     * 处理治疗明细并导出到Excel
     *
     * @param billings 账单列表
     * @param folder 目标文件夹
     * @param medicalCase 医疗案例
     * @param treatmentType 治疗类型
     * @param styleStrategy Excel样式策略
     */
    private void processTreatmentDetails(List<BillingInfo> billings, File folder, MedicalCases medicalCase,
                                        String treatmentType, HorizontalCellStyleStrategy styleStrategy) {
        if (billings.isEmpty()) {
            return;
        }

        // 获取医院名称和就诊时间范围
        String hospital = billings.get(0).getHospital();
        Date startDate = null;
        Date endDate = null;

        for (BillingInfo billing : billings) {
            if (startDate == null || (billing.getVisitDate() != null && billing.getVisitDate().before(startDate))) {
                startDate = billing.getVisitDate();
            }

            Date visitEndDate;
            if (billing.getDischargeDate() != null) {
                visitEndDate = billing.getDischargeDate();
            } else if (billing.getVisitEndDate() != null) {
                visitEndDate = billing.getVisitEndDate();
            } else {
                visitEndDate = billing.getVisitDate();
            }

            if (endDate == null || (visitEndDate != null && visitEndDate.after(endDate))) {
                endDate = visitEndDate;
            }
        }

        // 生成文件名
        String dateRange = "";
        if (startDate != null) {
            dateRange += DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN);
        }
        if (endDate != null && !endDate.equals(startDate)) {
            dateRange += "-" + DateUtil.format(endDate, DatePattern.NORM_DATE_PATTERN);
        }

        String fileName = hospital + "_" + treatmentType + "_" + dateRange + ".xlsx";
        File file = new File(folder, fileName);

        // 准备导出数据
        List<MedicalTreatmentDetailExport> exports = new ArrayList<>();

        // 添加基本信息
        MedicalTreatmentDetailExport baseInfo = new MedicalTreatmentDetailExport();
        baseInfo.setWorkerName(medicalCase.getWorkerName());
        baseInfo.setIdCard(medicalCase.getIdCard());
        baseInfo.setOrganization(medicalCase.getOrganization());
        baseInfo.setAccidentDate(medicalCase.getAccidentDate());
        baseInfo.setHospital(hospital);
        baseInfo.setTreatmentType(treatmentType);
        baseInfo.setVisitStartDate(startDate);
        baseInfo.setVisitEndDate(endDate);

        // 获取账单明细
        Map<String, MedicalTreatmentDetailExport> categoryMap = new HashMap<>();

        for (BillingInfo billing : billings) {
            // 查询账单明细
            Query detailQuery = Query.query(Criteria.where("billingInfoId").is(billing.getId()));
            detailQuery.with(Sort.by(Sort.Direction.ASC, "orderNum"));
            List<BillingDetail> details = mongoTemplate.find(detailQuery, BillingDetail.class);

            // 根据费用类别汇总
            for (BillingDetail detail : details) {
                String category = detail.getFeeCategory();
                if (StrUtil.isBlank(category)) {
                    category = "未分类";
                }

                MedicalTreatmentDetailExport export = categoryMap.get(category);
                if (export == null) {
                    export = new MedicalTreatmentDetailExport();
                    BeanUtil.copyProperties(baseInfo, export);
                    export.setFeeCategory(category);
                    export.setTotalAmountInCent(0L);
                    export.setReimbursableAmountInCent(0L);
                    export.setNonReimbursableAmountInCent(0L);
                    categoryMap.put(category, export);
                }

                export.setTotalAmountInCent(export.getTotalAmountInCent() + detail.getAmountInCent());

                // 计算可报销和不可报销金额
                long nonReimbursable = detail.getNonReimbursableAmountInCent() != null ?
                                      detail.getNonReimbursableAmountInCent() : 0;
                export.setNonReimbursableAmountInCent(export.getNonReimbursableAmountInCent() + nonReimbursable);
                export.setReimbursableAmountInCent(export.getReimbursableAmountInCent() +
                                                  (detail.getAmountInCent() - nonReimbursable));
            }
        }

        // 添加汇总记录
        exports.addAll(categoryMap.values());

        // 获取理算结果
        if (medicalCase.getClaimsInformations() != null) {
            String responsibilityCode = "门诊".equals(treatmentType) ? "OUTPATIENT_COST" : "HOSPITALIZATION_COST";
            MedicalCases.ClaimsInformation claim = medicalCase.getClaimsInformations().get(responsibilityCode);

            if (claim != null && claim.getClaimsResult() != null) {
                baseInfo.setThirdPartyPayAmountInCent(claim.getClaimsResult().getThirdPartyPayAmountInCent().longValue());
                baseInfo.setActualPayAmountInCent(claim.getClaimsResult().getActualPayAmountInCent().longValue());

                // 添加住院伙食补助
                if ("住院".equals(treatmentType) && claim.getClaimsData() != null) {
                    baseInfo.setFoodAllowanceAmountInCent(claim.getClaimsData().getFoodAllowanceAmountInCent().longValue());
                    baseInfo.setTotalPayDays(claim.getClaimsData().getTotalPayDays());
                }
            }
        }

        // 写入Excel
        try (ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(file), MedicalTreatmentDetailExport.class)
            .needHead(false)
            .registerWriteHandler(styleStrategy)
            .withTemplate(MEDICAL_TREATMENT_DETAIL_RESOURCE.getInputStream())
            .excelType(ExcelTypeEnum.XLSX).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(exports, fillConfig, writeSheet);
            excelWriter.fill(baseInfo, fillConfig, writeSheet);
            excelWriter.finish();
        } catch (IOException e) {
            log.error("导出待遇审核明细失败", e);
        }
    }
}
