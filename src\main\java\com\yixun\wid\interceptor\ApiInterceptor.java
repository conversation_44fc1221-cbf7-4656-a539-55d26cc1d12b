package com.yixun.wid.interceptor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ApiInterceptor - Api调用权限
 */
@Component
public class ApiInterceptor extends HandlerInterceptorAdapter {

	/** "token"属性名称 */
	private static final String TOKEN_NAME = "ApiToken";

	@Value("${api.token}")
	private String apiToken;

	@Override
	public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {

		String tokenValue = request.getHeader(TOKEN_NAME);
		if (!StringUtils.hasLength(tokenValue)){
			throw new RuntimeException("ApiToken为空");
		}

		if (apiToken.equals(tokenValue)){
			return true;
		}
//        try {
//			String userName = Jwts.parser().setSigningKey("QXBpVG9rZW5TZWNyZXQ=").parseClaimsJws(tokenValue).getBody().getSubject();
//		}catch (Exception e){
//            response.getWriter().write(e.getMessage());
//			return false;
//		}

		throw new RuntimeException("ApiToken不正确");
    }

	@Override
	public void postHandle(HttpServletRequest request,
                           HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
	}

}