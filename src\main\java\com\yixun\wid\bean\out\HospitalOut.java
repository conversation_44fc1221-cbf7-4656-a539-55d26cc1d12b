package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class HospitalOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @ApiModelProperty(value = "医院名称")
    private String name;

    @ApiModelProperty(value = "医院简介")
    private String brief;

    @ApiModelProperty(value = "医院等级")
    private String grade;

    @ApiModelProperty(value = "省级区域")
    private String province;

    @ApiModelProperty(value = "市级区域")
    private String city;

    @ApiModelProperty(value = "区级区域")
    private String district;

    @ApiModelProperty(value = "医院地址")
    private String address;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;
}
