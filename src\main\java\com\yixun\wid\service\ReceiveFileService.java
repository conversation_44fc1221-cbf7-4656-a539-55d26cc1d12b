package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.ReceiveFileGetIn;
import com.yixun.wid.entity.ReceiveFile;

import java.util.List;

public interface ReceiveFileService {

    ReceiveFile getByDeclaration(Long declarationId, String type);

    void save(ReceiveFile receiveFile);

    void update(ReceiveFile receiveFile);

    List<ReceiveFile> getList(ReceiveFileGetIn receiveFileGetIn, CommonPage commonPage);

    ReceiveFile getById(Long supplementaryFileId);
}
