package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.aop.RequiredPermission;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.StaffGroupIn;
import com.yixun.wid.bean.in.StaffGroupListIn;
import com.yixun.wid.bean.in.StaffGroupSearchIn;
import com.yixun.wid.bean.out.GroupStaffOut;
import com.yixun.wid.bean.out.StaffGroupOut;
import com.yixun.wid.entity.StaffGroup;
import com.yixun.wid.entity.StaffGroupAdministrator;
import com.yixun.wid.entity.StaffGroupMap;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.StaffGroupService;
import com.yixun.wid.utils.BeanFieldCheckingUtils;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.utils.TreeListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "admin人员分组")
@RestController
@RequestMapping("/admin/staffGroup")
public class AdminStaffGroupController {

    @Resource
    private StaffGroupService staffGroupService;

    @RequiredPermission("get:getSubGroupList:staffGroup")
    @ApiOperation(value = "获取下级组列表")
    @GetMapping(value = "/getSubGroupList")
    public CommonResult<List<StaffGroupOut>> getSubGroupList(@RequestParam Long parentId) {

        List<StaffGroup> staffGroupList = staffGroupService.getListByParentId(parentId);
        List<StaffGroupOut> outs = BeanUtils.copyToOutList(staffGroupList, StaffGroupOut.class);

        return CommonResult.successData(outs);
    }

    @RequiredPermission("get:getAllGroupList:staffGroup")
    @ApiOperation(value = "获取所有组列表")
    @GetMapping(value = "/getAllGroupList")
    public CommonResult<List<StaffGroupMap>> getAllGroupList() {

        List<StaffGroup> staffGroupList = staffGroupService.getAllGroupList();
        List<StaffGroupMap> staffGroupMapList = BeanUtils.copyToOutList(staffGroupList, StaffGroupMap.class);
        List<StaffGroupMap> outList = TreeListUtil.staffGroupTreeList(staffGroupMapList, 0L, "根节点");

        return CommonResult.successData(outList);
    }

    @RequiredPermission("get:getAllGroupListBySearch:staffGroup")
    @ApiOperation(value = "根据查询条件获取所有组列表")
    @PostMapping(value = "/getAllGroupListBySearch")
    public CommonResult<List<StaffGroupMap>> getAllGroupListBySearch(@RequestBody StaffGroupSearchIn staffGroupSearchIn) {

        List<StaffGroup> staffGroupList = staffGroupService.getAllGroupListBySearch(staffGroupSearchIn);
        List<StaffGroupMap> staffGroupMapList = BeanUtils.copyToOutList(staffGroupList, StaffGroupMap.class);

        return CommonResult.successData(staffGroupMapList);
    }

    @RequiredPermission("save:doSave:staffGroup")
    @ApiOperation(value = "保存组信息")
    @PostMapping(value = "/doSave")
    public CommonResult doSave(@RequestBody StaffGroupIn staffGroupIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(staffGroupIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        if (staffGroupIn.getParentId() != 0) {
            StaffGroup data = staffGroupService.getById(staffGroupIn.getParentId());
            if (data == null) {
                throw new DataErrorException("未找到父组信息");
            }
        }

        StaffGroup staffGroup = new StaffGroup();
        staffGroup.setId(SnGeneratorUtil.getId());
        staffGroup.setIsDel(false);
        staffGroup.setCreateDate(new Date());
        BeanUtils.copyProperties(staffGroupIn, staffGroup);

        staffGroupService.insert(staffGroup);

        return CommonResult.successResult("保存成功");
    }

    @RequiredPermission("update:doEdit:staffGroup")
    @ApiOperation(value = "修改组信息")
    @PostMapping(value = "/doEdit/{staffGroupId}")
    public CommonResult doEdit(@PathVariable("staffGroupId") Long staffGroupId, @RequestBody StaffGroupIn staffGroupIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAnyFieldNotNull(staffGroupIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        StaffGroup staffGroup = staffGroupService.getById(staffGroupId);
        if (staffGroup == null) {
            throw new DataErrorException("未找到该组信息");
        }

        if (staffGroupIn.getParentId() != null && staffGroupIn.getParentId() != 0) {
            StaffGroup data = staffGroupService.getById(staffGroupIn.getParentId());
            if (data == null) {
                throw new DataErrorException("未找到父组信息");
            }
        }

        BeanUtils.copyProperties(staffGroupIn, staffGroup, true);

        staffGroupService.update(staffGroup);

        return CommonResult.successResult("修改成功");
    }

    @RequiredPermission("delete:doDelete:staffGroup")
    @ApiOperation(value = "删除组信息")
    @PostMapping(value = "/doDelete/{staffGroupId}")
    public CommonResult doDelete(@PathVariable("staffGroupId") Long staffGroupId) {

        StaffGroup staffGroup = staffGroupService.getById(staffGroupId);
        if (staffGroup == null || staffGroup.getIsDel()) {
            throw new DataErrorException("未找到组信息");
        }

        List<StaffGroup> staffGroupList = staffGroupService.getListByParentId(staffGroupId);
        if (!staffGroupList.isEmpty()) {
            throw new DataErrorException("该组还有下级组，不能删除");
        }

        List<StaffGroupAdministrator> staffInGroupList = staffGroupService.getStaffInGroupList(staffGroupId);
        if (!staffInGroupList.isEmpty()) {
            throw new DataErrorException("该组还有人员，不能删除");
        }

        staffGroupService.delete(staffGroup);

        return CommonResult.successResult("删除成功");
    }

    @RequiredPermission("get:getGroupStaffList:staffGroup")
    @ApiOperation(value = "获取组的人员列表")
    @GetMapping(value = "/getGroupStaffList")
    public CommonResult<List<GroupStaffOut>> getGroupStaffList(@RequestParam Long staffGroupId) {

        List<GroupStaffOut> groupStaffOuts = staffGroupService.getGroupStaffList(staffGroupId);

        return CommonResult.successData(groupStaffOuts);
    }

    @RequiredPermission("get:getGroupAllStaffList:staffGroup")
    @ApiOperation(value = "获取组的全部人员列表（包括下级组）")
    @GetMapping(value = "/getGroupAllStaffList")
    public CommonResult<List<GroupStaffOut>> getGroupAllStaffList(@RequestParam Long staffGroupId) {

        //获取当前组的组员
        List<GroupStaffOut> outs = staffGroupService.getGroupStaffList(staffGroupId);
        //获取所有下级组的组员
        List<GroupStaffOut> groupStaffOuts = staffGroupService.getAllGroupStaffList();
        TreeListUtil.groupStaffTreeList(outs, groupStaffOuts, staffGroupId);

        return CommonResult.successData(outs);
    }

    @RequiredPermission("save:addStaffGroup:staffGroup")
    @ApiOperation(value = "给组添加组员")
    @PostMapping(value = "/addStaffGroup")
    public CommonResult addStaffGroup(@RequestBody StaffGroupListIn staffGroupIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(staffGroupIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        staffGroupService.addStaffGroup(staffGroupIn.getStaffGroupId(), staffGroupIn.getAdministratorIdList());

        return CommonResult.successResult("添加成功");
    }

    @RequiredPermission("delete:deleteStaffGroup:staffGroup")
    @ApiOperation(value = "给组删除组员")
    @PostMapping(value = "/deleteStaffGroup")
    public CommonResult deleteStaffGroup(@RequestBody StaffGroupListIn staffGroupIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(staffGroupIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        staffGroupService.deleteStaffGroup(staffGroupIn.getStaffGroupId(), staffGroupIn.getAdministratorIdList());

        return CommonResult.successResult("删除成功");
    }

}
