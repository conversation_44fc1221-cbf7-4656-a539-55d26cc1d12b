package com.yixun.wid.v2.vo.ai;

import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * SurgicalInformationVO测试类
 */
public class SurgicalInformationVOTest {

    @Test
    public void testSurgicalInformationVOCreation() {
        // 测试创建SurgicalInformationVO对象
        SurgicalInformationVO vo = new SurgicalInformationVO();
        
        // 设置测试数据
        vo.setId(12345L);
        vo.setSurgicalNames(Arrays.asList("阑尾切除术", "胆囊切除术", "疝气修补术"));

        // 验证对象创建成功
        assert vo.getId().equals(12345L);
        assert vo.getSurgicalNames().size() == 3;
        assert vo.getSurgicalNames().contains("阑尾切除术");
        assert vo.getSurgicalNames().contains("胆囊切除术");
        assert vo.getSurgicalNames().contains("疝气修补术");
        
        System.out.println("SurgicalInformationVO对象创建测试通过");
    }

    @Test
    public void testSurgicalInformationVOWithNullValues() {
        // 测试创建空的SurgicalInformationVO对象
        SurgicalInformationVO vo = new SurgicalInformationVO();
        
        // 验证默认值
        assert vo.getId() == null;
        assert vo.getSurgicalNames() == null;
        
        System.out.println("SurgicalInformationVO空值测试通过");
    }

    @Test
    public void testSurgicalInformationVOPartialData() {
        // 测试部分数据设置
        SurgicalInformationVO vo = new SurgicalInformationVO();
        
        // 只设置手术名称列表
        vo.setSurgicalNames(Arrays.asList("心脏搭桥手术"));
        
        // 验证设置的字段
        assert vo.getSurgicalNames().size() == 1;
        assert vo.getSurgicalNames().contains("心脏搭桥手术");
        
        // 验证未设置的字段为null
        assert vo.getId() == null;
        
        System.out.println("SurgicalInformationVO部分数据测试通过");
    }

    @Test
    public void testSurgicalInformationVOWithEmptyList() {
        // 测试空手术名称列表
        SurgicalInformationVO vo = new SurgicalInformationVO();
        
        // 设置空列表
        vo.setId(99999L);
        vo.setSurgicalNames(Arrays.asList());
        
        // 验证
        assert vo.getId().equals(99999L);
        assert vo.getSurgicalNames() != null;
        assert vo.getSurgicalNames().isEmpty();
        
        System.out.println("SurgicalInformationVO空列表测试通过");
    }

    @Test
    public void testSurgicalInformationVOBusinessScenarios() {
        // 测试实际业务场景
        System.out.println("=== 业务场景测试 ===");
        
        // 场景1：单个手术
        SurgicalInformationVO singleSurgery = new SurgicalInformationVO();
        singleSurgery.setId(1001L);
        singleSurgery.setSurgicalNames(Arrays.asList("阑尾切除术"));
        
        assert singleSurgery.getId().equals(1001L);
        assert singleSurgery.getSurgicalNames().size() == 1;
        System.out.println("场景1通过：单个手术 - " + singleSurgery.getSurgicalNames().get(0));
        
        // 场景2：多个手术
        SurgicalInformationVO multipleSurgeries = new SurgicalInformationVO();
        multipleSurgeries.setId(1002L);
        multipleSurgeries.setSurgicalNames(Arrays.asList(
            "胆囊切除术", 
            "疝气修补术", 
            "阑尾切除术"
        ));
        
        assert multipleSurgeries.getId().equals(1002L);
        assert multipleSurgeries.getSurgicalNames().size() == 3;
        System.out.println("场景2通过：多个手术 - " + multipleSurgeries.getSurgicalNames());
        
        // 场景3：复杂手术名称
        SurgicalInformationVO complexSurgery = new SurgicalInformationVO();
        complexSurgery.setId(1003L);
        complexSurgery.setSurgicalNames(Arrays.asList(
            "腹腔镜下胆囊切除术", 
            "经皮冠状动脉介入治疗术（PCI）",
            "全膝关节置换术"
        ));
        
        assert complexSurgery.getId().equals(1003L);
        assert complexSurgery.getSurgicalNames().size() == 3;
        System.out.println("场景3通过：复杂手术名称");
        complexSurgery.getSurgicalNames().forEach(surgery -> 
            System.out.println("  - " + surgery)
        );
        
        // 场景4：无案件ID的情况（直接传入request对象）
        SurgicalInformationVO noIdSurgery = new SurgicalInformationVO();
        noIdSurgery.setSurgicalNames(Arrays.asList("骨折内固定术"));
        
        assert noIdSurgery.getId() == null;
        assert noIdSurgery.getSurgicalNames().size() == 1;
        System.out.println("场景4通过：无案件ID的情况 - " + noIdSurgery.getSurgicalNames().get(0));
    }
}
