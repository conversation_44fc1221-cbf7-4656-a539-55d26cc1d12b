package com.yixun.wid.config;

import com.yixun.wid.interceptor.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private ApiInterceptor apiInterceptor;

    @Resource
    private AdminInterceptor adminInterceptor;

    @Resource
    private AdminApiInterceptor adminApiInterceptor;

	@Resource
	private AdminAndUserInterceptor adminAndUserInterceptor;

    //拦截器配置
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(new ParametersInterceptor()).addPathPatterns("/**");
//        if (!env.equals("dev")){
	        registry.addInterceptor(adminAndUserInterceptor).addPathPatterns("/v2/**")
	        .excludePathPatterns("/v2/machine/video/list2", "/v2/machine/banner/list2", "/v2/biz/item/list2", "/v2/biz/type/list2", "/v2/biz/item/getById2");
	        registry.addInterceptor(apiInterceptor).addPathPatterns("/api/**");
	        registry.addInterceptor(adminInterceptor).addPathPatterns("/admin/**")
		        .excludePathPatterns("/admin/hospital/getList2");
	        registry.addInterceptor(adminApiInterceptor).addPathPatterns("/adminApi/**");
//        }
    }
}
