package com.yixun.wid.utils;

import org.springframework.data.redis.core.RedisTemplate;

import java.util.Calendar;

public class SnGeneratorUtil {

    private static SnowFlake sf = new SnowFlake(2, 2);

    public static Long getId(){
        return sf.nextId();
    }

    public static String getSiChuanCaseSn(RedisTemplate redisTemplate, Boolean isCity, String type) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        Long sn = redisTemplate.opsForValue().increment("CaseSn:" + type + year);
        if (isCity!=null && isCity){
            return "[" + year + "]" + "川01-04" + type + sn+"号";
        }else {
            return "[" + year + "]" + "川0104" + type + sn+"号";
        }
    }

	public static String getSiChuanCaseSnV2(RedisTemplate redisTemplate, String type, String insuranceAddress) {
		Calendar calendar = Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		Long sn = redisTemplate.opsForValue().increment("CaseSn:" + type + year);
		if (insuranceAddress != null && insuranceAddress.equals("市辖区")){
			return "[" + year + "]" + "川01-04" + type + sn+"号";
		}else {
			return "[" + year + "]" + "川0104" + type + sn+"号";
		}
//		return "[" + year + "]" + "川0104（区）/01-04（市）" + type + sn+"号";
	}

}
