package com.yixun.wid.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.api.ApiSnApi;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CasesGetIn;
import com.yixun.wid.bean.in.CasesIn;
import com.yixun.wid.bean.in.MaterialsIn;
import com.yixun.wid.bean.out.CasesLogOut;
import com.yixun.wid.bean.out.CasesOut;
import com.yixun.wid.bean.out.RelateCasesOut;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.CasesLog;
import com.yixun.wid.entity.em.CasesStatus;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin用户报案")
@RestController
@RequestMapping(value = "/admin/cases")
public class AdminCasesController {

    @Resource
    private CasesService casesService;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private ApiSnApi apiSnApi;

	@Resource(name="customRedisTemplate")
	private RedisTemplate redisTemplate;

    @GetMapping("/test")
    @ApiOperation("test")
    public CommonResult<String> test() {

        CommonResult<String> caseSn = apiSnApi.getCaseSn();
        return caseSn;
    }

    @GetMapping("/getList")
    @ApiOperation("获取报案列表")
    public CommonResult<List<CasesOut>> getList(CasesGetIn casesGetIn, CommonPage commonPage) {

        List<Cases> casesList = casesService.getCasesList(casesGetIn, commonPage);
        List<CasesOut> outList = BeanUtils.copyToOutList(casesList, CasesOut.class);
        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/getLogList")
    @ApiOperation("获取报案操作日志")
    public CommonResult<List<CasesLogOut>> getLogList(Long casesId, CommonPage commonPage) {

        List<CasesLog> casesList = casesService.getCasesLogList(casesId, commonPage);
        List<CasesLogOut> outList = BeanUtils.copyToOutList(casesList, CasesLogOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/checkUserAccidentDate")
    @ApiOperation("获取用户事故日期")
    public CommonResult<List<String>> checkUserAccidentDate(Long id, @RequestParam String idCard,
                                                            @RequestParam Date accidentTime) {

        List<Cases> userCases = casesService.getByUserAccidentDate(id, idCard, accidentTime);
        List<String> names = userCases.stream().map(Cases::getName).distinct().collect(Collectors.toList());

        return CommonResult.successData(names);
    }

    @GetMapping("/get/{casesId}")
    @ApiOperation("获取报案详情")
    public CommonResult<CasesOut> getDetail(@PathVariable("casesId") Long casesId) {

        Cases cases = casesService.getById(casesId);
        if (cases == null) {
            throw new DataErrorException("该报案不存在");
        }
        CasesOut out = new CasesOut();
        BeanUtils.copyProperties(cases, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/save")
    @ApiOperation("保存报案")
    public CommonResult<Long> save(@RequestBody CasesIn casesIn) {

        if (!casesIn.getIsOccupDiseaseRelated()) {
            List<Cases> userCases = casesService.getByUserAccidentDate(null, casesIn.getIdCard(),
                    casesIn.getAccidentTime());
            if (!userCases.isEmpty()) {
                Cases c = userCases.get(0);
                throw new DataErrorException("职工" + c.getName() + "于"
                        + new SimpleDateFormat("yyyy年MM月dd日").format(c.getAccidentTime()) + "的工伤事故已报备");
            }
        }

        Cases cases = new Cases();
        cases.setId(SnGeneratorUtil.getId());
        cases.setCreateTime(new Date());
        BeanUtils.copyProperties(casesIn, cases);
        cases.setStatus(casesIn.getStatus().name());

//        cases.setCaseSn(casesService.getCaseSn());
	    cases.setCaseSn(SnGeneratorUtil.getSiChuanCaseSnV2(redisTemplate, "工", cases.getInsuranceAddress()));

        casesService.save(cases);

        casesService.setCasesLog(cases.getId(), "历史录入");

        return CommonResult.successData(cases.getId());
    }

    @PostMapping("/update/{casesId}")
    @ApiOperation("更新报案信息")
    public CommonResult<Long> update(@PathVariable("casesId") Long casesId, @RequestBody CasesIn casesIn) {

        Cases cases = casesService.getById(casesId);
        if (cases == null) {
            throw new DataErrorException("报案信息不存在");
        }
        BeanUtils.copyProperties(casesIn, cases, true);
        if (cases.getStatus().equals(CasesStatus.Applied.name())) {
            throw new DataErrorException("该报案已申报，不可修改");
        }
        if (!casesIn.getIsOccupDiseaseRelated()) {
            List<Cases> userCases = casesService.getByUserAccidentDate(casesId, casesIn.getIdCard(),
                    casesIn.getAccidentTime());
            if (!userCases.isEmpty()) {
                Cases c = userCases.get(0);
                throw new DataErrorException("职工" + c.getName() + "于"
                        + new SimpleDateFormat("yyyy年MM月dd日").format(c.getAccidentTime()) + "的工伤事故已报备");
            }
        }
        if (casesIn.getStatus() != null) {
            cases.setStatus(casesIn.getStatus().name());
        }

        // bugfix: 案件的申报信息的申请时间应记录提交的时间（即申报中的案件点击提交后），且申请时间可以手动修改
        String status = cases.getStatus();
        Date submitTime = cases.getSubmitTime();
        if (ObjectUtil.isNull(submitTime) && StrUtil.isNotBlank(status) && status.equals(CasesStatus.Submitted.name())) {
            cases.setSubmitTime(new Date());
        }

        casesService.update(cases);

        casesService.setCasesLog(cases.getId(), "历史修改");

        return CommonResult.successData(cases.getId());
    }

    @PostMapping("/updateMaterials/{casesId}")
    @ApiOperation("更新报案材料")
    public CommonResult<Long> updateMaterials(@PathVariable("casesId") Long casesId, @RequestBody MaterialsIn materialsIn) {

        Cases cases = casesService.getById(casesId);
        if (cases == null) {
            throw new DataErrorException("报案信息不存在");
        }
        BeanUtils.copyProperties(materialsIn, cases, true);
        if (cases.getStatus().equals(CasesStatus.Applied.name())) {
            throw new DataErrorException("该报案已申报，不可提交");
        }

        casesService.update(cases);

        casesService.setCasesLog(cases.getId(), "历史上传");

        return CommonResult.successData(cases.getId());
    }

    @PostMapping("/testSendMs")
    @ApiOperation("测试订阅消息发送")
    public CommonResult<String> testSendMs() {

        casesService.sendOver15DayMs();
        return CommonResult.successData("");
    }

    @GetMapping("/getRelateCase")
    @ApiOperation("获取关联事故数据")
    public CommonResult<List<RelateCasesOut>> getRelateCase(Long casesId) {
        List<RelateCasesOut> relateCase = casesService.getRelateCase(casesId);
        return CommonResult.successData(relateCase);
    }

}
