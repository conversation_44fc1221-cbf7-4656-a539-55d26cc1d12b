package com.yixun.wid.utils;

import com.yixun.wid.exception.DataErrorException;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

public class LoginLockoutUtil {
    private static final int MAX_FAILED_ATTEMPTS = 5;
    private static final long LOCKOUT_DURATION = 10; // 10 minutes
    private static final String LOCKOUT_KEY = "login_lockout:";

    public static void loginCheck(RedisTemplate redis, String username, boolean passwordCorrect) {

        String redisKey = LOCKOUT_KEY + username;

        if (redis.getExpire(redisKey, TimeUnit.MILLISECONDS) > 0) {
            long lockoutRemaining = redis.getExpire(redisKey, TimeUnit.MINUTES) + 1;
            throw new DataErrorException("账号已被锁定," + lockoutRemaining + "分钟后可再次登录");
        }

        if (passwordCorrect) {
            redis.delete(redisKey);
            return;
        }

        long failedAttempts = redis.opsForValue().increment(redisKey);
        if (failedAttempts < MAX_FAILED_ATTEMPTS) {
            throw new DataErrorException("登录失败,你还有" + (MAX_FAILED_ATTEMPTS - failedAttempts) + "次机会");
        }

        redis.expire(redisKey, LOCKOUT_DURATION, TimeUnit.MINUTES);
        throw new DataErrorException("登录错误过多,账号已被锁定10分钟");

    }
}
