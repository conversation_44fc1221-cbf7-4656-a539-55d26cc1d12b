package com.yixun.wid.aop;

import com.alibaba.fastjson.JSON;
import com.yixun.wid.service.AsyncService;
import com.yixun.wid.utils.AdminUserHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
public class PostInJsonParametersAop {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private AsyncService asyncService;

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void postInJsonParameters(){}

    @Around("postInJsonParameters()")
    public Object postExec(ProceedingJoinPoint invocation) throws Throwable {
        return getPostInJsonParameters(invocation);
    }

    private Object getPostInJsonParameters(ProceedingJoinPoint invocation) throws Throwable {
        //记录所有的修改日志到数据库
	    try {
		    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		    String servletPath = request.getServletPath();

			if (servletPath.contains("v2/ocr/general")) {
				return invocation.proceed();
			}

		    logger.info("参数："+ JSON.toJSONString(invocation.getArgs()));
		    if (servletPath.startsWith("/admin/")){
                Long userId = AdminUserHelper.getCurrentUserId();
                asyncService.recordLogAdmin(invocation, servletPath, userId);
            }

        }catch (Exception e){
            logger.error("参数log报错出错: " + e.getMessage());
        }

        return invocation.proceed();
    }

}
