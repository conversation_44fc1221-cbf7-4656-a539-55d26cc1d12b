package com.yixun.wid.v2.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.v2.entity.MedicalCasesLog;

import java.util.List;

/**
 * 工伤待遇业务操作日志服务接口
 */
public interface MedicalCasesLogService {
    
    /**
     * 新增操作日志
     *
     * @param medicalCasesLog 操作日志信息
     * @return 新增后的操作日志
     */
    MedicalCasesLog add(MedicalCasesLog medicalCasesLog);
    
    /**
     * 批量新增操作日志
     *
     * @param medicalCasesLogs 操作日志列表
     * @return 新增后的操作日志列表
     */
    List<MedicalCasesLog> batchAdd(List<MedicalCasesLog> medicalCasesLogs);
    
    /**
     * 更新操作日志
     *
     * @param medicalCasesLog 操作日志信息
     * @return 更新后的操作日志
     */
    MedicalCasesLog update(MedicalCasesLog medicalCasesLog);
    
    /**
     * 根据ID删除操作日志
     *
     * @param id 操作日志ID
     * @return 删除的记录数
     */
    Long delete(Long id);
    
    /**
     * 批量删除操作日志
     *
     * @param ids 操作日志ID列表
     * @return 删除的记录数
     */
    Long batchDelete(List<Long> ids);
    
    /**
     * 根据ID获取操作日志
     *
     * @param id 操作日志ID
     * @return 操作日志详情
     */
    MedicalCasesLog getById(Long id);
    
    /**
     * 根据业务ID查询操作日志列表
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @param commonPage 分页参数
     * @return 操作日志列表（分页）
     */
    List<MedicalCasesLog> listByMedicalCasesId(Long medicalCasesId, CommonPage commonPage);
    
    /**
     * 根据条件查询操作日志列表
     *
     * @param medicalCasesId 工伤待遇业务ID（可选）
     * @param status 状态（可选）
     * @param userId 录入用户ID（可选）
     * @param commonPage 分页参数
     * @return 操作日志列表（分页）
     */
    List<MedicalCasesLog> list(Long medicalCasesId, String status, String userId, CommonPage commonPage);
    
    /**
     * 根据业务ID查询操作日志列表（不分页）
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @return 操作日志列表（不分页）
     */
    List<MedicalCasesLog> listAllByMedicalCasesId(Long medicalCasesId);
} 