package com.yixun.wid.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yixun.wid.entity.CorpInfo;
import com.yixun.wid.entity.CorpInfoDetail;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CorpInfoService;
import com.yixun.wid.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CorpInfoServiceImpl implements CorpInfoService {

    @Resource(name="customRedisTemplate")
    private RedisTemplate redisTemplate;

	//企业详情 带联系人信息 818
	private String corpDetailUrl = "http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0";

    private String corpSearchUrl = "https://open.api.tianyancha.com/services/open/search/2.0";
    private String authorization = "9bdee227-d2e8-4f1f-a6ef-c25187c04875";

    @Override
    public List<CorpInfo.ItemsBean> getCorpInfoList(String name) {
        String corpRedisKey = "CorpInfoKey:" + name;
        List<CorpInfo.ItemsBean> corpList = redisTemplate.opsForList().range(corpRedisKey, 0, -1);
        if (corpList==null || corpList.isEmpty()){
            Map<String, String> params = new HashMap<>();
            params.put("word", name);
            try {
                JSONObject connGet = OkHttpKit.getWithAuthorization(corpSearchUrl,
                        params, authorization);
                if (connGet.containsKey("error_code") && connGet.get("error_code").equals(0)) {
                    CorpInfo corpInfo = connGet.getJSONObject("result").toJavaObject(CorpInfo.class);
                    corpList = corpInfo.getItems();
                    redisTemplate.opsForList().rightPushAll(corpRedisKey, corpList);
                } else {
                    throw new DataErrorException(connGet.getString("reason"));
                }
            } catch (IOException e) {
                log.error(e.getMessage());
                throw new DataErrorException(e.getMessage());
            }
        }

        return corpList;
    }

	/**
	 * 获取天眼查公司详情接口
	 * @param name 公司名称、公司id、注册号或社会统一信用代码
	 * @return 公司详情
	 */
	@Override
	public CorpInfoDetail getCorpInfoDetail(String name) {
		String corpDetailRedisKey = "CorpInfoDetailKey:" + name;
		CorpInfoDetail corpInfoDetail = (CorpInfoDetail) redisTemplate.opsForValue().get(corpDetailRedisKey);
		if (corpInfoDetail==null){
			Map<String, String> params = new HashMap<>();
			//搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
			params.put("keyword", name);
			try {
				JSONObject connGet = OkHttpKit.getWithAuthorization(corpDetailUrl,
					params, authorization);
				if (connGet.containsKey("error_code") && connGet.get("error_code").equals(0)) {
					corpInfoDetail =  connGet.getJSONObject("result").toJavaObject(CorpInfoDetail.class);
					redisTemplate.opsForValue().set(corpDetailRedisKey, corpInfoDetail);
				} else {
					throw new DataErrorException(connGet.getString("reason"));
				}
			} catch (Exception e) {
				log.error("天眼查 查询企业["+name+"]："+e.getMessage());
				throw new DataErrorException("天眼查 查询企业["+name+"]："+e.getMessage());
			}
		}

		return corpInfoDetail;
	}

}
