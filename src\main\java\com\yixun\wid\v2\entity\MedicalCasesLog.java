package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 工伤待遇业务操作日志
 */
@Data
@Document("medicalCasesLog")
public class MedicalCasesLog {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工伤待遇业务id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medicalCasesId;

    /**
     * 状态
     */
    private String status;

    /**
     * 录入用户id
     */
    private String userId;

    /**
     * 录入用户名称
     */
    private String userName;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;
} 