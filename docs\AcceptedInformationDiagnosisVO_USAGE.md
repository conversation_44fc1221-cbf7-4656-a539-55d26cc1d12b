# AcceptedInformationDiagnosisVO 使用说明

## 概述

`AcceptedInformationDiagnosisVO` 是专门为受理信息和临床诊断信息识别接口设计的响应对象。它替代了原来直接返回 `MedicalCases` 对象的方式，提供了更加简洁和安全的数据结构。

## 设计目的

1. **数据安全**: 避免暴露完整的 `MedicalCases` 对象中的敏感信息
2. **接口简洁**: 只返回AI识别相关的字段，减少不必要的数据传输
3. **职责分离**: 将AI识别结果与业务实体分离，提高代码的可维护性

## 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | `Long` | 案件ID（如果是基于现有案件的识别） |
| `accidentDate` | `Date` | 事故日期 |
| `workerName` | `String` | 职工姓名 |
| `gender` | `String` | 性别 |
| `idCard` | `String` | 身份证号码 |
| `organization` | `String` | 用人单位名称 |
| `injuryDiagnoses` | `List<String>` | 工伤诊断列表（临床诊断） |

## 使用示例

### 1. 基于案件ID的调用

```java
// 调用接口
CommonResult<AcceptedInformationDiagnosisVO> result = 
    medicalCasesController.acceptedInformationDiagnosis(12345L, null);

if (result.isSuccess()) {
    AcceptedInformationDiagnosisVO vo = result.getData();
    
    // 获取识别结果
    System.out.println("案件ID: " + vo.getId());
    System.out.println("职工姓名: " + vo.getWorkerName());
    System.out.println("性别: " + vo.getGender());
    System.out.println("身份证号: " + vo.getIdCard());
    System.out.println("用人单位: " + vo.getOrganization());
    System.out.println("事故日期: " + vo.getAccidentDate());
    System.out.println("工伤诊断: " + vo.getInjuryDiagnoses());
}
```

### 2. 直接传入请求对象的调用

```java
// 构建请求对象
AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();
// ... 设置请求参数

// 调用接口
CommonResult<AcceptedInformationDiagnosisVO> result = 
    medicalCasesController.acceptedInformationDiagnosis(null, request);

if (result.isSuccess()) {
    AcceptedInformationDiagnosisVO vo = result.getData();
    // 处理识别结果
}
```

### 3. 前端JavaScript示例

```javascript
// 基于案件ID调用
fetch('/v2/medical/cases/accepted-information-diagnosis?id=12345', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        const vo = data.data;
        console.log('识别结果:', vo);
        
        // 显示识别结果
        document.getElementById('workerName').value = vo.workerName || '';
        document.getElementById('gender').value = vo.gender || '';
        document.getElementById('idCard').value = vo.idCard || '';
        document.getElementById('organization').value = vo.organization || '';
        
        if (vo.accidentDate) {
            document.getElementById('accidentDate').value = vo.accidentDate;
        }
        
        if (vo.injuryDiagnoses && vo.injuryDiagnoses.length > 0) {
            document.getElementById('diagnoses').value = vo.injuryDiagnoses.join(', ');
        }
    }
});
```

## 与 MedicalCases 的对比

| 特性 | AcceptedInformationDiagnosisVO | MedicalCases |
|------|--------------------------------|--------------|
| 字段数量 | 7个核心字段 | 50+个字段 |
| 数据安全性 | 高（只包含必要字段） | 低（包含所有业务数据） |
| 传输效率 | 高（数据量小） | 低（数据量大） |
| 维护性 | 高（专用结构） | 中（通用实体） |

## 注意事项

1. **只读性质**: 此VO对象仅用于展示AI识别结果，不会自动保存到数据库
2. **字段可空**: 所有字段都可能为空，前端需要做好空值处理
3. **日期格式**: `accidentDate` 字段使用 `yyyy-MM-dd` 格式
4. **诊断列表**: `injuryDiagnoses` 可能包含多个诊断结果

## 最佳实践

1. **错误处理**: 始终检查接口返回的 `success` 状态
2. **空值检查**: 在使用字段值之前进行空值检查
3. **用户确认**: 将AI识别结果展示给用户确认后再保存
4. **日志记录**: 记录AI识别的调用和结果，便于问题排查

## 迁移指南

如果您之前使用的是返回 `MedicalCases` 对象的版本，请按以下步骤迁移：

1. 更新接口调用代码，将返回类型从 `MedicalCases` 改为 `AcceptedInformationDiagnosisVO`
2. 更新字段访问代码，移除对不再存在字段的访问
3. 更新前端代码，适配新的数据结构
4. 测试所有相关功能，确保迁移成功

## 版本信息

- **引入版本**: 2025-07-22
- **相关接口**: `/v2/medical/cases/accepted-information-diagnosis`
- **相关类**: `AcceptedInformationDiagnosisVO`, `MedicalCasesController`
