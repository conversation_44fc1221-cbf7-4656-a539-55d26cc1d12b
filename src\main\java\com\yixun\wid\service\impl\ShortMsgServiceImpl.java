package com.yixun.wid.service.impl;

import cn.hutool.core.util.StrUtil;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.yixun.wid.service.ShortMsgService;
import com.yixun.wid.utils.AliyunSms;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ShortMsgServiceImpl implements ShortMsgService {

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${message.smsFlag}")
    private Integer smsFlag;

    @Value("${message.smsSign}")
    private String smsSign;

    //阿里发短信
    @Override
    public void sendSMS(Map<String, String> map) {
        try {
            log.info("开始发短信:" + map + smsSign);
            SendSmsResponse response = AliyunSms.sendSms(map, smsSign);
            log.info("短信回执:" + map.get("phone") + ", " + response.getCode() + ", " + response.getMessage());
            if (!response.getCode().equals("OK")) {
                throw new RuntimeException("短信发送失败，请稍后再试");
            }
        } catch (Exception e) {
            log.error("短信发送失败" + e.getMessage());
            // throw new RuntimeException("短信发送失败");
        }
    }

    @Override
    public void sendSMS(String templateCode, Map<String, String> paramMap) {
        if (smsFlag == 1) {
            paramMap.put("templateCode", templateCode);
            this.sendSMS(paramMap);
        }
    }

}
