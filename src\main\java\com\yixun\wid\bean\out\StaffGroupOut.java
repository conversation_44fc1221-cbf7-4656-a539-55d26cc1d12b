package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StaffGroupOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createDate;

    @ApiModelProperty("分组名称")
    private String groupName;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long parentId;

    @ApiModelProperty("显示顺序")
    private Integer showOrder;

    @ApiModelProperty("禁用标志")
    private Boolean isDisable;

    @ApiModelProperty("类型(1人社部门 2辅助调查)")
    private Integer type;
}
