package com.yixun.wid.v2.utils;

import com.yixun.wid.v2.entity.ResponsibilityTree;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 责任树工具类
 * 用于获取责任树路径，将责任树路径中的name用/拼接
 */
@Component
public class ResponsibilityTreeUtil {

    @Resource
    private MongoTemplate mongoTemplate;
    
    private Map<String, ResponsibilityTree> codeToNodeCache = new HashMap<>();
    private Map<Long, ResponsibilityTree> idToNodeCache = new HashMap<>();
    
    /**
     * 获取责任树路径名称
     * 根据责任树编码获取完整路径的名称，用/拼接
     *
     * @param code 责任树编码
     * @return 路径名称字符串，如"工伤保险责任/工伤医疗待遇/工伤门诊就诊费用"
     */
    public String getPathNameByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return "";
        }
        
        // 初始化缓存
        initCacheIfNeeded();
        
        // 根据code查找节点
        ResponsibilityTree node = codeToNodeCache.get(code);
        if (node == null) {
            return "";
        }
        
        // 获取节点路径
        List<ResponsibilityTree> pathNodes = getPathToRoot(node);
        
        // 拼接路径名称
        return pathNodes.stream()
                .map(ResponsibilityTree::getName)
                .collect(Collectors.joining("/"));
    }
    
    /**
     * 获取从根节点到当前节点的路径
     *
     * @param node 当前节点
     * @return 从根到当前节点的路径（倒序）
     */
    private List<ResponsibilityTree> getPathToRoot(ResponsibilityTree node) {
        List<ResponsibilityTree> path = new ArrayList<>();
        ResponsibilityTree current = node;
        
        while (current != null) {
            path.add(current);
            
            if (current.getParentId() == null || current.getParentId() == 0) {
                break;
            }
            
            current = idToNodeCache.get(current.getParentId());
        }
        
        // 反转列表，使其从根节点到当前节点排序
        Collections.reverse(path);
        return path;
    }
    
    /**
     * 初始化节点缓存
     */
    private synchronized void initCacheIfNeeded() {
        if (!codeToNodeCache.isEmpty() && !idToNodeCache.isEmpty()) {
            return;
        }
        
        // 查询所有节点
        Query query = new Query();
        List<ResponsibilityTree> allNodes = mongoTemplate.find(query, ResponsibilityTree.class);
        
        if (CollectionUtils.isEmpty(allNodes)) {
            return;
        }
        
        // 构建缓存
        for (ResponsibilityTree node : allNodes) {
            if (StringUtils.hasText(node.getCode())) {
                codeToNodeCache.put(node.getCode(), node);
            }
            if (node.getId() != null) {
                idToNodeCache.put(node.getId(), node);
            }
        }
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        codeToNodeCache.clear();
        idToNodeCache.clear();
    }
} 