package com.yixun.wid.v2.vo;

import lombok.Data;

/**
 * 统计数据点，可用于按月或按天统计
 */
@Data
public class CountPoint {
    /**
     * 横坐标值（可表示月份1-12或日期1-31）
     */
    private Integer x;
    
    /**
     * 数量
     */
    private Long y;
    
    /**
     * 平均时长
     */
    private Double avgTime;

    public CountPoint(Integer x, Long y) {
        this.x = x;
        this.y = y;
        this.avgTime = 0.0;
    }

    public CountPoint(Integer x, Long y, Double avgTime) {
        this.x = x;
        this.y = y;
        this.avgTime = avgTime;
    }
} 