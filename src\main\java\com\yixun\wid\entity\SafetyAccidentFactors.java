package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SafetyAccidentFactors {

    //事故原因
    @ApiModelProperty("人的不安全行为")
    private String peopleUnsafeBehavior;
    @ApiModelProperty("物的不安全状态")
    private String thingsUnsafeStatus;
    @ApiModelProperty("安全管理缺陷")
    private String safetyManageDefect;
    @ApiModelProperty("作业环境缺陷")
    private String workEnvDefect;
    @ApiModelProperty("应急缺陷")
    private String emergencyDefect;

    //安全责任人
    @ApiModelProperty("安全负责人")
    private String safetyPrincipal;
    @ApiModelProperty("安全员")
    private String safetyPerson;
    @ApiModelProperty("安全班组长")
    private String safetyTeamLeader;
    @ApiModelProperty("安全机构")
    private String safetyInstitution;

    //整改措施
    @ApiModelProperty("是否为企业重复事故")
    private Boolean isCorpRepeated;
    @ApiModelProperty("是否为岗位重复事故")
    private String isPositionRepeated;
    @ApiModelProperty("定时间")
    private String timeSuggestion;
    @ApiModelProperty("定责任")
    private String dutySuggestion;
    @ApiModelProperty("定人员")
    private String personSuggestion;
    @ApiModelProperty("定标准")
    private String standardSuggestion;
    @ApiModelProperty("定措施")
    private String measureSuggestion;
    @ApiModelProperty("整改时间")
    private String rectifyTime;

    //开展教育
    @ApiModelProperty("培训对象")
    private String trainees;
    @ApiModelProperty("培训内容")
    private String trainingContent;
    @ApiModelProperty("培训建议")
    private String trainingSuggestion;

}
