package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReceiveFile {

    private Long id;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty(value = "申报id")
    private Long declarationId;

    @ApiModelProperty(value = "案件号")
    private String caseSn;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "环节 申报，补充资料，认定中止，撤销")
    private String step;

    @ApiModelProperty(value = "类型 material-实物资料，virtual-虚拟/补充资料")
    private String type;

    @ApiModelProperty(value = "事故时间")
    private Date accidentTime;

    @ApiModelProperty(value = "上报人的用户id")
    private Long userId;

    @ApiModelProperty(value = "申报人/经办人姓名")
    private String applicantName;

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "是否已确认")
    private Boolean isConfirmed;

    @ApiModelProperty(value = "审核/确认时间")
    private Date confirmedTime;

    @ApiModelProperty(value = "文书编号")
    private String writSn;

    @ApiModelProperty(value = "资料列表")
    private List fileList;
}
