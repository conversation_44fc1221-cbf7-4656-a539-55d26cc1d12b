package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.aop.RequiredPermission;
import com.yixun.wid.bean.out.*;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Corporation;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.Investigation;
import com.yixun.wid.entity.em.*;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.CorporationService;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.service.InvestigationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin统计看板")
@RestController
@RequestMapping("/admin/statistic")
public class AdminStatisticController {

	@Resource
	private CasesService casesService;

	@Resource
	private CorporationService corporationService;

	@Resource
	private DeclarationService declarationService;

	@Resource
	private InvestigationService investigationService;

	@RequiredPermission("get:getCasesStatistic:statistic")
	@ApiOperation(value = "获取报案统计")
	@GetMapping(value = "/getCasesStatistic")
	public CommonResult<CasesStatisticOut> getCasesStatistic(@RequestParam StatisticType statisticType){

		Calendar c = Calendar.getInstance();
		if (statisticType.equals(StatisticType.month)){
			c.set(Calendar.DAY_OF_MONTH, 1);
			c.set(Calendar.HOUR_OF_DAY, 0);
			c.set(Calendar.MINUTE, 0);
			c.set(Calendar.SECOND, 0);
		}
		if (statisticType.equals(StatisticType.year)){
			c.set(Calendar.DAY_OF_YEAR, 1);
			c.set(Calendar.HOUR_OF_DAY, 0);
			c.set(Calendar.MINUTE, 0);
			c.set(Calendar.SECOND, 0);
		}
		if (statisticType.equals(StatisticType.quarter)){
			int currentMonth = c.get(Calendar.MONTH);

			// 确定当前季度的开始月份
			int startMonthOfQuarter;
			if (currentMonth <= Calendar.MARCH) {
				startMonthOfQuarter = Calendar.JANUARY;
			} else if (currentMonth <= Calendar.JUNE) {
				startMonthOfQuarter = Calendar.APRIL;
			} else if (currentMonth <= Calendar.SEPTEMBER) {
				startMonthOfQuarter = Calendar.JULY;
			} else {
				startMonthOfQuarter = Calendar.OCTOBER;
			}

			// 将日历的月份设置为季度开始的月份，日期设置为1号
			c.set(Calendar.MONTH, startMonthOfQuarter);
			c.set(Calendar.DAY_OF_MONTH, 1);
			c.set(Calendar.HOUR_OF_DAY, 0);
			c.set(Calendar.MINUTE, 0);
			c.set(Calendar.SECOND, 0);
		}

		CasesStatisticOut out = new CasesStatisticOut();
		List<Cases> casesList = casesService.getCasesStatistic(c.getTime());

		long submittedCount = casesList.stream().filter(d -> d.getStatus()!=null &&
                (d.getStatus().equals(CasesStatus.Submitted.name()) || d.getStatus().equals(CasesStatus.Applied.name()))
				&& (d.getIsNoNeedApply()==null || !d.getIsNoNeedApply())).count();
		out.setSubmittedCount(submittedCount);

		long appliedCount = casesList.stream().filter(d -> d.getStatus()!=null &&
				d.getStatus().equals(CasesStatus.Applied.name())).count();
		out.setAppliedCount(appliedCount);

		return CommonResult.successData(out);
	}

	@RequiredPermission("get:getCasesStatisticList:statistic")
	@ApiOperation(value = "获取报案统计列表")
	@GetMapping(value = "/getCasesStatisticList")
	public CommonResult<List<CasesStatisticListOut>> getCasesStatisticList(String organization, CommonPage commonPage){

		List<CasesStatisticListOut> outList = casesService.getCasesStatisticList(organization, commonPage);

		List<Long> corpIds = outList.stream().map(CasesStatisticListOut::getOrganizationId).collect(Collectors.toList());
		List<Corporation> corporationList = corporationService.getByIds(corpIds);

		for (CasesStatisticListOut out : outList){
			Optional<Corporation> optional = corporationList.stream().filter(c -> c.getId().equals(out.getOrganizationId())).findFirst();
			if (optional.isPresent()){
				Corporation corporation = optional.get();
				if (corporation.getStatus().contains("Authenticated")){
					out.setIsAuthenticated(true);
				}else {
					out.setIsAuthenticated(false);
				}
			}else {
				out.setIsAuthenticated(false);
			}
		}

		return CommonResult.successPageData(outList, commonPage);
	}

	@RequiredPermission("get:getDeclarationStatistic:statistic")
    @ApiOperation(value = "获取申报统计")
	@GetMapping(value = "/getDeclarationStatistic")
	public CommonResult<DeclarationStatisticOut> getDeclarationStatistic(){

		Calendar c = Calendar.getInstance();
		c.add(Calendar.MONTH, -1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);

		DeclarationStatisticOut out = new DeclarationStatisticOut();
		List<Declaration> declarationList = declarationService.getDeclarationStatistic(c.getTime());

		long acceptingCount = declarationList.stream().filter(d -> d.getStatus()!=null &&
				d.getStatus().equals(DeclarationStatus.Accepting.name())).count();
		out.setAcceptingCount(acceptingCount);

		long acceptCount = declarationList.stream().filter(d -> d.getStatus()!=null &&
				d.getStatus().equals(DeclarationStatus.Identifying.name())).count();
		out.setAcceptCount(acceptCount);

		long furtherInfoCount = declarationList.stream().filter(d -> d.getSubStatus()!=null &&
				d.getSubStatus().equals(DeclarationSubStatus.FurtherInfo.name())).count();
		out.setFurtherInfoCount(furtherInfoCount);

		long notAcceptCount = declarationList.stream().filter(d -> d.getSubStatus()!=null &&
				d.getSubStatus().equals(DeclarationSubStatus.NotAccept.name())).count();
		out.setNotAcceptCount(notAcceptCount);

		long identifyCount = declarationList.stream().filter(d -> d.getStatus()!=null &&
				d.getStatus().equals(DeclarationStatus.Identifying.name())).count();
		out.setIdentifyCount(identifyCount);

		long notIdentifyCount = declarationList.stream().filter(d -> d.getSubStatus()!=null &&
				d.getSubStatus().equals(DeclarationSubStatus.NotIdentify.name())).count();
		out.setNotIdentifyCount(notIdentifyCount);

//		long classifyCount = declarationList.stream().filter(d -> d.getStatus()!=null && d.getStatus().equals(DeclarationStatus.Done.name()) && d.getSubStatus()==null).count();
		out.setClassifyCount(0L);

		return CommonResult.successData(out);
	}

	@RequiredPermission("get:getInvestigationStatistic:statistic")
	@ApiOperation(value = "获取第三方调查统计")
	@GetMapping(value = "/getInvestigationStatistic")
	public CommonResult<InvestigationStatisticOut> getInvestigationStatistic() {

		Calendar c = Calendar.getInstance();
		c.add(Calendar.MONTH, -1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);

		InvestigationStatisticOut out = new InvestigationStatisticOut();
		List<Investigation> investigationList = investigationService.getInvestigationStatistic(c.getTime());

		long startCount = investigationList.stream().filter(i -> i.getStatus()!=null &&
				i.getStatus().equals(InvestigationStatus.Start.name())).count();
		out.setStartCount(startCount);

		long investigatingCount = investigationList.stream().filter(i -> i.getStatus()!=null &&
				i.getStatus().equals(InvestigationStatus.Investigating.name())).count();
		out.setInvestigatingCount(investigatingCount);

		long doneCount = investigationList.stream().filter(i -> i.getStatus()!=null &&
				i.getStatus().equals(InvestigationStatus.Done.name())).count();
		out.setDoneCount(doneCount);

		return CommonResult.successData(out);
	}

	@RequiredPermission("get:getClassifyStatistic:statistic")
	@ApiOperation(value = "获取鉴定评级统计")
	@GetMapping(value = "/getClassifyStatistic")
	public CommonResult<ClassifyStatisticOut> getClassifyStatistic() {

		Calendar c = Calendar.getInstance();
		c.set(Calendar.DAY_OF_YEAR, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);

		ClassifyStatisticOut out = new ClassifyStatisticOut();
		List<Declaration> declarationList = declarationService.getDeclarationStatistic(c.getTime());
		Map<String, Long> collectMap = declarationList.stream().filter(d->d.getInjureLevel()!=null)
				.collect(Collectors.groupingBy(Declaration::getInjureLevel, Collectors.counting()));

		List<String> levelList = Arrays.asList("一级","二级","三级","四级","五级",
				"六级","七级","八级","九级","十级", "未达等级");
		List<ClassifyStatisticOut.LevelCount> levelCountList = new ArrayList<>();
		levelList.forEach(level->{
			ClassifyStatisticOut.LevelCount levelCount = new ClassifyStatisticOut.LevelCount();
			levelCount.setLevel(level);
			long count = collectMap.get(level)==null ? 0 : collectMap.get(level);
			levelCount.setCount(count);
			levelCountList.add(levelCount);
		});
		out.setLevelCountList(levelCountList);

		return CommonResult.successData(out);
	}

}
