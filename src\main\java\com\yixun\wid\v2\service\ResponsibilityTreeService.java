package com.yixun.wid.v2.service;

import com.yixun.wid.v2.entity.ResponsibilityTree;

import java.util.List;

/**
 * 责任列表树形结构服务接口
 */
public interface ResponsibilityTreeService {

    /**
     * 查询完整树形结构
     *
     * @return 树形结构（顶层节点列表）
     */
    List<ResponsibilityTree> getTree();

    /**
     * 根据ID查询节点及其子孙节点
     *
     * @param id 节点ID
     * @return 以指定节点为根的子树
     */
    ResponsibilityTree getTreeById(Long id);

    /**
     * 查询所有节点（平铺结构）
     *
     * @return 所有节点列表
     */
    List<ResponsibilityTree> getAll();

    /**
     * 新增节点
     *
     * @param responsibilityTree 节点信息
     * @return 新增后的节点
     */
    ResponsibilityTree add(ResponsibilityTree responsibilityTree);

    /**
     * 更新节点
     *
     * @param responsibilityTree 节点信息
     * @return 更新后的节点
     */
    ResponsibilityTree update(ResponsibilityTree responsibilityTree);

    /**
     * 删除节点及其子节点
     *
     * @param id 节点ID
     * @return 删除的节点数
     */
    Long delete(Long id);
} 