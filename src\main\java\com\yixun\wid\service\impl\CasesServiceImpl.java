package com.yixun.wid.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yixun.api.ApiSnApi;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CasesGetIn;
import com.yixun.wid.bean.other.TaskType;
import com.yixun.wid.bean.out.CasesStatisticListOut;
import com.yixun.wid.bean.out.RelateCasesOut;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.CasesLog;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.em.CasesStatus;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.entity.em.DeclarationSubStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.MessageService;
import com.yixun.wid.service.ShortMsgService;
import com.yixun.wid.utils.AdminUserHelper;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.OkHttpKit;
import com.yixun.wid.utils.SnGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CasesServiceImpl implements CasesService {

    @Value("${api.workInjuryDeclareApi}")
    private String workInjuryDeclareApi;

    @Value("${api.token}")
    private String token;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AdministratorService administratorService;

    @Resource
    private MessageService messageService;

    @Resource
    private ShortMsgService shortMsgService;

    @Resource
    private ApiSnApi apiSnApi;

    @Override
    public void save(Cases cases) {
        mongoTemplate.save(cases);
    }

    @Override
    public void update(Cases cases) {
        cases.setUpdateTime(new Date());
        mongoTemplate.save(cases);
    }

    @Override
    public List<Cases> getCasesList(CasesGetIn casesGetIn, CommonPage commonPage) {
        Query query = new Query();
        if (casesGetIn.getName() != null) {
            query.addCriteria(Criteria.where("name").regex(casesGetIn.getName()));
        }
        if (casesGetIn.getStatus() != null) {
            query.addCriteria(Criteria.where("status").is(casesGetIn.getStatus().name()));
        } else {
            query.addCriteria(Criteria.where("status").ne(CasesStatus.Start.name()));
        }
        if (casesGetIn.getInjuredPart() != null) {
            query.addCriteria(Criteria.where("injuredPart").is(casesGetIn.getInjuredPart()));
        }
        if (casesGetIn.getStartDate() != null && casesGetIn.getEndDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(casesGetIn.getEndDate());
            c.add(Calendar.SECOND, 86399); //结束时间加到当天的23:59:59
            query.addCriteria(Criteria.where("accidentTime").gte(casesGetIn.getStartDate()).lte(c.getTime()));
        }
        if (casesGetIn.getOrganization() != null) {
            query.addCriteria(Criteria.where("organization").regex(casesGetIn.getOrganization()));
        }
        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, Cases.class, query, commonPage);
        return mongoTemplate.find(query, Cases.class);
    }

    @Override
    public Cases getById(Long casesId) {
        return mongoTemplate.findById(casesId, Cases.class);
    }

    @Override
    public void setCasesLog(Long casesId, String type) {
        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        CasesLog casesLog = new CasesLog();
        casesLog.setId(SnGeneratorUtil.getId());
        casesLog.setCasesId(casesId);
        casesLog.setCreateTime(new Date());
        casesLog.setType(type);
        casesLog.setName(administrator.getRealName());
        mongoTemplate.save(casesLog);
    }

    @Override
    public List<CasesLog> getCasesLogList(Long casesId, CommonPage commonPage) {
        Query query = new Query();
        query.addCriteria(Criteria.where("casesId").is(casesId));

        MongoUtil.setPageInfo(mongoTemplate, CasesLog.class, query, commonPage);
        return mongoTemplate.find(query, CasesLog.class);
    }

    @Override
    public String getCaseSn() {
        try {
//            JSONObject connGet = OkHttpKit.apiGetWithToken(workInjuryDeclareApi + "/api/sn/getCaseSn",
//                    null, token);
            CommonResult<String> caseSn = apiSnApi.getCaseSn();
            if (caseSn != null && caseSn.getCode() == 200) {
                return caseSn.getData();
            } else {
                throw new DataErrorException(caseSn.getMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public void updateCorpName(String companyName, Long id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("organization").is(companyName));
        Update update = new Update();
        update.set("organizationId", id);
        mongoTemplate.updateMulti(query, update, Cases.class);
    }

    @Override
    public List<Cases> getCasesStatistic(Date startTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").gte(startTime).lte(new Date()));
        return mongoTemplate.find(query, Cases.class);
    }

    @Override
    public List<Cases> getByUserAccidentDate(Long id, String idCard, Date accidentTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("idCard").is(idCard));

        if (id != null) {
            query.addCriteria(Criteria.where("id").ne(id));
        }

        Calendar c = Calendar.getInstance();
        c.setTime(accidentTime);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        Date start = c.getTime();

        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        Date end = c.getTime();

        query.addCriteria(Criteria.where("accidentTime").gte(start).lte(end));
        query.addCriteria(Criteria.where("status").ne("Start"));

        return mongoTemplate.find(query, Cases.class);
    }

    @Override
    public List<CasesStatisticListOut> getCasesStatisticList(String organization, CommonPage commonPage) {

        int pageSize = commonPage.getPageSize();

        MatchOperation match = Aggregation.match(Criteria.where("organization").regex(""));
        if (organization != null) {
            match = Aggregation.match(Criteria.where("organization").regex(organization));
        }

        Aggregation countA = Aggregation.newAggregation(
                match,
                Aggregation.group("organizationId")
        );
        List<Map> cases = mongoTemplate.aggregate(countA, "cases", Map.class).getMappedResults();
        int totalCount = cases.size();

        Aggregation aggregation = Aggregation.newAggregation(
                match,
                Aggregation.group("creditCode")
                        .first("organizationId").as("organizationId")
                        .first("organization").as("organization")
                        .first("creditCode").as("creditCode")
                        .first("createTime").as("createTime")
                        .sum(
                                ConditionalOperators
                                        .when(Criteria.where("isNoNeedApply").is(true))
                                        .then(1)
                                        .otherwise(0)
                        ).as("noNeedApplyCount")
                        .sum(
                                ConditionalOperators
                                        .when(new Criteria().orOperator(Criteria.where("status").is("Submitted"),
                                                Criteria.where("status").is("Applied").and("isNoNeedApply").is(false)))
                                        .then(1)
                                        .otherwise(0)
                        ).as("submittedCount")
                        .sum(
                                ConditionalOperators
                                        .when(Criteria.where("status").is("Applied"))
                                        .then(1)
                                        .otherwise(0)
                        ).as("appliedCount"),
                Aggregation.sort(Sort.by(Sort.Order.desc("createTime"))),
                Aggregation.skip((long) (commonPage.getPageNum() - 1) * pageSize),
                Aggregation.limit(pageSize)
        );

        if (totalCount <= 0) {
            commonPage.setTotal(0);
            commonPage.setPages(0);
        } else {
            commonPage.setTotal(totalCount);
            commonPage.setPages(totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1);
        }

        return mongoTemplate.aggregate(aggregation, "cases", CasesStatisticListOut.class).getMappedResults();
    }

    @Override
    public List<Cases> getByIdList(List<Long> caseIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(caseIds));

        return mongoTemplate.find(query, Cases.class);
    }

    @XxlJob("sendOver15DayMsHandler")
    @Override
    public void sendOver15DayMs() {
        LocalDateTime currentLocalDate = LocalDateTime.now();
        // 计算15天前的日期
        LocalDateTime fifteenDaysAgo = currentLocalDate.minusDays(15);
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").lte(fifteenDaysAgo));
        query.addCriteria(Criteria.where("status").is(CasesStatus.Submitted.name()));
        List<Cases> cases = mongoTemplate.find(query, Cases.class);
        List<Long> idList = cases.stream()
                .map(Cases::getId)
                .collect(Collectors.toList());
        query = new Query();
        query.addCriteria(Criteria.where("casesId").in(idList));
        List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);
        List<Long> declarationCasesIds = declarations.stream()
                .map(Declaration::getCasesId)
                .collect(Collectors.toList());
        List<Cases> filteredCasesList = cases.stream()
                .filter(c -> !declarationCasesIds.contains(c.getId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filteredCasesList)) {
            //发送订阅消息
            String message = "用人单位应在事故发生日或职业病诊断日起30日内提出工伤认定申请；工伤职工或者近亲属、工会组织应在1年内提起工伤认定申请";
            Set<String> phones = new HashSet<>();
            filteredCasesList.forEach(e -> {
                messageService.sendTrueWxMessage(e.getUserId() == null ? e.getOrgSubmitterId() : e.getUserId(), "工伤快报", "报案后15个自然日且未申报的",
                        message);
                phones.add(e.getReportPhone());
            });
            Map<String, String> paramMap = new HashMap<>();
            phones.forEach(e -> {
                if (StringUtils.hasLength(e)) {
                    paramMap.put("phone", e);
                    shortMsgService.sendSMS("SMS_470445041", paramMap);
                }
            });
        }
        //定时发送消息
        //1、 >=10天，待受理
        LocalDateTime tenDaysAgo = currentLocalDate.minusDays(10);
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(tenDaysAgo));
        query.addCriteria(Criteria.where("status").is(DeclarationStatus.Accepting.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        Date currentDate = new Date();
        declarations.forEach(e -> {
            //发送PC端消息
            this.sendAdminMessage("时效提醒", e.getName() + "的工伤认定申请已提交" + DateUtil.betweenDay(e.getUpdateTime(), currentDate, true) + "天，请尽快做出受理结论！", 1,
                    "/task/acceptDetail?id=" + e.getId(), TaskType.ADD_DECLARATION_OVER10, e.getId() + "");
        });
        //2、 >=45天，认定中
        LocalDateTime fortyFiveDaysAgo = currentLocalDate.minusDays(45);
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(fortyFiveDaysAgo));
        query.addCriteria(Criteria.where("status").is(DeclarationStatus.Identifying.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        declarations.forEach(e -> {
            //发送PC端消息
            this.sendAdminMessage("时效提醒", e.getName() + "的工伤认定申请已提交" + DateUtil.betweenDay(e.getUpdateTime(), currentDate, true) + "天，请尽快做出认定结论！", 1,
                    "/task/acceptDetail?id=" + e.getId(), TaskType.IDENTIFYING_OVER45, e.getId() + "");
        });
        //3、 >=3天，资料补充
        LocalDateTime threeDaysAgo = currentLocalDate.minusDays(3);
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(threeDaysAgo));
        query.addCriteria(Criteria.where("subStatus").is(DeclarationSubStatus.Auditing.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        declarations.forEach(e -> {
            //发送PC端消息
            this.sendAdminMessage("时效提醒", e.getName() + "的补充资料已提交" + DateUtil.betweenDay(e.getUpdateTime(), currentDate, true) + "天，请尽快审核！", 1,
                    "/task/acceptDetail?id=" + e.getId() + "&page=accept", TaskType.FURTHER_INFO_OVER3, e.getId() + "");
        });
        if (!CollectionUtils.isEmpty(declarations)) {
            //发送短信
            Map<String, String> paramMap = new HashMap<>();
            this.sendAdminSms("SMS_470750002", paramMap);
        }
        //4、 >=3天，认定中止资料补充
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(threeDaysAgo));
        query.addCriteria(Criteria.where("subStatus").is(DeclarationSubStatus.SuspendAuditing.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        declarations.forEach(e -> {
            //发送PC端消息
            this.sendAdminMessage("时效提醒", e.getName() + "的补充资料已提交" + DateUtil.betweenDay(e.getUpdateTime(), currentDate, true) + "天，请尽快审核！", 1,
                    "/task/acceptDetail?id=" + e.getId() + "&page=accept", TaskType.DEEMED_TERMINATION_OVER3, e.getId() + "");
        });
        if (!CollectionUtils.isEmpty(declarations)) {
            //发送短信
            Map<String, String> paramMap = new HashMap<>();
            this.sendAdminSms("SMS_470470142", paramMap);
        }
        // >=12天，待受理
        LocalDateTime twelveDaysAgo = currentLocalDate.minusDays(12);
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(twelveDaysAgo));
        query.addCriteria(Criteria.where("status").is(DeclarationStatus.Accepting.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        if (!CollectionUtils.isEmpty(declarations)) {
            //发送短信
            Map<String, String> paramMap = new HashMap<>();
            this.sendAdminSms("SMS_470695029", paramMap);
        }
        //>=50天，认定中
        LocalDateTime fiftyDaysAgo = currentLocalDate.minusDays(50);
        query = new Query();
        query.addCriteria(Criteria.where("updateTime").lte(fiftyDaysAgo));
        query.addCriteria(Criteria.where("status").is(DeclarationStatus.Identifying.name()));
        declarations = mongoTemplate.find(query, Declaration.class);
        if (!CollectionUtils.isEmpty(declarations)) {
            //发送短信
            Map<String, String> paramMap = new HashMap<>();
            this.sendAdminSms("SMS_470600157", paramMap);
        }
    }

    @Override
    public List<RelateCasesOut> getRelateCase(Long casesId) {
        Cases cases = mongoTemplate.findById(casesId, Cases.class);
        if (cases == null) {
            throw new DataErrorException("casesId错误");
        }
        Date accidentTime = cases.getAccidentTime();
        DateTime beginTime = DateUtil.beginOfDay(accidentTime);
        DateTime endTime = DateUtil.endOfDay(accidentTime);
        Query query = new Query();
        query.addCriteria(Criteria.where("organizationId").is(cases.getOrganizationId()));
        query.addCriteria(Criteria.where("accidentTime").gte(beginTime).lte(endTime));
        query.addCriteria(Criteria.where("id").ne(casesId));
        List<Cases> casesList = mongoTemplate.find(query, Cases.class);
        List<RelateCasesOut> relateCasesOuts = BeanUtil.copyToList(casesList, RelateCasesOut.class);
        return relateCasesOuts;
    }

    void sendAdminMessage(String title, String Content, Integer type, String redirectUrl, String businessType, String businessParam) {
        List<Administrator> administrators = administratorService.list(new QueryWrapper<Administrator>().eq("is_disable", 0));
        List<Long> userIds = administrators.stream()
                .map(Administrator::getId)
                .collect(Collectors.toList());
        messageService.sendAdminMessage(userIds, title, Content, type,
                redirectUrl, businessType, businessParam);
    }

    void sendAdminSms(String templateCode, Map<String, String> paramMap) {
        List<Administrator> administrators = administratorService.list(new QueryWrapper<Administrator>().eq("is_disable", 0));
        Set<String> phones = administrators.stream()
                .map(Administrator::getPhone)
                .collect(Collectors.toSet());
        phones.forEach(e -> {
            if (StringUtils.hasLength(e)) {
                paramMap.put("phone", e);
                shortMsgService.sendSMS(templateCode, paramMap);
            }
        });
    }

}




