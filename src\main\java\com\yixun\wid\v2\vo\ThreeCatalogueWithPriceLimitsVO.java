package com.yixun.wid.v2.vo;

import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 三目录信息及其关联的限价目录列表
 */
@Data
public class ThreeCatalogueWithPriceLimitsVO {
    
    /**
     * 三目录信息
     */
    @NotNull(message = "三目录信息不能为空")
    @Valid
    private ThreeCatalogue threeCatalogue;
    
    /**
     * 限价目录列表
     */
    private List<PriceLimit> priceLimits;
} 