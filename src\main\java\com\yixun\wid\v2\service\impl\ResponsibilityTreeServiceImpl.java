package com.yixun.wid.v2.service.impl;

import com.mongodb.client.result.DeleteResult;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.ResponsibilityTree;
import com.yixun.wid.v2.service.ResponsibilityTreeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 责任列表树形结构服务实现类
 */
@Slf4j
@Service
public class ResponsibilityTreeServiceImpl implements ResponsibilityTreeService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public List<ResponsibilityTree> getTree() {
        // 1. 查询所有节点
        List<ResponsibilityTree> allNodes = getAll();
        if (CollectionUtils.isEmpty(allNodes)) {
            return new ArrayList<>();
        }

        // 2. 转换为树形结构
        return buildTree(allNodes);
    }

    @Override
    public ResponsibilityTree getTreeById(Long id) {
        if (id == null) {
            throw new RuntimeException("节点ID不能为空");
        }

        // 1. 查询指定节点
        ResponsibilityTree node = mongoTemplate.findById(id, ResponsibilityTree.class);
        if (node == null) {
            throw new RuntimeException("节点不存在");
        }

        // 2. 查询所有子孙节点
        List<ResponsibilityTree> allDescendants = new ArrayList<>();
        findAllDescendants(id, allDescendants);

        // 3. 构建子树
        Map<Long, ResponsibilityTree> nodeMap = new HashMap<>();
        nodeMap.put(node.getId(), node);
        for (ResponsibilityTree descendant : allDescendants) {
            nodeMap.put(descendant.getId(), descendant);
        }

        // 4. 设置父子关系
        for (ResponsibilityTree descendant : allDescendants) {
            ResponsibilityTree parent = nodeMap.get(descendant.getParentId());
            if (parent != null) {
                parent.getChildren().add(descendant);
            }
        }

        // 5. 对所有节点的子节点按排序号升序排序
        sortChildren(node);

        return node;
    }

    @Override
    public List<ResponsibilityTree> getAll() {
        // 查询所有节点，按sort字段升序排序
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.ASC, "sort", "id"));
        return mongoTemplate.find(query, ResponsibilityTree.class);
    }

    @Override
    public ResponsibilityTree add(ResponsibilityTree responsibilityTree) {
        // 参数校验
        if (responsibilityTree == null) {
            throw new RuntimeException("节点信息不能为空");
        }
        if (responsibilityTree.getParentId() == null) {
            responsibilityTree.setParentId(0L);  // 默认为根节点
        }

        // 设置层级
        if (responsibilityTree.getParentId() == 0) {
            responsibilityTree.setLevel(1);  // 根节点层级为1
        } else {
            ResponsibilityTree parent = mongoTemplate.findById(responsibilityTree.getParentId(), ResponsibilityTree.class);
            if (parent == null) {
                throw new RuntimeException("父节点不存在");
            }
            responsibilityTree.setLevel(parent.getLevel() + 1);
        }

        // 设置ID和时间
        responsibilityTree.setId(SnGeneratorUtil.getId());
        Date now = new Date();
        responsibilityTree.setCreateTime(now);
        responsibilityTree.setUpdateTime(now);

        // 设置排序号，如果未指定则默认为同级最大排序号+10
        if (responsibilityTree.getSort() == null) {
            Query query = new Query(Criteria.where("parentId").is(responsibilityTree.getParentId()));
            query.with(Sort.by(Sort.Direction.DESC, "sort"));
            query.limit(1);
            ResponsibilityTree lastNode = mongoTemplate.findOne(query, ResponsibilityTree.class);
            int sort = (lastNode == null || lastNode.getSort() == null) ? 10 : lastNode.getSort() + 10;
            responsibilityTree.setSort(sort);
        }

        // 保存到数据库
        mongoTemplate.save(responsibilityTree);

        return responsibilityTree;
    }

    @Override
    public ResponsibilityTree update(ResponsibilityTree responsibilityTree) {
        // 参数校验
        if (responsibilityTree == null || responsibilityTree.getId() == null) {
            throw new RuntimeException("节点ID不能为空");
        }

        // 查询原有节点
        ResponsibilityTree existingNode = mongoTemplate.findById(responsibilityTree.getId(), ResponsibilityTree.class);
        if (existingNode == null) {
            throw new RuntimeException("节点不存在");
        }

        // 不允许修改父节点（避免循环引用和层级混乱）
        responsibilityTree.setParentId(existingNode.getParentId());
        responsibilityTree.setLevel(existingNode.getLevel());

        // 保留创建时间，更新修改时间
        responsibilityTree.setCreateTime(existingNode.getCreateTime());
        responsibilityTree.setUpdateTime(new Date());

        // 更新到数据库
        mongoTemplate.save(responsibilityTree);

        return responsibilityTree;
    }

    @Override
    public Long delete(Long id) {
        if (id == null) {
            throw new RuntimeException("节点ID不能为空");
        }

        // 查找所有要删除的节点ID（包括当前节点及其所有子孙节点）
        List<Long> idsToDelete = new ArrayList<>();
        idsToDelete.add(id);
        findDescendantIds(id, idsToDelete);

        // 批量删除
        Query query = Query.query(Criteria.where("_id").in(idsToDelete));
        DeleteResult result = mongoTemplate.remove(query, ResponsibilityTree.class);

        return result.getDeletedCount();
    }

    /**
     * 构建树形结构
     *
     * @param nodes 所有节点列表
     * @return 树形结构（顶层节点列表）
     */
    private List<ResponsibilityTree> buildTree(List<ResponsibilityTree> nodes) {
        // 1. 按ID索引所有节点
        Map<Long, ResponsibilityTree> nodeMap = nodes.stream()
                .collect(Collectors.toMap(ResponsibilityTree::getId, node -> node));

        List<ResponsibilityTree> rootNodes = new ArrayList<>();

        // 2. 遍历所有节点，建立父子关系
        for (ResponsibilityTree node : nodes) {
            if (node.getParentId() == null || node.getParentId() == 0) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 非根节点，找到父节点并添加到其子节点列表
                ResponsibilityTree parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                } else {
                    // 父节点不存在，视为顶层节点
                    rootNodes.add(node);
                }
            }
        }

        // 3. 对所有节点的children列表按排序号排序
        for (ResponsibilityTree node : nodes) {
            if (!node.getChildren().isEmpty()) {
                node.getChildren().sort(Comparator.comparing(ResponsibilityTree::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ResponsibilityTree::getId));
            }
        }

        // 4. 根节点按排序号排序
        rootNodes.sort(Comparator.comparing(ResponsibilityTree::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ResponsibilityTree::getId));

        return rootNodes;
    }

    /**
     * 查找指定节点的所有子孙节点
     *
     * @param parentId 父节点ID
     * @param result   结果列表
     */
    private void findAllDescendants(Long parentId, List<ResponsibilityTree> result) {
        // 查询直接子节点
        Query query = Query.query(Criteria.where("parentId").is(parentId));
        query.with(Sort.by(Sort.Direction.ASC, "sort", "id"));
        List<ResponsibilityTree> children = mongoTemplate.find(query, ResponsibilityTree.class);

        if (!CollectionUtils.isEmpty(children)) {
            result.addAll(children);
            // 递归查询每个子节点的子孙节点
            for (ResponsibilityTree child : children) {
                findAllDescendants(child.getId(), result);
            }
        }
    }

    /**
     * 查找指定节点的所有子孙节点ID
     *
     * @param parentId 父节点ID
     * @param result   结果列表
     */
    private void findDescendantIds(Long parentId, List<Long> result) {
        Query query = Query.query(Criteria.where("parentId").is(parentId));
        query.fields().include("_id");
        List<ResponsibilityTree> children = mongoTemplate.find(query, ResponsibilityTree.class);

        if (!CollectionUtils.isEmpty(children)) {
            List<Long> childIds = children.stream().map(ResponsibilityTree::getId).collect(Collectors.toList());
            result.addAll(childIds);
            // 递归查询每个子节点的子孙节点
            for (Long childId : childIds) {
                findDescendantIds(childId, result);
            }
        }
    }

    /**
     * 递归对节点的子节点进行排序
     *
     * @param node 节点
     */
    private void sortChildren(ResponsibilityTree node) {
        if (node != null && !CollectionUtils.isEmpty(node.getChildren())) {
            node.getChildren().sort(Comparator.comparing(ResponsibilityTree::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(ResponsibilityTree::getId));
            for (ResponsibilityTree child : node.getChildren()) {
                sortChildren(child);
            }
        }
    }
} 