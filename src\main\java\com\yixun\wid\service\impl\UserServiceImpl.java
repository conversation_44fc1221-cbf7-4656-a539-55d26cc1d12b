package com.yixun.wid.service.impl;

import com.alibaba.fastjson.JSON;
import com.yixun.wid.bean.out.UserInfoCache;
import com.yixun.wid.entity.User;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.RedisKeyResolver;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class UserServiceImpl implements UserService{

    @Value("${jwt.expiration}")
    private Long expiration;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

	@Override
	public User getByIdCardType(String phone, String type) {
		Query query = new Query();
		query.addCriteria(Criteria.where("idCard").is(phone));
		query.addCriteria(Criteria.where("type").is(type));

		return mongoTemplate.findOne(query, User.class);
	}

    @Override
    public void insert(User user) {
        mongoTemplate.save(user);
    }

    @Override
    public User getUserById(Long userId) {
        return mongoTemplate.findById(userId, User.class);
    }

    @Override
    public UserInfoCache getUserInfoByIdFromCache(Long userId) {
        String userInfoKey = RedisKeyResolver.getUserInfoKey(userId);
        String userInfoCacheString = stringRedisTemplate.opsForValue().get(userInfoKey);
        UserInfoCache userInfoCache = JSON.parseObject(userInfoCacheString, UserInfoCache.class);
        if (userInfoCache == null) {
            throw new DataErrorException("会话已过期，请重新登录");
        }
        return userInfoCache;
    }

    @Override
    public void saveUserInfoCache(UserInfoCache userInfoCache) {
        String userInfoKey = RedisKeyResolver.getUserInfoKey(userInfoCache.getId());
        String userInfoCacheString = JSON.toJSONString(userInfoCache);
        stringRedisTemplate.opsForValue().set(userInfoKey, userInfoCacheString, expiration, TimeUnit.SECONDS);
    }

    @Override
    public User getByPhone(String phone) {
        Query query = new Query();
        query.addCriteria(Criteria.where("phone").is(phone));

        return mongoTemplate.findOne(query, User.class);
    }

    @Override
    public void updateById(User user) {
        mongoTemplate.save(user);
    }

    @Override
    public User getWeixinByMiniOpenId(String openid) {
        Query query = new Query();
        query.addCriteria(Criteria.where("weixinMiniOpenId").is(openid));

        return mongoTemplate.findOne(query, User.class);
    }
}




