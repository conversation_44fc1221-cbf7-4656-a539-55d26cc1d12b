package com.yixun.wid.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.wid.aop.RequiredPermission;
import com.yixun.wid.entity.Permission;
import com.yixun.wid.entity.Role;
import com.yixun.wid.entity.RolePermission;
import com.yixun.wid.mapper.PermissionMapper;
import com.yixun.wid.mapper.RoleMapper;
import com.yixun.wid.mapper.RolePermissionMapper;
import com.yixun.wid.service.RolePermissionService;
import com.yixun.wid.utils.SpringContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RolePermissionServiceImpl implements RolePermissionService {


    @Value("${api.token}")
    private String token;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private RolePermissionMapper rolePermissionMapper;

    @Resource
    private PermissionMapper permissionMapper;

    @Override
    public void insertRole(Role role) {
        roleMapper.insert(role);
    }

    @Override
    public void updateRole(Role role) {
        roleMapper.updateById(role);
    }

    @Override
    public Role getRoleById(Long roleId) {
        return roleMapper.selectById(roleId);
    }

    @Override
    public Role getRoleByName(String roleName) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", roleName);
        return roleMapper.selectOne(queryWrapper);
    }

    @Override
    public Page<Role> getAllRoleList(String label, CommonPage commonPage) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        if (label!=null){
            queryWrapper.like("label", label);
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<Role>) roleMapper.selectPage(page, queryWrapper);
    }

    @Override
    public void deleteRole(Long roleId) {
        roleMapper.deleteById(roleId);
    }

    @Override
    @Transactional
    public void setRolePermissions(String role, List<String> permissionList) {
        cleanPermissionsOfRole(role);

        permissionList.forEach(permission -> {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRole(role);
            rolePermission.setPermission(permission);
            rolePermissionMapper.insert(rolePermission);
        });
    }

    @Override
    public void cleanPermissionsOfRole(String role) {
        QueryWrapper<RolePermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role", role);
        rolePermissionMapper.delete(queryWrapper);
    }

    @Override
    public void initPermission(List<Permission> permissionList) {
        permissionMapper.delete(null);
        permissionMapper.initPermission(permissionList);
    }

    @Override
    public Permission getPermissionByName(String permissionName) {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", permissionName);

        return permissionMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Permission> getAllPermissionList() {
        return permissionMapper.selectList(null);
    }

    @Override
    public List<Permission> getPermissionListByRole(String role) {
        return permissionMapper.getPermissionListByRole(role);
    }

    @Override
    public List<Permission> getPermissionListByRoleList(List<String> roleList) {
        return permissionMapper.getPermissionListByRoleList(roleList);
    }

    @Override
    public List<Permission> loadPermissions() {
        Map<String, Object> map = SpringContextHolder.getContext().getBeansWithAnnotation(RestController.class);
        Collection<Object> set = map.values();

        List<Permission> permissionList = new ArrayList<>();
        for (Object cls : set) {
            if (!"com.yixun.wid.controller.admin".equals(cls.getClass().getPackage().getName())) {
                continue;
            }

            //类型
            String type = "";
            Api api = AnnotationUtils.findAnnotation(cls.getClass(), Api.class);
            if (api != null) {
                String[] tags = api.tags();
                if (tags.length > 0) {
                    type = tags[0];
                }
            }

            //获取各接口的方法注解
            Method[] methods = cls.getClass().getMethods();
            for (Method method : methods) {
                String label = "";
                String name = "";

                ApiOperation apiOperation = AnnotationUtils.findAnnotation(method, ApiOperation.class);
                if (apiOperation != null) {
                    label = apiOperation.value();

                    RequiredPermission requiredPermission = AnnotationUtils.findAnnotation(method, RequiredPermission.class);
                    if (requiredPermission != null) {
                        if (requiredPermission.value().length() > 0) {
                            name = requiredPermission.value();
                        }
                    } else {
                        continue;
                    }

                    Permission permission = new Permission();
                    permission.setName(name);
                    permission.setLabel(label);
                    permission.setType(type);
                    permissionList.add(permission);
                }
            }
        }

        return permissionList;
    }

}
