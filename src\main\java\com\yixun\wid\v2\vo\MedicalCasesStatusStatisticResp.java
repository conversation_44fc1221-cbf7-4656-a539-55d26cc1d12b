package com.yixun.wid.v2.vo;

import lombok.Data;

@Data
public class MedicalCasesStatusStatisticResp {

	/**
	 * 受理中案件数量
	 */
	private Long applying = 0L;

	/**
	 * 初审中案件数量
	 */
	private Long preReviewing = 0L;
	;

	/**
	 * 复审中案件数量
	 */
	private Long reviewing = 0L;
	;

	/**
	 * 终审中案件数量
	 */
	private Long finalReviewing = 0L;
	;

	/**
	 * 已办结案件数量
	 */
	private Long done = 0L;

	/**
	 * 业务总量
	 */
	private Long total = 0L;

	public Long getTotal() {
		return applying + preReviewing + reviewing + finalReviewing + done;
	}
}
