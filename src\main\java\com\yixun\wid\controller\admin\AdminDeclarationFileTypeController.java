package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.DeclarationFileType;
import com.yixun.wid.service.DeclarationFileTypeService;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "admin认定申请材料类型")
@RestController
@RequestMapping(value = "/admin/declarationFileType")
public class AdminDeclarationFileTypeController {

    @Resource
    private DeclarationFileTypeService declarationFileTypeService;

    @PostMapping("/save")
    @ApiOperation("保存认定申请材料类型")
    public CommonResult<Long> save(@RequestBody DeclarationFileType declarationFileType) {

        declarationFileType.setId(SnGeneratorUtil.getId());
        declarationFileTypeService.save(declarationFileType);

        return CommonResult.successResult("操作成功");
    }

}
