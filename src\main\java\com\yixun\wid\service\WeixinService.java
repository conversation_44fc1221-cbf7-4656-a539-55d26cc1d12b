package com.yixun.wid.service;

import com.yixun.wid.bean.weixin.AccessToken;
import com.yixun.wid.bean.weixin.MiniSession;
import com.yixun.wid.entity.em.UserType;
import com.yixun.wid.v2.vo.WxaCodeVO;

public interface WeixinService {

    MiniSession getMiniSessionByCode(String weixinCode);

    MiniSession getMiniSessionByOpenId(String openId);

	AccessToken getJSAccessToken(UserType userType);

	WxaCodeVO getWxaCode(String id);

}
