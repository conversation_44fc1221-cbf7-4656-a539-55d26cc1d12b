package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 限价目录实体
 */
@Data
public class PriceLimit {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的三目录实体主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long threeCatalogueId;

    /**
     * 项目名称（冗余，方便查询显示）
     */
    private String projectName;

    /**
     * 限价医院等级
     */
    private String priceLimitLevel;

    /**
     * 上限金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer maxPriceInCent;

    /**
     * 上限金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal maxPrice;

    /**
     * 当前有效状态: true-有效, false-无效
     */
    @Transient
    private Boolean effective;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 获取上限金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getMaxPrice() {
        if (maxPriceInCent == null) {
            return null;
        }
        return new BigDecimal(maxPriceInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置上限金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setMaxPrice(BigDecimal maxPrice) {
        if (maxPrice == null) {
            this.maxPriceInCent = null;
            return;
        }
        this.maxPriceInCent = maxPrice.multiply(new BigDecimal(100)).intValue();
    }
}
