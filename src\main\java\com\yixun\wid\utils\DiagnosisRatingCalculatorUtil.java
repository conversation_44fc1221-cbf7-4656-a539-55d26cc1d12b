package com.yixun.wid.utils;

public class DiagnosisRatingCalculatorUtil {

    public static float getTotalPoint(Integer safetyManagePoints, Integer occuHealthPoints, Integer adminPenalPoints,
            Integer injuryDataPoints, Float overallAverage, Integer competitivePoints) {

        safetyManagePoints = safetyManagePoints==null ? 0 : safetyManagePoints;
        occuHealthPoints = occuHealthPoints==null ? 0 : occuHealthPoints;
        adminPenalPoints = adminPenalPoints==null ? 0 : adminPenalPoints;
        injuryDataPoints = injuryDataPoints==null ? 0 : injuryDataPoints;
        overallAverage = overallAverage==null ? 0 : overallAverage;
        competitivePoints = competitivePoints==null ? 0 : competitivePoints;

        float total = 0f;

        if (overallAverage == 0) {
            total = safetyManagePoints * 0.4f
                    + occuHealthPoints * 0.2f
                    + adminPenalPoints * 0.1f
                    + injuryDataPoints * 0.2f
                    + overallAverage * 0.1f
                    + competitivePoints;
        } else {
            total = safetyManagePoints * 0.4f
                    + occuHealthPoints * 0.2f
                    + adminPenalPoints * 0.1f
                    + injuryDataPoints * 0.2f;
            total = total / 0.9f + competitivePoints;
        }

        return Math.round(total*100)/100f;
    }

    public static String getDiagnosisRatingChinese(float total) {
        String rating = null;

        if (total<=0){
            rating = "";
        }else if (total<60){
            rating = "红";
        }else if (total<80){
            rating = "橙";
        }else if (total<90){
            rating = "黄";
        }else {
            rating = "蓝";
        }

        return rating;
    }

    public static String getDiagnosisRatingEnglish(float total) {
        String rating = null;

        if (total<=0){
            rating = "";
        }else if (total<60){
            rating = "red";
        }else if (total<80){
            rating = "orange";
        }else if (total<90){
            rating = "yellow";
        }else {
            rating = "blue";
        }

        return rating;
    }
}
