package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.v2.vo.*;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 办事项目
 */
@Data
public class BizItem {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	// 办事项目概况

	/**
	 * 办事项目名称
	 */
	private String bizItemName;

	/**
	 * 项目别称
	 */
	private List<String> bizItemAliasName;

	/**
	 * 是否支持预约（0：不支持，1：支持）
	 */
	private Integer reservation;

	/**
	 * 是否支持在线办理（0：不支持，1：支持）
	 */
	private Integer onlineProcessing;

	/**
	 * 业务类型id
	 */
	private Long bizTypeId;

	/**
	 * 业务类型名称 回显使用
	 */
	private String bizTypeName;

	/**
	 * 办事项目简介
	 */
	private String bizItemDesc;

	// 基本信息

	/**
	 * 实施编码
	 */
	private String implCode;

	/**
	 * 服务对象
	 */
	private String serveTarget;

	/**
	 * 特别程序
	 */
	private String specialProgram;

	/**
	 * 业务统筹区
	 */
	private List bizCoordinationArea;

	/**
	 * 办事地点
	 */
	private List<OfficeLocationVO> officeLocations;

	/**
	 * 材料清单
	 */
	@Deprecated
	private MaterialsList materialsList;

	/**
	 * 办理流程说明
	 */
	private String processDesc;

	/**
	 * 办理流程说明图
	 */
	private String processDescUrl;

	/**
	 * 更多信息
	 */
	@Deprecated
	private ExtraInfo extraInfo;

	/**
	 * 禁用状态（0：未禁用，1：已禁用）
	 */
	private Integer status;

	/**
	 * 排序
	 */
	private Long sort;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date updateTime;

	/**
	 * 是否删除（0：未删除，1：已删除）
	 */
	private Integer deleted;

	// 材料清单

	/**
	 * 材料清单 是否具备材料要求 0否 1是
	 */
	private Integer materialRequirements;

	/**
	 * 材料清单 告知类文书材料 0否 1是
	 */
	private Integer notifyDoc;

	/**
	 * 材料清单 材料名称列表
	 */
	private List<NotifyDocMaterials>  notifyDocMaterials;

	/**
	 * 材料清单 清单生成条件 0否 1是
	 */
	private Integer listGenConditions;

	/**
	 * 材料清单 生成条件列表
	 */
	private List<GenConditions> conditions;

	/**
	 * 材料清单开关 0否 1是
	 */
	private Integer materialsSwitch;

	/**
	 * 材料清单 材料清单
	 */
	private List<Materials> materials;

	// 更多信息

	/**
	 * 是否收费 0否 1是
	 */
	private Integer charge;

	/**
	 * 收费标准
	 */
	private String chargeStandard;

	/**
	 * 是否支持手机支付 0否 1是
	 */
	private Integer phone;

	/**
	 * 数量限制
	 */
	private String numLimit;

	/**
	 * 禁止性要求
	 */
	private String prohibitionRequirements;

	/**
	 * 受理条件
	 */
	private String acceptanceConditions;

	/**
	 * 设定依据开关 0否 1是
	 */
	private Integer settingBasesSwitch;

	/**
	 * 法律法规名称
	 */
	private List<SettingBasis> settingBases;

	/**
	 * 常见问题开关 0否 1是
	 */
	private Integer frequentQuestionsSwitch;

	/**
	 * 常见问题
	 */
	private List<FrequentQuestions> frequentQuestions;

	/**
	 * 法律法规名称
	 */
	@Data
	public static class SettingBasis {

		private String regulationsName;

		private String content;

	}

	/**
	 * 常见问题
	 */
	@Data
	public static class FrequentQuestions {

		private String question;

		private String answer;

	}

	/**
	 * 告知类文书材料
	 */
	@Data
	public static class NotifyDocMaterials {

		/**
		 * 材料名称
		 */
		private String name;

		/**
		 * 材料地址
		 */
		private String url;

	}

	/**
	 * 材料
	 */
	@Data
	public static class Materials {

		/**
		 * 材料id
		 */
		private String id;

		/**
		 *  材料名称
		 */
		private String name;

		/**
		 * 材料来源 0政府部门核发 1申请人自备
		 */
		private Integer from;

		/**
		 * 是否必要 0否 1是
		 */
		private Integer necessary;

		/**
		 * 文件类型 0普通电子文件 1纸质或电子
		 */
		private Integer fileType;

		/**
		 * 文件模板
		 */
		private String docTemplates;

		/**
		 * 样表模板
		 */
		private String sampleFormTemplate;

		/**
		 * 条件配置
		 */
		private List<GenConditionsOptions> genConditionsOptions;

		/**
		 * 是否特殊 0否 1是
		 */
		private Integer special;

	}

	@Data
	public static class GenConditionsOptions {

		private String conditionName;

		private String optionName;

	}

}
