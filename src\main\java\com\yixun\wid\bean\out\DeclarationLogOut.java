package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class DeclarationLogOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long declarationId;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    private String type;
    private String name;
}
