package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeliveryFile {

    private Long id;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty("申报id")
    private Long declarationId;

    @ApiModelProperty("案件号")
    private String caseSn;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("环节 受理，认定")
    private String step;

    @ApiModelProperty("送达类别 邮寄，自领")
    private String type;

    @ApiModelProperty("事故时间")
    private Date accidentTime;

    @ApiModelProperty("上报人的用户id")
    private Long userId;

    @ApiModelProperty("职工姓名")
    private String name;

    @ApiModelProperty("是否已送达")
    private Boolean isConfirmed;

    @ApiModelProperty("送达时间")
    private Date confirmedTime;

    @ApiModelProperty("送达材料")
    private List fileList;

    @ApiModelProperty("邮寄送达")
    private List<MailFileItem> mailList;

    @ApiModelProperty("材料自领")
    private List selfList;
}
