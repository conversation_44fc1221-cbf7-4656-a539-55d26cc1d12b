package com.yixun.wid.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.wid.entity.Permission;
import com.yixun.wid.entity.Role;

import java.util.List;

public interface RolePermissionService {

    void insertRole(Role role);

    void updateRole(Role role);

    Role getRoleById(Long roleId);

    Role getRoleByName(String roleName);

    Page<Role> getAllRoleList(String label, CommonPage page);

    void deleteRole(Long roleId);

    void setRolePermissions(String role, List<String> permissionList);

    void cleanPermissionsOfRole(String role);

    void initPermission(List<Permission> permissionList);

    Permission getPermissionByName(String permissionName);

    List<Permission> getAllPermissionList();

    List<Permission> getPermissionListByRole(String role);

    List<Permission> getPermissionListByRoleList(List<String> roleList);

    List<Permission> loadPermissions();

}
