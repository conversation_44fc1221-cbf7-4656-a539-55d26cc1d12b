package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class Hospital implements Serializable {

    private Long id;

    private Date createTime;

    @ApiModelProperty(value = "医院名称")
    private String name;

    @ApiModelProperty(value = "医院简介")
    private String brief;

    @ApiModelProperty(value = "医院等级")
    private String grade;

    @ApiModelProperty(value = "省级区域")
    private String province;

    @ApiModelProperty(value = "市级区域")
    private String city;

    @ApiModelProperty(value = "区级区域")
    private String district;

    @ApiModelProperty(value = "医院地址")
    private String address;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

}