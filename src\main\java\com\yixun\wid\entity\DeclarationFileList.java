package com.yixun.wid.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "认定申请材料清单类型")
public class DeclarationFileList {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("张数")
    private Integer num;

    @ApiModelProperty("原件/复印件")
    private String copyFile;

    @ApiModelProperty("是否是该项有内容时才展示 1是 0否")
    private Integer showFlag;

    @ApiModelProperty("序号")
    private Integer orderNum;

    @ApiModelProperty("业务字段名称")
    private String columnName;

}