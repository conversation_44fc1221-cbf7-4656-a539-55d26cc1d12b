package com.yixun.wid.service.impl;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.ReceiveFileGetIn;
import com.yixun.wid.entity.ReceiveFile;
import com.yixun.wid.service.ReceiveFileService;
import com.yixun.wid.utils.MongoUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class ReceiveFileServiceImpl implements ReceiveFileService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public ReceiveFile getByDeclaration(Long declarationId, String type) {
        Query query = new Query();
        query.addCriteria(Criteria.where("declarationId").is(declarationId));
        query.addCriteria(Criteria.where("type").is(type));

        return mongoTemplate.findOne(query, ReceiveFile.class);
    }

    @Override
    public void save(ReceiveFile receiveFile) {
        mongoTemplate.save(receiveFile);
    }

    @Override
    public void update(ReceiveFile receiveFile) {
        receiveFile.setUpdateTime(new Date());
        mongoTemplate.save(receiveFile);
    }

    @Override
    public List<ReceiveFile> getList(ReceiveFileGetIn getIn, CommonPage commonPage) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isConfirmed").ne(true));

        // bugfix: 选择资料补充后，点击提交，收件列表会生成两条案件号一样的数据
        query.addCriteria(Criteria.where("type").is("material"));

        if (getIn.getName()!=null){
            query.addCriteria(Criteria.where("name").regex(getIn.getName()));
        }
        if (getIn.getStep()!=null){
            query.addCriteria(Criteria.where("step").is(getIn.getStep()));
        }
        if (getIn.getOrganization()!=null){
            query.addCriteria(Criteria.where("organization").regex(getIn.getOrganization()));
        }
        if (getIn.getStartDate()!=null&&getIn.getEndDate()!=null){
            Calendar c = Calendar.getInstance();
            c.setTime(getIn.getEndDate());
            c.add(Calendar.SECOND,86399); //结束时间加到当天的23:59:59
            query.addCriteria(Criteria.where("accidentTime").gte(getIn.getStartDate()).lte(c.getTime()));
        }

        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, ReceiveFile.class, query, commonPage);
        return mongoTemplate.find(query, ReceiveFile.class);
    }

    @Override
    public ReceiveFile getById(Long supplementaryFileId) {
        return mongoTemplate.findById(supplementaryFileId, ReceiveFile.class);
    }
}
