package com.yixun.wid.v2.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GeneralStatusEnum implements IEnum<Integer> {

	/**
	 * 否
	 */
	OFF(0, "否"),

	/**
	 * 是
	 */
	ON(1, "是"),
	;

	@JsonValue
	@EnumValue
	private final Integer code;

	private final String msg;

	@Override
	public Integer getValue() {
		return this.code;
	}
}
