package com.yixun.wid.v2.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.enums.CellDataTypeEnum;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
public class DictListStringConverter implements Converter<List> {

	@Override
	public Class<?> supportJavaTypeKey() {
		return List.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public WriteCellData<?> convertToExcelData(List value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		try {
			if (ObjectUtil.isNotEmpty(value)) {
				List<String> values = (List<String>) value.stream().map(o -> {
					Map o1 = (Map) o;
					return o1.get("label");
				}).collect(Collectors.toList());
				return new WriteCellData<>(StrUtil.join("、", values));
			} else {
				return new WriteCellData<>("");
			}
		} catch (Exception e) {
			// 处理异常
			return new WriteCellData<>("");
		}
	}

}
