package com.yixun.wid.v2.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserType implements IEnum<Integer> {

	/**
	 * 单选
	 */
	ADMIN(0, "管理员"),

	/**
	 * 判断
	 */
	USER(1, "用户"),

	;

	@JsonValue
	@EnumValue
	private final Integer code;

	private final String msg;

	@Override
	public Integer getValue() {
		return this.code;
	}
}
