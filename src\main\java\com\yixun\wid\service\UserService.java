package com.yixun.wid.service;

import com.yixun.wid.bean.out.UserInfoCache;
import com.yixun.wid.entity.User;

public interface UserService {

    void insert(User user);

    User getUserById(Long userId);

    UserInfoCache getUserInfoByIdFromCache(Long userId);

    void saveUserInfoCache(UserInfoCache userInfoCache);

    User getByPhone(String phone);

    void updateById(User user);

    User getWeixinByMiniOpenId(String openid);

	User getByIdCardType(String phone, String type);

}
