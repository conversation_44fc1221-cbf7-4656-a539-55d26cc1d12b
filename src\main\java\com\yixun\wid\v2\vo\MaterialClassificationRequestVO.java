package com.yixun.wid.v2.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 材料类型识别请求参数
 */
@Data
public class MaterialClassificationRequestVO {
    
    /**
     * 文件URL列表
     */
    @NotEmpty(message = "文件列表不能为空")
    private List<String> files;
    
    /**
     * 类型：application/medical
     */
    @NotNull(message = "类型不能为空")
    private String type;
    
    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private Long id;
}
