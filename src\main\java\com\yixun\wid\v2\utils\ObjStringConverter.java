package com.yixun.wid.v2.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

public class ObjStringConverter implements Converter<Object> {

	@Override
	public WriteCellData<?> convertToExcelData(Object value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (ObjectUtil.isEmpty(value)) {
			return new WriteCellData<>("否");
		} else {
			return new WriteCellData<>("是");
		}
	}

}
