package com.yixun.wid.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeclarationGetIn {

    private String name;
    private String idCard;
    private String injuredPart;
    private String organization;
    private Date startDate;
    private Date endDate;
    @ApiModelProperty("Applying(申报中),Accepting(待受理),Identifying(认定中),Classifying(鉴定中)," +
            "Done(已办结),Canceling(撤销中),Cancelled(已撤销),Rejected(已退回)")
    private List<String> statusList;
    @ApiModelProperty("FurtherInfo(补充资料),WaitMaterialSubmit(待实物提交),Suspended(时效终止)," +
            "Canceling(撤销申请),NotAccept(不予受理),NotIdentify(不予认定)")
    private List<String> subStatusList;
    @ApiModelProperty("是否查询所有(查询全部状态和子状态)")
    private Boolean isQueryAll;
}
