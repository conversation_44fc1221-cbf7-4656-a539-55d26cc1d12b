package com.yixun.wid.v2.vo;

import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.listener.ThreeCatalogueImportListener;
import org.junit.jupiter.api.Test;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.lang.reflect.Method;
import java.math.BigDecimal;

import static org.mockito.Mockito.mock;

/**
 * ThreeCatalogueImportVO maxPrice字段测试类
 * 测试从String类型转换为BigDecimal的验证逻辑
 */
public class ThreeCatalogueImportVOMaxPriceTest {

    @Test
    public void testValidMaxPriceConversion() throws Exception {
        // 测试有效的价格转换
        System.out.println("=== 测试有效的价格转换 ===");
        
        // 创建测试数据
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("100.50");
        
        // 使用反射调用私有方法进行测试
        PriceLimit result = callParsePriceLimit(importVO);
        
        // 验证结果
        assert result != null;
        assert result.getPriceLimitLevel().equals("三甲");
        assert result.getMaxPrice().compareTo(new BigDecimal("100.50")) == 0;
        
        System.out.println("测试通过：有效价格 '100.50' 转换成功");
        System.out.println("  转换结果: " + result.getMaxPrice());
    }

    @Test
    public void testValidIntegerMaxPrice() throws Exception {
        // 测试整数价格
        System.out.println("=== 测试整数价格 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三乙");
        importVO.setMaxPrice("200");
        
        PriceLimit result = callParsePriceLimit(importVO);
        
        assert result != null;
        assert result.getMaxPrice().compareTo(new BigDecimal("200")) == 0;
        
        System.out.println("测试通过：整数价格 '200' 转换成功");
        System.out.println("  转换结果: " + result.getMaxPrice());
    }

    @Test
    public void testEmptyMaxPrice() throws Exception {
        // 测试空价格
        System.out.println("=== 测试空价格 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("");
        
        PriceLimit result = callParsePriceLimit(importVO);
        
        assert result != null;
        assert result.getMaxPrice() == null;
        
        System.out.println("测试通过：空价格处理正确");
    }

    @Test
    public void testNullMaxPrice() throws Exception {
        // 测试null价格
        System.out.println("=== 测试null价格 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice(null);
        
        PriceLimit result = callParsePriceLimit(importVO);
        
        assert result != null;
        assert result.getMaxPrice() == null;
        
        System.out.println("测试通过：null价格处理正确");
    }

    @Test
    public void testInvalidMaxPriceFormat() throws Exception {
        // 测试无效的价格格式
        System.out.println("=== 测试无效的价格格式 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("abc");
        
        try {
            callParsePriceLimit(importVO);
            assert false : "应该抛出异常";
        } catch (Exception e) {
            assert e.getCause() instanceof IllegalArgumentException;
            assert e.getCause().getMessage().contains("定价上限金额格式不正确");
            System.out.println("测试通过：无效格式 'abc' 正确抛出异常");
            System.out.println("  异常信息: " + e.getCause().getMessage());
        }
    }

    @Test
    public void testNegativeMaxPrice() throws Exception {
        // 测试负数价格
        System.out.println("=== 测试负数价格 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("-50.00");
        
        try {
            callParsePriceLimit(importVO);
            assert false : "应该抛出异常";
        } catch (Exception e) {
            assert e.getCause() instanceof IllegalArgumentException;
            assert e.getCause().getMessage().contains("定价上限金额必须大于0");
            System.out.println("测试通过：负数价格 '-50.00' 正确抛出异常");
            System.out.println("  异常信息: " + e.getCause().getMessage());
        }
    }

    @Test
    public void testZeroMaxPrice() throws Exception {
        // 测试零价格
        System.out.println("=== 测试零价格 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("0");
        
        try {
            callParsePriceLimit(importVO);
            assert false : "应该抛出异常";
        } catch (Exception e) {
            assert e.getCause() instanceof IllegalArgumentException;
            assert e.getCause().getMessage().contains("定价上限金额必须大于0");
            System.out.println("测试通过：零价格 '0' 正确抛出异常");
            System.out.println("  异常信息: " + e.getCause().getMessage());
        }
    }

    @Test
    public void testTooManyDecimalPlaces() throws Exception {
        // 测试过多小数位
        System.out.println("=== 测试过多小数位 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        importVO.setPriceLimitLevel("三甲");
        importVO.setMaxPrice("100.123");
        
        try {
            callParsePriceLimit(importVO);
            assert false : "应该抛出异常";
        } catch (Exception e) {
            assert e.getCause() instanceof IllegalArgumentException;
            assert e.getCause().getMessage().contains("定价上限金额最多支持2位小数");
            System.out.println("测试通过：过多小数位 '100.123' 正确抛出异常");
            System.out.println("  异常信息: " + e.getCause().getMessage());
        }
    }

    @Test
    public void testNoLimitInfo() throws Exception {
        // 测试没有限价信息的情况
        System.out.println("=== 测试没有限价信息 ===");
        
        ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
        // 不设置priceLimitLevel和maxPrice
        
        PriceLimit result = callParsePriceLimit(importVO);
        
        assert result == null;
        
        System.out.println("测试通过：没有限价信息时返回null");
    }

    /**
     * 使用反射调用私有的parsePriceLimit方法
     */
    private PriceLimit callParsePriceLimit(ThreeCatalogueImportVO importVO) throws Exception {
        // 创建监听器实例
        MongoTemplate mockMongoTemplate = mock(MongoTemplate.class);
        ThreeCatalogueImportListener listener = new ThreeCatalogueImportListener(mockMongoTemplate);
        
        // 获取私有方法
        Method method = ThreeCatalogueImportListener.class.getDeclaredMethod("parsePriceLimit", ThreeCatalogueImportVO.class);
        method.setAccessible(true);
        
        // 调用方法
        return (PriceLimit) method.invoke(listener, importVO);
    }

    @Test
    public void testAllValidationScenarios() throws Exception {
        // 综合测试所有验证场景
        System.out.println("=== 综合验证测试 ===");
        
        // 测试各种有效的价格格式
        String[] validPrices = {"1", "1.0", "1.00", "99.99", "1000", "0.01"};
        
        for (String price : validPrices) {
            ThreeCatalogueImportVO importVO = new ThreeCatalogueImportVO();
            importVO.setPriceLimitLevel("三甲");
            importVO.setMaxPrice(price);
            
            PriceLimit result = callParsePriceLimit(importVO);
            assert result != null;
            assert result.getMaxPrice().compareTo(BigDecimal.ZERO) > 0;
            
            System.out.println("  有效价格 '" + price + "' -> " + result.getMaxPrice());
        }
        
        System.out.println("综合验证测试通过");
    }
}
