package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "admin文书编号")
@RestController
@RequestMapping(value = "/admin/sn")
public class AdminSnController {

    @Resource(name="customRedisTemplate")
    private RedisTemplate redisTemplate;

    @GetMapping("/getSn")
    @ApiOperation("获取报案详情列表")
    public CommonResult<String> getSn(Boolean isCity, String type) {

        String sn = SnGeneratorUtil.getSiChuanCaseSn(redisTemplate, isCity, type);

        return CommonResult.successData(sn);
    }

}
