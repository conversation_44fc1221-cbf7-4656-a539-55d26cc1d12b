package com.yixun.wid.v2.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 医院支付统计明细VO
 */
@Data
public class HospitalPaymentDetailVO {

    /**
     * 职工姓名
     */
    private String workerName;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 单位名称
     */
    private String organization;
    
    /**
     * 就诊次数
     */
    private Integer visitCount;
    
    /**
     * 就诊总金额（元）
     */
    private Double totalInvoiceAmount;
    
    /**
     * 可报销金额（元）
     */
    private Double totalReimbursableAmount;
    
    /**
     * 不可报销金额（元）
     */
    private Double totalNonReimbursableAmount;
    
    /**
     * 住院总天数
     */
    private Integer totalHospitalDays;
    
    /**
     * 住院伙食补助（元）
     */
    private Double totalHospitalFoodAllowance;
    
    /**
     * 实际支付金额（元）
     */
    private Double totalActualPayAmount;
    
    /**
     * 首次就诊日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date firstVisitDate;
    
    /**
     * 最近就诊日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date lastVisitDate;
} 