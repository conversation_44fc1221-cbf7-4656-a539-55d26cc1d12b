package com.yixun.wid.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.HospitalGetIn;
import com.yixun.wid.entity.Hospital;
import com.yixun.wid.service.HospitalService;
import com.yixun.wid.utils.MongoUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class HospitalServiceImpl implements HospitalService{

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void save(Hospital hospital) {
        mongoTemplate.save(hospital);
    }

    @Override
    public Hospital getById(Long hospitalId) {
        return mongoTemplate.findById(hospitalId, Hospital.class);
    }

    @Override
    public void update(Hospital hospital) {
        mongoTemplate.save(hospital);
    }

    @Override
    public void delete(Hospital hospital) {
        mongoTemplate.remove(hospital);
    }

    @Override
    public List<Hospital> getList(HospitalGetIn getIn, CommonPage commonPage) {

        Query query = new Query();
	    if (StrUtil.isNotBlank(getIn.getBrief())){
		    query.addCriteria(Criteria.where("brief").regex(getIn.getBrief()));
	    }
	    if (StrUtil.isNotBlank(getIn.getAddress())){
		    query.addCriteria(Criteria.where("address").regex(getIn.getAddress()));
	    }
        if (StrUtil.isNotBlank(getIn.getName())){
            query.addCriteria(Criteria.where("name").regex(getIn.getName()));
        }
        if (StrUtil.isNotBlank(getIn.getGrade())){
            query.addCriteria(Criteria.where("grade").is(getIn.getGrade()));
        }
	    if (StrUtil.isNotBlank(getIn.getDistrict())){
		    query.addCriteria(Criteria.where("district").regex(getIn.getDistrict()));
	    }
	    if (StrUtil.isNotBlank(getIn.getCity())){
		    query.addCriteria(Criteria.where("city").regex(getIn.getCity()));
	    }
	    if (StrUtil.isNotBlank(getIn.getProvince())){
		    query.addCriteria(Criteria.where("province").regex(getIn.getProvince()));
	    }

        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, Hospital.class, query, commonPage);
        return mongoTemplate.find(query, Hospital.class);
    }
}
