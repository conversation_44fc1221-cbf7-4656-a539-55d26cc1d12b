package com.yixun.wid.service.impl;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.InvestigationGetIn;
import com.yixun.wid.entity.Investigation;
import com.yixun.wid.service.InvestigationService;
import com.yixun.wid.utils.MongoUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class InvestigationServiceImpl implements InvestigationService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void save(Investigation investigation) {
        mongoTemplate.save(investigation);
    }

    @Override
    public Investigation getById(Long investigationId) {
        return mongoTemplate.findById(investigationId, Investigation.class);
    }

    @Override
    public void update(Investigation investigation) {
        investigation.setUpdateTime(new Date());
        mongoTemplate.save(investigation);
    }

    @Override
    public void delete(Investigation investigation) {
        mongoTemplate.remove(investigation);
    }

    @Override
    public List<Investigation> getList(InvestigationGetIn getIn, CommonPage commonPage) {
        Query query = new Query();
        if (getIn.getName()!=null){
            query.addCriteria(Criteria.where("name").regex(getIn.getName()));
        }
        if (getIn.getInjuredPart()!=null){
            query.addCriteria(Criteria.where("injuredPart").is(getIn.getInjuredPart()));
        }
        if (getIn.getOrganization()!=null){
            query.addCriteria(Criteria.where("organization").regex(getIn.getOrganization()));
        }
        if (getIn.getStatus()!=null){
            query.addCriteria(Criteria.where("status").is(getIn.getStatus().name()));
        }
        if (getIn.getStartDate()!=null&&getIn.getEndDate()!=null){
            Calendar c = Calendar.getInstance();
            c.setTime(getIn.getEndDate());
            c.add(Calendar.SECOND,86399); //结束时间加到当天的23:59:59
            query.addCriteria(Criteria.where("accidentTime").gte(getIn.getStartDate()).lte(c.getTime()));
        }

        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, Investigation.class, query, commonPage);
        return mongoTemplate.find(query, Investigation.class);
    }

    @Override
    public List<Investigation> getInvestigationStatistic(Date startTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").gte(startTime).lte(new Date()));
        return mongoTemplate.find(query, Investigation.class);
    }
}
