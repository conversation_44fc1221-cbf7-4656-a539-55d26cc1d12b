package com.yixun.wid.service;

import com.yixun.bean.in.MessageUpdateIn;
import com.yixun.wid.bean.in.SaveBatchMessageIn;
import com.yixun.wid.bean.in.WxMiniSendIn;
import com.yixun.wid.entity.Declaration;

import java.util.List;

public interface MessageService {

    void sendAppMessage(Long userId, String title, String content, Integer type, String redirectUrl, String businessType, String businessParam);

    void sendAppTask(String taskType, String title, Declaration declaration);

    void sendBatchMessage(SaveBatchMessageIn saveBatchMessageIn);

    /**
     * 设置消息或待办为已读
     *
     * @param messageUpdateIn
     */
    void setFinished(MessageUpdateIn messageUpdateIn);

    /**
     * 发送订阅消息
     * @param wxMiniSendIn
     */
    void sendWxMessage(WxMiniSendIn wxMiniSendIn);

    /**
     *
     * @param userId
     * @param msType
     * @param msTitle
     * @param msContent
     */
    void sendTrueWxMessage(Long userId, String msType, String msTitle, String msContent);

    /**
     * 发送PC端消息
     *
     * @param userIds
     * @param title
     * @param content
     * @param type
     * @param redirectUrl
     * @param businessType
     * @param businessParam
     */
    void sendAdminMessage(List<Long> userIds, String title, String content, Integer type, String redirectUrl, String businessType, String businessParam);

}
