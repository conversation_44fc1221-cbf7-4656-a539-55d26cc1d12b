package com.yixun.wid.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class SurveyDocInfo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("档案名称")
    private String docName;

    @ApiModelProperty("案件快照")
    private Cases caseSnapshot;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("发布人ID")
    private Long publishUserId;

    @ApiModelProperty("发布人姓名")
    private String publishUserName;

    @ApiModelProperty("文书档案总数")
    private Integer docTotal;

    @ApiModelProperty("影音档案总数")
    private Integer videoTotal;

    @ApiModelProperty("调查id")
    private Long surveyId;

    @ApiModelProperty("调查机构ID")
    private Long surveyOrgId;

    @ApiModelProperty("调查机构名称")
    private String surveyOrgName;

    @ApiModelProperty("文书档案列表")
    private List<Event> events;

    @ApiModelProperty("影音档案列表")
    private List<SurveyEvidenceDetailResponse> videoFile;

    @ApiModelProperty("调查人员列表")
    private List<SurveyUserBaseInfoResponse> surveyUsers;

    @ApiModelProperty("来源机构ID")
    private Long sourceOrgId;

    @ApiModelProperty("来源机构名称")
    private String sourceOrgName;

}
