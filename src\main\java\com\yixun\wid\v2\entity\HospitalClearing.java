package com.yixun.wid.v2.entity;

import lombok.Data;
import java.math.BigDecimal;
import org.springframework.data.annotation.Transient;
import java.util.List;

/**
 * 核销明细-住院
 * 按每次住院（每个住院账单）展示基本核算信息
 */
@Data
public class HospitalClearing {
    
    /**
     * 住院核销明细项列表
     */
    private List<HospitalClearingItem> clearingItems;
    
    /**
     * 总发票金额（分）
     */
    private Integer totalInvoiceAmountInCent;
    
    /**
     * 总可报销金额（分）
     */
    private Integer totalReimbursableAmountInCent;
    
    /**
     * 总不可报销金额（分）
     */
    private Integer totalNonReimbursableAmountInCent;
    
    /**
     * 住院总天数
     */
    private Integer totalHospitalDays;
    
    /**
     * 住院伙食补助总金额（分）
     */
    private Integer totalHospitalFoodAllowanceInCent;
    
    /**
     * 总应付金额（分）
     */
    private Integer totalPayableAmountInCent;
    
    /**
     * 总发票金额（元）
     */
    @Transient
    private BigDecimal totalInvoiceAmount;
    
    /**
     * 总可报销金额（元）
     */
    @Transient
    private BigDecimal totalReimbursableAmount;
    
    /**
     * 总不可报销金额（元）
     */
    @Transient
    private BigDecimal totalNonReimbursableAmount;
    
    /**
     * 住院伙食补助总金额（元）
     */
    @Transient
    private BigDecimal totalHospitalFoodAllowance;
    
    /**
     * 总应付金额（元）
     */
    @Transient
    private BigDecimal totalPayableAmount;
    
    /**
     * 计算汇总金额
     * 根据明细项计算总发票金额、总可报销金额、总不可报销金额、住院总天数、住院伙食补助总金额、总应付金额
     */
    public void calculateTotals() {
        if (clearingItems == null || clearingItems.isEmpty()) {
            totalInvoiceAmountInCent = 0;
            totalReimbursableAmountInCent = 0;
            totalNonReimbursableAmountInCent = 0;
            totalHospitalDays = 0;
            totalHospitalFoodAllowanceInCent = 0;
            totalPayableAmountInCent = 0;
            return;
        }
        
        totalInvoiceAmountInCent = 0;
        totalReimbursableAmountInCent = 0;
        totalNonReimbursableAmountInCent = 0;
        totalHospitalDays = 0;
        totalHospitalFoodAllowanceInCent = 0;
        totalPayableAmountInCent = 0;
        
        for (HospitalClearingItem item : clearingItems) {
            if (item.getInvoiceAmountInCent() != null) {
                totalInvoiceAmountInCent += item.getInvoiceAmountInCent();
            }
            
            if (item.getReimbursableAmountInCent() != null) {
                totalReimbursableAmountInCent += item.getReimbursableAmountInCent();
            }
            
            if (item.getNonReimbursableAmountInCent() != null) {
                totalNonReimbursableAmountInCent += item.getNonReimbursableAmountInCent();
            }
            
            if (item.getHospitalDays() != null) {
                totalHospitalDays += item.getHospitalDays();
            }
            
            if (item.getHospitalFoodAllowanceInCent() != null) {
                totalHospitalFoodAllowanceInCent += item.getHospitalFoodAllowanceInCent();
            }
            
            if (item.getTotalPayableAmountInCent() != null) {
                totalPayableAmountInCent += item.getTotalPayableAmountInCent();
            }
        }
    }
    
    // 元分转换方法
    public BigDecimal getTotalInvoiceAmount() {
        if (totalInvoiceAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalInvoiceAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalInvoiceAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalInvoiceAmountInCent = null;
        } else {
            this.totalInvoiceAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalReimbursableAmount() {
        if (totalReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalReimbursableAmountInCent = null;
        } else {
            this.totalReimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalNonReimbursableAmount() {
        if (totalNonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalNonReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalNonReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalNonReimbursableAmountInCent = null;
        } else {
            this.totalNonReimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalHospitalFoodAllowance() {
        if (totalHospitalFoodAllowanceInCent == null) {
            return null;
        }
        return new BigDecimal(totalHospitalFoodAllowanceInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalHospitalFoodAllowance(BigDecimal amount) {
        if (amount == null) {
            this.totalHospitalFoodAllowanceInCent = null;
        } else {
            this.totalHospitalFoodAllowanceInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
    
    public BigDecimal getTotalPayableAmount() {
        if (totalPayableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalPayableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalPayableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalPayableAmountInCent = null;
        } else {
            this.totalPayableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
} 