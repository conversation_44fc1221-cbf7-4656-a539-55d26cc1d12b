package com.yixun.wid.bean.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.entity.DiagnosticInfo;
import com.yixun.wid.utils.DateJsonSerializer;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(value = "案件输出对象")
@Data
public class CasesOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @ApiModelProperty(value = "案件号")
    private String caseSn;

    @ApiModelProperty(value = "提交方式")
    private String submitWay;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "关系")
    private String relationship;

    @ApiModelProperty(value = "关系详情")
    private String relationshipDetail;

    @ApiModelProperty(value = "上报人名称")
    private String reportName;

    @ApiModelProperty(value = "上报人电话")
    private String reportPhone;

    @ApiModelProperty(value = "案件状态（Submitted: '待申报' Applying: '申报中' Applied: '已申报' Accepting: '待受理' Identifying: '认定中' Classifying: '鉴定中' Cancelled: '已撤销' Canceling: '撤销中' Rejected: '已退回' Done: '已办结',）")
    private String status;

    @ApiModelProperty(value = "是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty(value = "参保地址")
    private String insuranceAddress;

    @ApiModelProperty(value = "受害职工姓名")
    private String name;

    @ApiModelProperty(value = "受害职工性别")
    private String gender;

    @ApiModelProperty(value = "受害职工出生日期")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date birthdayDate;

    @ApiModelProperty(value = "受害职工身份证")
    private String idCard;

    @ApiModelProperty(value = "受害职工身份证地址")
    private String idCardAddr;

    @ApiModelProperty(value = "受害职工电话")
    private String phone;

    @ApiModelProperty(value = "受害职工单位id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long organizationId;

    @ApiModelProperty(value = "受害职工单位名称")
    private String organization;

    @ApiModelProperty(value = "社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "受害职工 职业、工种或工作岗位")
    private List position;

    @ApiModelProperty(value = "参加工作时间")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date firstWorkDate;

    @ApiModelProperty(value = "单位所在区域")
    private List organizationRegion;

    @ApiModelProperty(value = "详细地址")
    private String organizationAddr;

    @ApiModelProperty("单位地址经度")
    private Double organizationLongitude;

    @ApiModelProperty("单位地址纬度")
    private Double organizationLatitude;

    private String zipCode;

    @ApiModelProperty(value = "伤害部位")
    private List injuredPart;

    private String diseaseName;

    @ApiModelProperty(value = "事故发生时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date accidentTime;

    @ApiModelProperty(value = "事故发生地区")
    private List accidentRegion;

    @ApiModelProperty(value = "事故发生详细地址")
    private String accidentAddress;

    @ApiModelProperty(value = "事故发生地点经度")
    private Double longitude;

    @ApiModelProperty(value = "事故发生地点纬度")
    private Double latitude;

    @ApiModelProperty(value = "上报地点")
    private List reportRegion;

    @ApiModelProperty(value = "上报详细地址")
    private String reportAddress;

    @ApiModelProperty(value = "上报地点经度")
    private Double reportLongitude;

    @ApiModelProperty(value = "上报地点纬度")
    private Double reportLatitude;

    @ApiModelProperty(value = "事故详情")
    private String accidentDetail;

    @ApiModelProperty(value = "事故原因")
    private String accidentCause;

    @ApiModelProperty(value = "事故发生时从事的工作")
    private String workingOfAccident;

    @ApiModelProperty(value = "补充说明")
    private String supplementary;

    @ApiModelProperty(value = "涉及人数")
    private String involvedNumber;

    @ApiModelProperty(value = "是否职业病相关")
    private Boolean isOccupDiseaseRelated;

    @ApiModelProperty(value = "职业病名称")
    private String occupDiseaseName;

    @ApiModelProperty(value = "接触职业病危害岗位")
    private String occupDiseasePosition;

    @ApiModelProperty(value = "接触职业病危害时间")
    private String occupDiseaseDate;

    @ApiModelProperty(value = "职业病诊断证明或鉴定书")
    private List occupDiseaseDiagnose;

    @ApiModelProperty(value = "现场图片")
    private List pictures;

    @ApiModelProperty(value = "现场视频")
    private List videos;

    @ApiModelProperty(value = "受伤害部位图片")
    private List injuredPartPics;

    private List reportFormPics;

    @ApiModelProperty(value = "材料提交方式")
    private String materialSubmitWay;

    @ApiModelProperty(value = "是否无需申报")
    private Boolean isNoNeedApply;

    @ApiModelProperty(value = "无需申报原因")
    private String noNeedReason;

    @ApiModelProperty(value = "无需申报原因（其他）")
    private String noNeedReasonOther;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("案件申请时间")
    private Date submitTime;

	@ApiModelProperty("诊断信息")
	private List<DiagnosticInfo> diagnosticInfoList;

}
