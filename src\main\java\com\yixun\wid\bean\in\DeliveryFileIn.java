package com.yixun.wid.bean.in;

import com.yixun.wid.entity.MailFileItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DeliveryFileIn {

    @ApiModelProperty("申报id")
    private Long declarationId;

    @ApiModelProperty("环节 受理，认定")
    private String step;

    @ApiModelProperty("送达类别 邮寄，自领")
    private String type;

    @ApiModelProperty("是否已送达")
    private Boolean isConfirmed;

    @ApiModelProperty("送达材料")
    private List fileList;

    @ApiModelProperty("邮寄送达")
    private List<MailFileItem> mailList;

    @ApiModelProperty("材料自领")
    private List selfList;
}
