package com.yixun.wid.bean.other;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(value = "消息对象定义")
@Data
public class MessageDefine {

    @ApiModelProperty("申报的id")
    private Long id;

    @ApiModelProperty("职工姓名")
    private String name;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("伤害部位")
    private List injuredPart;

    @ApiModelProperty("事故时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentTime;

    @ApiModelProperty("事故地点")
    private String accidentAddress;

    @ApiModelProperty("材料提交方式")
    private String materialSubmitWay;

}
