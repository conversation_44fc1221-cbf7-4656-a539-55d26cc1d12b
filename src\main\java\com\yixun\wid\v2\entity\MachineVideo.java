package com.yixun.wid.v2.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.validation.constraints.NotNull;

@Data
public class MachineVideo {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 视频标题
	 */
	@NotNull(message = "视频标题不能为空")
	private String name;

	/**
	 * 视频地址
	 */
	@NotNull(message = "视频地址不能为空")
	private String videoUrl;

	/**
	 * 视频时长
	 */
	private String videoTime;

	/**
     * 封面地址
	 */
	@NotNull(message  = "封面地址不能为空")
	private String coverUrl;

	/**
	 * 是否启用
	 */
	@NotNull(message  = "发布状态不能为空")
	private Boolean enabled;

}
