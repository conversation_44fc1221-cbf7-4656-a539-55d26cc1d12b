package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Investigation {

    private Long id;

    @ApiModelProperty(value = "签发时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "申报id")
    private Long declarationId;

    @ApiModelProperty(value = "调查状态")
    private String status;

    @ApiModelProperty(value = "接单员id")
    private Long userId;

    @ApiModelProperty(value = "政府机构名称")
    private String government;

    @ApiModelProperty(value = "政府签发负责人")
    private String issueOfficer;

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "单位id")
    private Long organizationId;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "受伤部位")
    private List injuredPart;

    @ApiModelProperty(value = "事故时间")
    private Date accidentTime;

    @ApiModelProperty(value = "第三方辅助调查情况")
    private ThirdPartyInfo thirdPartyInfo;

    @ApiModelProperty(value = "证据登记")
    private EvidenceInfo evidenceInfo;

    @ApiModelProperty(value = "工伤事故辅助调查证据清单")
    private InvestigateEvidenceList investigateEvidenceList;

}
