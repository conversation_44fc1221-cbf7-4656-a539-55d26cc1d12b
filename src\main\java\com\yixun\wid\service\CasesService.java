package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.CasesGetIn;
import com.yixun.wid.bean.out.CasesStatisticListOut;
import com.yixun.wid.bean.out.RelateCasesOut;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.CasesLog;

import java.util.Date;
import java.util.List;

public interface CasesService {

    void save(Cases cases);
    void update(Cases cases);

    List<Cases> getCasesList(CasesGetIn userId, CommonPage commonPage);

    Cases getById(Long casesId);

    void setCasesLog(Long casesId, String casesLog);

    List<CasesLog> getCasesLogList(Long casesId, CommonPage commonPage);

    String getCaseSn();

    void updateCorpName(String companyName, Long id);

    List<Cases> getCasesStatistic(Date startTime);

    List<Cases> getByUserAccidentDate(Long id, String idCard, Date accidentTime);

    List<CasesStatisticListOut> getCasesStatisticList(String organization, CommonPage commonPage);

    List<Cases> getByIdList(List<Long> caseIds);

    /**
     * 发送超过15天未申报的订阅消息
     */
    void sendOver15DayMs();

    /**
     * 获取关联事故数据
     * @param casesId
     * @return
     */
    List<RelateCasesOut> getRelateCase(Long casesId);

}
