package com.yixun.wid.v2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.wid.v2.bean.in.ProjectFeeSimilarSearchIn;
import com.yixun.wid.v2.entity.MedicalInstitutions;
import com.yixun.wid.v2.entity.ProjectFee;
import com.yixun.wid.v2.service.ProjectFeeService;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.ai.SimilarityResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目费用服务实现类
 */
@Slf4j
@Service
public class ProjectFeeServiceImpl implements ProjectFeeService {
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Resource
    private AiUtils aiUtils;
    
    @Override
    public ProjectFee similarSearch(ProjectFeeSimilarSearchIn searchIn) {
        try {
            // 构建查询条件
            Query query = new Query();
            
            // 根据医院标识筛选项目费用
            List<Long> hospitalIds = getHospitalIds(searchIn);
            if (CollUtil.isNotEmpty(hospitalIds)) {
                query.addCriteria(Criteria.where("hospitalId").in(hospitalIds));
            }
            
            // 查询项目费用数据，只获取项目名称字段用于相似度比较
            query.fields().include("projectName");
            List<ProjectFee> projectFees = mongoTemplate.find(query, ProjectFee.class);
            
            if (CollUtil.isEmpty(projectFees)) {
                log.info("未找到匹配的项目费用数据，搜索条件：{}", searchIn);
                return null;
            }
            
            // 提取项目名称列表
            List<String> targetNames = projectFees.stream()
                    .map(ProjectFee::getProjectName)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(targetNames)) {
                log.info("项目费用数据中未找到有效的项目名称，搜索条件：{}", searchIn);
                return null;
            }
            
            // 使用AI工具进行相似度匹配
            List<SimilarityResult> similarResults = aiUtils.findSimilarItems(
                    searchIn.getProjectName(), 
                    targetNames, 
                    searchIn.getTopK(), 
                    searchIn.getSimilarityThreshold()
            );
            
            if (CollUtil.isEmpty(similarResults)) {
                log.info("未找到相似度满足条件的项目费用项目，搜索条件：{}", searchIn);
                return null;
            }
            
            // 获取相似度最高的项目名称
            String mostSimilarProjectName = similarResults.get(0).getName();
            log.info("找到最相似的项目名称：{}，相似度：{}", mostSimilarProjectName, similarResults.get(0).getSimilarity());
            
            // 根据项目名称查询完整的项目费用详情
            Query detailQuery = new Query();
            detailQuery.addCriteria(Criteria.where("projectName").is(mostSimilarProjectName));
            
            // 如果指定了医院标识，也要加入查询条件
            if (CollUtil.isNotEmpty(hospitalIds)) {
                detailQuery.addCriteria(Criteria.where("hospitalId").in(hospitalIds));
            }
            
            // 按创建时间倒序，获取最新的一条记录
            detailQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));
            
            ProjectFee result = mongoTemplate.findOne(detailQuery, ProjectFee.class);
            
            if (result == null) {
                log.warn("根据项目名称未找到匹配的项目费用详情，项目名称：{}，搜索条件：{}", mostSimilarProjectName, searchIn);
                return null;
            }
            
            log.info("项目费用相似搜索成功，返回结果：{}", result.getProjectName());
            return result;
            
        } catch (Exception e) {
            log.error("项目费用相似搜索异常，搜索条件：{}", searchIn, e);
            return null;
        }
    }
    
    /**
     * 根据医院标识获取医院ID列表
     * 支持医院ID、医院名称、医院别名三种方式
     */
    private List<Long> getHospitalIds(ProjectFeeSimilarSearchIn searchIn) {
        List<Long> hospitalIds = new ArrayList<>();
        
        // 如果直接提供了医院ID
        if (searchIn.getHospitalId() != null) {
            hospitalIds.add(searchIn.getHospitalId());
            return hospitalIds;
        }
        
        // 如果提供了医院名称或别名，需要查询医疗机构表
        if (StrUtil.isNotBlank(searchIn.getHospitalName()) || StrUtil.isNotBlank(searchIn.getHospitalAlias())) {
            Query hospitalQuery = new Query();
            
            if (StrUtil.isNotBlank(searchIn.getHospitalName())) {
                hospitalQuery.addCriteria(Criteria.where("name").is(searchIn.getHospitalName()));
            } else if (StrUtil.isNotBlank(searchIn.getHospitalAlias())) {
                hospitalQuery.addCriteria(Criteria.where("aliases").in(searchIn.getHospitalAlias()));
            }
            
            hospitalQuery.fields().include("id");
            List<MedicalInstitutions> hospitals = mongoTemplate.find(hospitalQuery, MedicalInstitutions.class);
            
            if (CollUtil.isNotEmpty(hospitals)) {
                hospitalIds = hospitals.stream()
                        .map(MedicalInstitutions::getId)
                        .collect(Collectors.toList());
                log.info("根据医院标识找到医院ID列表：{}，搜索条件：医院名称={}，医院别名={}", 
                        hospitalIds, searchIn.getHospitalName(), searchIn.getHospitalAlias());
            } else {
                log.info("根据医院标识未找到匹配的医院，搜索条件：医院名称={}，医院别名={}", 
                        searchIn.getHospitalName(), searchIn.getHospitalAlias());
            }
        }
        
        return hospitalIds;
    }
}
