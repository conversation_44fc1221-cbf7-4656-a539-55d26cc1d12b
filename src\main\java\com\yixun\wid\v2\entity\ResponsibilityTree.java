package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 责任列表树形结构
 */
@Data
//@Document(collection = "responsibilityTree")
public class ResponsibilityTree {

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 父级ID，根节点为0
     */
    private Long parentId;

    /**
     * 责任名称
     */
    private String name;

    /**
     * 责任代码
     */
    private String code;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 子节点列表，不存入数据库，仅用于返回树形结构
     */
    @Transient
    private List<ResponsibilityTree> children = new ArrayList<>();
}
