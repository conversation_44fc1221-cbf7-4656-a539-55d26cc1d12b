package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.support.ExcelTypeEnum;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.fill.FillConfig;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.*;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.ReceiveFileService;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.BizItem;
import com.yixun.wid.v2.entity.MaterialsListPO;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 材料相关接口
 */
@SuppressWarnings({"DuplicatedCode", "rawtypes"})
@Slf4j
@RequestMapping("/v2/materials")
@Controller
public class MaterialsController {

	public static final ClassPathResource CLASS_PATH_RESOURCE = new ClassPathResource("file/工伤认定申请材料接收清单-已生成.xlsx");

	public static final ClassPathResource CASES_MATERIALS_LIST_RESOURCE = new ClassPathResource("file/报案清单.xlsx");

	public static final ClassPathResource DECLARATION_MATERIALS_LIST_RESOURCE = new ClassPathResource("file/工伤认定申请清单.xlsx");

	@Value("${filePath}")
	private String filePath;

	@Resource
	private MongoTemplate mongoTemplate;

	@Resource
	private CasesService casesService;

	@Resource
	private ReceiveFileService receiveFileService;

	private void downloadFile(List list, File file, String name) {
		if (ObjectUtil.isEmpty(list)) {
			return;
		}
		for (int i = 0; i < list.size(); i++) {
			Map map = (Map) list.get(i);
			if (ObjectUtil.isEmpty(map)) {
				return;
			}
			String url = (String) map.get("url");
			String extension = FilenameUtils.getExtension(url);
			HttpUtil.downloadFile(url, new File(file, name + (i + 1) + "." + extension));
		}
	}

	private void downloadDeclarationMaterials(List declarationMaterials, File file) {
		if (ObjectUtil.isEmpty(declarationMaterials)) {
			return;
		}
		for (Object declarationMaterial : declarationMaterials) {
			Map map = (Map) declarationMaterial;
			if (ObjectUtil.isEmpty(map)) {
				return;
			}
			List fileList = (List) map.get("fileList");
			if (ObjectUtil.isEmpty(fileList)) {
				return;
			}
			String label = (String) map.get("label");

			for (int i = 0; i < fileList.size(); i++) {
				Map fileMap = (Map) fileList.get(i);
				if (ObjectUtil.isEmpty(fileMap)) {
					return;
				}
				String url = (String) fileMap.get("url");
				String extension = FilenameUtils.getExtension(url);
				HttpUtil.downloadFile(url, new File(file, label + (i + 1) + "." + extension));
			}
		}
	}

	/**
	 * 查询材料清单列表
	 * @param commonPage 分页参数
	 * @return 材料清单列表
	 */
	@ResponseBody
	@GetMapping("/list")
	public CommonResult<List<MaterialsListPO>> getMaterialsList(CommonPage commonPage) {
		Query query = new Query();
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));
		MongoUtil.setPageInfo(mongoTemplate, MaterialsListPO.class, query, commonPage);
		List<MaterialsListPO> materialsListPOS = mongoTemplate.find(query, MaterialsListPO.class);
		return CommonResult.successPageData(materialsListPOS, commonPage);
	}

	/**
	 * 根据条件生成材料清单
	 * @param materialsListVO 材料清单信息
	 * @return 操作结果
	 */
	@ResponseBody
	@PostMapping("/export/list")
	public CommonResult<Void> exportMaterialsList(@RequestBody MaterialsListVO materialsListVO) {
		String materialsListType = materialsListVO.getMaterialsListType();
		Date startTime = materialsListVO.getStartTime();
		String startTimeFormat = DateUtil.format(startTime, DatePattern.PURE_DATE_PATTERN);
		Date endTime = materialsListVO.getEndTime();
		String endTimeFormat = DateUtil.format(endTime, DatePattern.PURE_DATE_PATTERN);
		List<String> area = materialsListVO.getArea();

		WriteCellStyle headStyle = new WriteCellStyle();
		headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headStyle.setWrapped(true);

		WriteCellStyle contentStyle = new WriteCellStyle();
		contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentStyle.setWrapped(true);

		HorizontalCellStyleStrategy styleStrategy =
			new HorizontalCellStyleStrategy(headStyle, contentStyle);

		if (materialsListType.equals("事故报备清单")) {
			Query query = new Query();
			query.addCriteria(Criteria.where("submitTime").gte(startTime).lte(endTime));
			if (ObjectUtil.isEmpty(area)) {
				query.addCriteria(new Criteria().orOperator(
					Criteria.where("insuranceAddress").isNull(),
					Criteria.where("insuranceAddress").is("")));
			} else {
				query.addCriteria(Criteria.where("insuranceAddress").in(area));
			}
//			Criteria criteria = new Criteria().andOperator(
//				Criteria.where("submitTime").gte(startTime).lte(endTime),
//				new Criteria().orOperator(
//					Criteria.where("insuranceAddress").in(area),
//					Criteria.where("insuranceAddress").isNull(),
//					Criteria.where("insuranceAddress").is("")
//				)
//			);
//			query.addCriteria(criteria);
			List<Cases> cases = mongoTemplate.find(query, Cases.class);

			Long id = SnGeneratorUtil.getId();
			File file = FileUtil.newFile(filePath + "materialsList/" + id + "-" + "事故报备清单" + startTimeFormat + "-" + endTimeFormat + ".xlsx");

			List<CasesExport> casesExports = BeanUtil.copyToList(cases, CasesExport.class);
			try (ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(file), CasesExport.class)
				.needHead(false)
				.registerWriteHandler(styleStrategy)
				.withTemplate(CASES_MATERIALS_LIST_RESOURCE.getInputStream())
				.excelType(ExcelTypeEnum.XLSX).build()) {
				WriteSheet writeSheet = EasyExcel.writerSheet().build();
				FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
				excelWriter.fill(casesExports, fillConfig, writeSheet);
				excelWriter.finish();
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

			MaterialsListPO materialsListPO = new MaterialsListPO();
			materialsListPO.setId(id);
			materialsListPO.setArea(area);
			materialsListPO.setName("事故报备清单" + startTimeFormat + "-" + endTimeFormat);
			materialsListPO.setStartTime(startTime);
			materialsListPO.setEndTime(endTime);
			materialsListPO.setFile("materialsList/" + id + "-" +  "事故报备清单" + startTimeFormat + "-" + endTimeFormat + ".xlsx");
			materialsListPO.setCreateTime(new Date());
			materialsListPO.setFileExpired(false);

			mongoTemplate.save(materialsListPO);
		}

		if (materialsListType.equals("工伤认定申请清单")) {
			Query query = new Query();
			query.addCriteria(Criteria.where("submitTime").gte(startTime).lte(endTime));
			if (ObjectUtil.isEmpty(area)) {
				query.addCriteria(new Criteria().orOperator(
					Criteria.where("insuranceAddress").isNull(),
					Criteria.where("insuranceAddress").is("")));
			} else {
				query.addCriteria(Criteria.where("insuranceAddress").in(area));
			}
//			Criteria criteria = new Criteria().andOperator(
//				Criteria.where("submitTime").gte(startTime).lte(endTime),
//				new Criteria().orOperator(
//					Criteria.where("insuranceAddress").in(area),
//					Criteria.where("insuranceAddress").isNull(),
//					Criteria.where("insuranceAddress").is("")
//				)
//			);
//			query.addCriteria(criteria);

			List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);

			Long id = SnGeneratorUtil.getId();
			File file = FileUtil.newFile(filePath + "materialsList/" + id + "-" + "工伤认定申请清单" + startTimeFormat + "-" + endTimeFormat + ".xlsx");

			List<DeclarationExport> declarationExports = BeanUtil.copyToList(declarations, DeclarationExport.class);

			for (DeclarationExport declarationExport : declarationExports) {

				// 处理职业病危害时间
				String occupDiseaseDate = declarationExport.getOccupDiseaseDate();
				if (StrUtil.isNotBlank(occupDiseaseDate)) {
					String[] split = occupDiseaseDate.split("~");
					declarationExport.setOccupDiseaseDateStart(split[0]);
					declarationExport.setOccupDiseaseDateEnd(split[1]);
				}
				// 处理诊断信息
				List<DiagnosticInfo> diagnosticInfoList = declarationExport.getDiagnosticInfoList();
				if (ObjectUtil.isNotEmpty(diagnosticInfoList)) {
					DiagnosticInfo diagnosticInfo = diagnosticInfoList.get(0);
					declarationExport.setDiagnosticInfo(diagnosticInfo);
					List<DiagnosticInfo> sub = CollectionUtil.sub(diagnosticInfoList, 1, diagnosticInfoList.size());
					List<String> otherDiagnosticInfos = new ArrayList<>();
					if (ObjectUtil.isNotEmpty(sub)) {
						for (DiagnosticInfo info : sub) {
							String diagnoses = StrUtil.join(",", info.getDiagnoses());
							otherDiagnosticInfos.add(StrUtil.join(",", DateUtil.formatDate(info.getFirstClinicDate()), info.getHospital(), diagnoses));
						}
					}
					declarationExport.setOtherDiagnosticInfo(StrUtil.join(";", otherDiagnosticInfos));
				}

				// 处理补充资料
				ReceiveFile receiveFile = receiveFileService.getByDeclaration(declarationExport.getId(), "virtual");
				if (ObjectUtil.isNull(receiveFile) || ObjectUtil.isNull(receiveFile.getId())) {
					declarationExport.setIsReceiveFile(false);
				} else {
					declarationExport.setIsReceiveFile(true);
					declarationExport.setReceiveFileWritSn(receiveFile.getWritSn());
				}

				// 处理认定终止
				SuspendedConclusion suspendedConclusion = declarationExport.getSuspendedConclusion();
				declarationExport.setIsSuspendedConclusion(!ObjectUtil.isNull(suspendedConclusion));
			}

			List<Long> caseIds = declarationExports.stream().map(DeclarationExport::getCasesId).collect(Collectors.toList());
			List<Cases> byIdList = casesService.getByIdList(caseIds);
			List<CasesExport> casesExports = BeanUtil.copyToList(byIdList, CasesExport.class);
			Map<Long, CasesExport> collect = casesExports.stream().collect(Collectors.toMap(cases -> cases.getId(), Function.identity()));
			for (DeclarationExport declarationExport : declarationExports) {
				Long casesId = declarationExport.getCasesId();
				if (ObjectUtil.isNotNull(casesId)) {
					CasesExport cases = collect.get(casesId);
					if (ObjectUtil.isNotNull(cases)) {
						declarationExport.setCasesExport(cases);
					}
				}
			}

			try (ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(file), DeclarationExport.class)
				.needHead(false)
				.registerWriteHandler(styleStrategy)
				.withTemplate(DECLARATION_MATERIALS_LIST_RESOURCE.getInputStream())
				.excelType(ExcelTypeEnum.XLSX).build()) {
				WriteSheet writeSheet = EasyExcel.writerSheet().build();
				FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
				excelWriter.fill(declarationExports, fillConfig, writeSheet);
				excelWriter.finish();
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

			MaterialsListPO materialsListPO = new MaterialsListPO();
			materialsListPO.setId(id);
			materialsListPO.setArea(area);
			materialsListPO.setName("工伤认定申请清单" + startTimeFormat + "-" + endTimeFormat);
			materialsListPO.setStartTime(startTime);
			materialsListPO.setEndTime(endTime);
			materialsListPO.setFile("materialsList/" + id + "-" +  "工伤认定申请清单" + startTimeFormat + "-" + endTimeFormat + ".xlsx");
			materialsListPO.setCreateTime(new Date());
			materialsListPO.setFileExpired(false);

			mongoTemplate.save(materialsListPO);
		}

		if (materialsListType.equals("工伤认定申请材料-已办结")) {
			Query query = new Query();
			query.addCriteria(Criteria.where("submitTime").gte(startTime).lte(endTime));
			query.addCriteria(Criteria.where("status").is(DeclarationStatus.Done.toString()));
			if (ObjectUtil.isEmpty(area)) {
				query.addCriteria(new Criteria().orOperator(
					Criteria.where("insuranceAddress").isNull(),
					Criteria.where("insuranceAddress").is("")));
			} else {
				query.addCriteria(Criteria.where("insuranceAddress").in(area));
			}
//
//			Criteria criteria = new Criteria().andOperator(
//				Criteria.where("submitTime").gte(startTime).lte(endTime),
//				new Criteria().orOperator(
//					Criteria.where("insuranceAddress").in(area),
//					Criteria.where("insuranceAddress").isNull(),
//					Criteria.where("insuranceAddress").is("")
//				),
//				Criteria.where("status").is(DeclarationStatus.Done.toString())
//			);
//			query.addCriteria(criteria);

			List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);

			Long id = SnGeneratorUtil.getId();

			File dir = FileUtil.mkdir(filePath + "materialsList/" + id + "-" + "工伤认定申请材料-已办结" + startTimeFormat + "-" + endTimeFormat + "/");
			for (Declaration declaration : declarations) {
				File file = FileUtil.mkdir(new File(dir, declaration.getName()));
				downloadFile(declaration.getPictures(), file, "现场图片");
				downloadFile(declaration.getVideos(), file, "现场视频");
				downloadFile(declaration.getInjuredPartPics(), file, "受伤部位图片");
				downloadFile(declaration.getInjureDeclaration(), file, "工伤（亡）认定申请表");
				downloadFile(declaration.getIdCardPics(), file, "受伤害职工身份证明");
				downloadFile(declaration.getLaborRelation(), file, "劳动关系证明");
				downloadFile(declaration.getBusinessCertificate(), file, "用人单位营业证明");
				downloadFile(declaration.getAuthorizationLetter(), file, "用人单位授权委托书");
				downloadFile(declaration.getAuthorizationLetterV2(), file, "伤者授权委托书");
				downloadFile(declaration.getApplicantCert(), file, "经办人员身份证明");
				downloadFile(declaration.getWitnessTestimony1(), file, "证人证言1");
				downloadFile(declaration.getWitnessTestimony2(), file, "证人证言2");
				downloadFile(declaration.getWitnessTestimony3(), file, "证人证言3");
				downloadFile(declaration.getWitnessTestimony4(), file, "证人证言4");
				downloadFile(declaration.getNoWitnessDeclare(), file, "无证人声明");
				downloadFile(declaration.getMedicalDiagnose(), file, "医疗就诊材料");
				downloadFile(declaration.getOccupDiseaseDiagnose(), file, "职业病诊断证明书或鉴定书");
				downloadFile(declaration.getOrgSurveyReport(), file, "单位事故调查报告");
				downloadFile(declaration.getAcciRespIdentify(), file, "交通事故责任认定书");
				downloadFile(declaration.getRouteMap(), file, "路线图");
				downloadFile(declaration.getResidenceProof(), file, "居住证明");
				downloadFile(declaration.getCourtPoliceCert(), file, "暴力伤害证明");
				downloadFile(declaration.getDeathCremationCert(), file, "医学死亡证明或火化证明");
				downloadFile(declaration.getCivilAffairsCert(), file, "抢险救灾证明");
				downloadFile(declaration.getDisabledMilitaryCert(), file, "伤残军人证");
				downloadFile(declaration.getRecurrenceCert(), file, "旧伤复发鉴定证明");
				downloadFile(declaration.getPublicSecurityCert(), file, "公安机关证明或者其它有效证明");
				downloadFile(declaration.getCourtDeathCert(), file, "人民法院宣告死亡的结论");
				downloadFile(declaration.getWorkTravelCert(), file, "因工外出证明");
				downloadFile(declaration.getOtherCert(), file, "其他证明材料");
				downloadFile(declaration.getCancelApplication(), file, "撤销申请书");
				downloadDeclarationMaterials(declaration.getDeclarationMaterials(), file);
			}
			File zip = ZipUtil.zip(dir);

			MaterialsListPO materialsListPO = new MaterialsListPO();
			materialsListPO.setId(id);
			materialsListPO.setArea(area);
			materialsListPO.setName("工伤认定申请材料-已办结" + startTimeFormat + "-" + endTimeFormat);
			materialsListPO.setStartTime(startTime);
			materialsListPO.setEndTime(endTime);
			materialsListPO.setFile("materialsList/" + zip.getName());
			materialsListPO.setCreateTime(new Date());
			materialsListPO.setFileExpired(false);

			mongoTemplate.save(materialsListPO);

			try {
				FileUtil.del(dir);
			} catch (Exception e) {
				log.error("清理临时文件夹失败：", e);
			}

		}

		if (materialsListType.equals("工伤认定申请材料-未办结")) {
			Query query = new Query();
			query.addCriteria(Criteria.where("submitTime").gte(startTime).lte(endTime));
			query.addCriteria(Criteria.where("status").in("Accepting", "Identifying"));
			if (ObjectUtil.isEmpty(area)) {
				query.addCriteria(new Criteria().orOperator(
					Criteria.where("insuranceAddress").isNull(),
					Criteria.where("insuranceAddress").is("")));
			} else {
				query.addCriteria(Criteria.where("insuranceAddress").in(area));
			}

//			Criteria criteria = new Criteria().andOperator(
//				Criteria.where("submitTime").gte(startTime).lte(endTime),
//				new Criteria().orOperator(
//					Criteria.where("insuranceAddress").in(area),
//					Criteria.where("insuranceAddress").isNull(),
//					Criteria.where("insuranceAddress").is("")
//				),
//				Criteria.where("status").in("Accepting", "Identifying")
//			);
//			query.addCriteria(criteria);

//			query.addCriteria(Criteria.where("submitTime").gte(startTime).lte(endTime));
//			query.addCriteria(Criteria.where("insuranceAddress").in(area));
//			query.addCriteria(Criteria.where("status").in("Accepting", "Identifying"));

			List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);

			Long id = SnGeneratorUtil.getId();

			File dir = FileUtil.mkdir(filePath + "materialsList/" + id + "-" + "工伤认定申请材料-未办结" + startTimeFormat + "-" + endTimeFormat);
			for (Declaration declaration : declarations) {
				File file = FileUtil.mkdir(new File(dir, declaration.getName()));
				downloadFile(declaration.getPictures(), file, "现场图片");
				downloadFile(declaration.getVideos(), file, "现场视频");
				downloadFile(declaration.getInjuredPartPics(), file, "受伤部位图片");
				downloadFile(declaration.getInjureDeclaration(), file, "工伤（亡）认定申请表");
				downloadFile(declaration.getIdCardPics(), file, "受伤害职工身份证明");
				downloadFile(declaration.getLaborRelation(), file, "劳动关系证明");
				downloadFile(declaration.getBusinessCertificate(), file, "用人单位营业证明");
				downloadFile(declaration.getAuthorizationLetter(), file, "用人单位授权委托书");
				downloadFile(declaration.getAuthorizationLetterV2(), file, "伤者授权委托书");
				downloadFile(declaration.getApplicantCert(), file, "经办人员身份证明");
				downloadFile(declaration.getWitnessTestimony1(), file, "证人证言1");
				downloadFile(declaration.getWitnessTestimony2(), file, "证人证言2");
				downloadFile(declaration.getWitnessTestimony3(), file, "证人证言3");
				downloadFile(declaration.getWitnessTestimony4(), file, "证人证言4");
				downloadFile(declaration.getNoWitnessDeclare(), file, "无证人声明");
				downloadFile(declaration.getMedicalDiagnose(), file, "医疗就诊材料");
				downloadFile(declaration.getOccupDiseaseDiagnose(), file, "职业病诊断证明书或鉴定书");
				downloadFile(declaration.getOrgSurveyReport(), file, "单位事故调查报告");
				downloadFile(declaration.getAcciRespIdentify(), file, "交通事故责任认定书");
				downloadFile(declaration.getRouteMap(), file, "路线图");
				downloadFile(declaration.getResidenceProof(), file, "居住证明");
				downloadFile(declaration.getCourtPoliceCert(), file, "暴力伤害证明");
				downloadFile(declaration.getDeathCremationCert(), file, "医学死亡证明或火化证明");
				downloadFile(declaration.getCivilAffairsCert(), file, "抢险救灾证明");
				downloadFile(declaration.getDisabledMilitaryCert(), file, "伤残军人证");
				downloadFile(declaration.getRecurrenceCert(), file, "旧伤复发鉴定证明");
				downloadFile(declaration.getPublicSecurityCert(), file, "公安机关证明或者其它有效证明");
				downloadFile(declaration.getCourtDeathCert(), file, "人民法院宣告死亡的结论");
				downloadFile(declaration.getWorkTravelCert(), file, "因工外出证明");
				downloadFile(declaration.getOtherCert(), file, "其他证明材料");
				downloadFile(declaration.getCancelApplication(), file, "撤销申请书");
				downloadDeclarationMaterials(declaration.getDeclarationMaterials(), file);
			}
			File zip = ZipUtil.zip(dir);

			MaterialsListPO materialsListPO = new MaterialsListPO();
			materialsListPO.setId(id);
			materialsListPO.setArea(area);
			materialsListPO.setName("工伤认定申请材料-未办结" + startTimeFormat + "-" + endTimeFormat);
			materialsListPO.setStartTime(startTime);
			materialsListPO.setEndTime(endTime);
			materialsListPO.setFile("materialsList/" + zip.getName());
			materialsListPO.setCreateTime(new Date());
			materialsListPO.setFileExpired(false);

			mongoTemplate.save(materialsListPO);

			try {
				FileUtil.del(dir);
			} catch (Exception e) {
				log.error("清理临时文件夹失败：", e);
			}
		}

		return CommonResult.successResult("操作成功");
	}

	/**
	 * 根据序列号生成材料清单
	 *
	 * @param materialsVO 材料清单信息
	 */
	@PostMapping("/export")
	public void genMaterials(@RequestBody MaterialsVO materialsVO) throws UnsupportedEncodingException {
		HttpServletResponse response = ServletRequestUtils.getResp();
		response.setContentType(ExcelUtil.XLSX_CONTENT_TYPE);
		response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" +
			URLEncoder.encode(materialsVO.getSerialNum() + "-" + materialsVO.getName() + "-工伤认定申请材料接收清单.xlsx", StandardCharsets.UTF_8.toString()));

		WriteCellStyle headStyle = new WriteCellStyle();
		headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headStyle.setWrapped(true);

		WriteCellStyle contentStyle = new WriteCellStyle();
		contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentStyle.setWrapped(true);

		HorizontalCellStyleStrategy styleStrategy =
			new HorizontalCellStyleStrategy(headStyle, contentStyle);

		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), MaterialsFileVO.class)
			.needHead(false)
			.registerWriteHandler(styleStrategy)
			.withTemplate(CLASS_PATH_RESOURCE.getInputStream())
			.excelType(ExcelTypeEnum.XLSX).build()) {
			WriteSheet writeSheet = EasyExcel.writerSheet().build();
			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			excelWriter.fill(materialsVO.getMaterialsFileList(), fillConfig, writeSheet);
			excelWriter.fill(materialsVO, fillConfig, writeSheet);
			excelWriter.finish();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

}
