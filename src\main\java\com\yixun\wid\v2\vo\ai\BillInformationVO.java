package com.yixun.wid.v2.vo.ai;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingDetailsGroup;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账单信息识别响应VO
 * 用于返回AI识别的账单信息数据，不直接返回完整的BillInformationResponse对象
 */
@Data
public class BillInformationVO {

    /**
     * 案件ID（如果是基于现有案件的识别）
     */
    private Long id;

    /**
     * 文件URL（如果是基于单个文件的OCR识别）
     */
    private String fileUrl;

    /**
     * 账单信息列表
     */
    private List<BillInformation> billInformations;

    @Data
    public static class BillInformation {
        /**
         * 账单号（对应BillingInfo的billId）
         */
        private String billId;

        /**
         * 治疗医院ID
         */
        private Long hospitalId;

        /**
         * 治疗医院
         */
        private String hospital;

        /**
         * 治疗类型：门诊/住院
         */
        private String treatmentType;

        /**
         * 账单总金额（分）
         */
        private Integer amountInCent;

        /**
         * 可报销金额（分）
         */
        private Integer reimbursableAmountInCent;

        /**
         * 不可报销金额（分）
         */
        private Integer nonReimbursableAmountInCent;

        /**
         * 就诊日期
         */
        @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        private Date visitDate;

        /**
         * 就诊结束日期
         */
        @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        private Date visitEndDate;

        /**
         * 出院日期
         */
        @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        private Date dischargeDate;

        /**
         * 门诊开始时间
         */
        @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        private Date outpatientStartTime;

        /**
         * 门诊结束时间
         */
        @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        private Date outpatientEndTime;

        /**
         * 门诊天数
         */
        private Integer outpatientDays;

        /**
         * 电子清单列表
         */
        private List<BillingDetail> billingDetails;

        /**
         * 电子清单分组
         */
        private List<BillingDetailsGroup> billingDetailsGroup;
    }

}
