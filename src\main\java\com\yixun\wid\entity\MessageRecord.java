package com.yixun.wid.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "消息记录表")
@Data
public class MessageRecord {

    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("人员类型")
    private String personType;

    @ApiModelProperty("信息类型")
    private String msgType;

    @ApiModelProperty("信息内容")
    private String msgContent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("发送时间")
    private Date sendTime;

    @ApiModelProperty("业务类型 1报备 2受理 3认定")
    private Integer busType;

    @ApiModelProperty("业务id")
    private String businessId;

}
