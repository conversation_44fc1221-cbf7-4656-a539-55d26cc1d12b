package com.yixun.wid.service.impl;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.CorpAgencyGetIn;
import com.yixun.wid.entity.CorpAgency;
import com.yixun.wid.service.CorpAgencyService;
import com.yixun.wid.utils.MongoUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CorpAgencyServiceImpl implements CorpAgencyService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public List<CorpAgency> getByAgencyCorpId(Long agencyCorpId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("agencyCorpId").is(agencyCorpId));

        return mongoTemplate.find(query, CorpAgency.class);
    }

    @Override
    public void save(CorpAgency corpAgency) {
        mongoTemplate.save(corpAgency);
    }

    @Override
    public void update(CorpAgency corpAgency) {
        corpAgency.setUpdateTime(new Date());
        mongoTemplate.save(corpAgency);
    }

    @Override
    public CorpAgency getById(Long corpAgencyId) {
        return mongoTemplate.findById(corpAgencyId, CorpAgency.class);
    }

    @Override
    public List<CorpAgency> getListByCorpName(String corpName) {
        Query query = new Query();
        query.addCriteria(Criteria.where("corpName").regex(corpName));

        return mongoTemplate.find(query, CorpAgency.class);
    }

    @Override
    public CorpAgency getByCorporation(Long corporationId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("corporationId").is(corporationId));

        return mongoTemplate.findOne(query, CorpAgency.class);
    }

    @Override
    public List<CorpAgency> getPage(CorpAgencyGetIn corpAgencyGetIn, CommonPage commonPage) {
        Query query = new Query();
        if (corpAgencyGetIn.getStatus()!=null){
            query.addCriteria(Criteria.where("agencyStatus").is(corpAgencyGetIn.getStatus().name()));
        }
        if (corpAgencyGetIn.getCorpName()!=null){
            query.addCriteria(Criteria.where("corpName").regex(corpAgencyGetIn.getCorpName()));
        }
        if (corpAgencyGetIn.getAgencyCorpName()!=null){
            query.addCriteria(Criteria.where("agencyCorpName").regex(corpAgencyGetIn.getAgencyCorpName()));
        }
        query.with(Sort.by(Sort.Order.asc("agencyStatus"), Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, CorpAgency.class, query, commonPage);
        return mongoTemplate.find(query, CorpAgency.class);
    }

    @Override
    public List<CorpAgency> getList(Long agencyCorpId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("agencyCorpId").is(agencyCorpId));

        return mongoTemplate.find(query, CorpAgency.class);
    }
}
