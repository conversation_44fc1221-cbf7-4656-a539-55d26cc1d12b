package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.entity.EvidenceInfo;
import com.yixun.wid.entity.InvestigateEvidenceList;
import com.yixun.wid.entity.ThirdPartyInfo;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.IdCardUtil;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InvestigationOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long declarationId;

    private String status;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long governmentId;

    @ApiModelProperty(value = "政府机构名称")
    private String government;

    @ApiModelProperty(value = "政府签发负责人")
    private String issueOfficer;

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "受伤部位")
    private List injuredPart;

    @ApiModelProperty(value = "事故时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date accidentTime;

    @ApiModelProperty(value = "第三方辅助调查情况")
    private ThirdPartyInfo thirdPartyInfo;

    @ApiModelProperty(value = "证据登记")
    private EvidenceInfo evidenceInfo;

    @ApiModelProperty(value = "工伤事故辅助调查证据清单")
    private InvestigateEvidenceList investigateEvidenceList;

    public void setIdCard(String idCard) {
        this.idCard = IdCardUtil.getMask(idCard);
    }

}
