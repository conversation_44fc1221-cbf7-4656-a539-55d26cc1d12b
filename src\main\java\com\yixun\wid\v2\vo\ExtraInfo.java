package com.yixun.wid.v2.vo;

import lombok.Data;

import java.util.List;

@Data
public class ExtraInfo {

	/**
	 * 是否收费 产品确认为输入框
	 */
	private String charge;

	/**
	 * 收费标准 0工伤认定 1劳动能力鉴定 2工伤待遇支付
	 */
	private Integer chargeStandard;

	/**
	 * 是否支持手机支付 产品确认为输入框
	 */
	private String phone;

	/**
	 * 数量限制
	 */
	private String numLimit;

	/**
	 * 禁止性要求
	 */
	private String prohibitionRequirements;

	/**
	 * 受理条件
	 */
	private String acceptanceConditions;

	/**
	 * 法律法规名称
	 */
	private List<SettingBasis> settingBases;

	/**
	 * 常见问题
	 */
	private List<FrequentQuestions> frequentQuestions;

	/**
	 * 法律法规名称
	 */
	@Data
	public static class SettingBasis {

		private String regulationsName;

		private String content;

	}

	/**
	 * 常见问题
	 */
	@Data
	public static class FrequentQuestions {

		private String question;

		private String answer;

	}


}
