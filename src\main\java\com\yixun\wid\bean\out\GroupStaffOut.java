package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.LongJsonSerializer;
import lombok.Data;

@Data
public class GroupStaffOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long staffGroupId;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long parentId;
    private String groupName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long administratorId;
    private String username;
    private String avatar;
}
