package com.yixun.wid.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.yixun.wid.service.AliyunStsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AliyunStsServiceImpl implements AliyunStsService {

	@Value("${spring.profiles.active}")
	private String env;

	@Value("${aliyun.endpoint}")
	private String endpoint;

	@Value("${aliyun.bucket}")
	private String bucket;

	@Value("${aliyun.access-key-id}")
	private String accessKeyId;

	@Value("${aliyun.access-key-secret}")
	private String accessKeySecret;

	@Value("${aliyun.role-arn}")
	private String roleArn;

	@Value("${aliyun.policy-user}")
	private String policyUser;

	@Value("${aliyun.expired}")
	private Integer expired;

	@Override
	public AssumeRoleResponse.Credentials getOssStsToken(String userName, String role) {
		// 只有 RAM用户（子账号）才能调用 AssumeRole 接口
		// 阿里云主账号的AccessKeys不能用于发起AssumeRole请求
		// 请首先在RAM控制台创建一个RAM用户，并为这个用户创建AccessKeys
//        String accessKeyId = "LTAI5tN75kowyabp7qWReFLG";
//        String accessKeySecret = "******************************";
		// AssumeRole API 请求参数: RoleArn, RoleSessionName, Policy, and DurationSeconds
		// RoleArn 需要在 RAM 控制台上获取
//        String roleArn = "acs:ram::1841673459408468:role/ramossrole";
		// RoleSessionName 是临时Token的会话名称，自己指定用于标识你的用户，主要用于审计，或者用于区分Token颁发给谁
		// 但是注意RoleSessionName的长度和规则，不要有空格，只能有'-' '_' 字母和数字等字符
		// 具体规则请参考API文档中的格式要求
		String roleSessionName;

//        long expired = 3600; //超时时间，单位秒, 1小时

//        String policyUser = "{\n" +
//                "    \"Version\": \"1\",\n" +
//                "    \"Statement\": [\n" +
//                "        {\n" +
//                "            \"Effect\": \"Allow\",\n" +
//                "            \"Action\": \"oss:*\",\n" +
//                "            \"Resource\": \"*\"\n" +
//                "        }\n" +
//                "    ]\n" +
//                "}";

		roleSessionName = env + "-" + userName;
		AssumeRoleResponse.Credentials credentials = null;
		try {
			String policy = null;
			if (role.equals("user")){
				policy = policyUser;
			}
			credentials = assumeRole(accessKeyId, accessKeySecret, roleArn, roleSessionName, policy, expired).getCredentials();
		} catch (ClientException e) {
			e.printStackTrace();
		}
		return credentials;
	}

	private AssumeRoleResponse assumeRole(String accessKeyId, String accessKeySecret,
		String roleArn, String roleSessionName, String policy,
		long expired) throws ClientException {
		try {
			//构造default profile（参数留空，无需添加Region ID）
			IClientProfile profile = DefaultProfile.getProfile("", accessKeyId, accessKeySecret);
			//用profile构造client
			DefaultAcsClient client = new DefaultAcsClient(profile);
			final AssumeRoleRequest request = new AssumeRoleRequest();
			request.setSysEndpoint(endpoint);
			request.setSysMethod(MethodType.POST);
			request.setRoleArn(roleArn);
			request.setRoleSessionName(roleSessionName);
			request.setPolicy(policy); // Optional
			request.setDurationSeconds(expired);
			// 发起请求，并得到response
			final AssumeRoleResponse response = client.getAcsResponse(request);
			return response;
		} catch (ClientException e) {
			throw e;
		}
	}

}
