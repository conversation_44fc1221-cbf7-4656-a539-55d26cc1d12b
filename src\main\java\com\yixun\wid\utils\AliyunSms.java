package com.yixun.wid.utils;

import cn.hutool.json.JSONUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class AliyunSms {
    // 产品名称:云通信短信API产品,开发者无需替换
    static final String product = "Dysmsapi";
    static final String domain = "dysmsapi.aliyuncs.com";
    static final String accessKeyId = "LTAI5tJQEfMFUJMqoYyHZwfG";
    static final String accessKeySecret = "******************************";

    public static SendSmsResponse sendSms(Map<String, String> map, String signName)
            throws ClientException {
        // 可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        // 初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou",
                accessKeyId, accessKeySecret);
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product,
                domain);
        IAcsClient acsClient = new DefaultAcsClient(profile);
        // 组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        // 必填:待发送手机号
        request.setPhoneNumbers(map.get("phone"));
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName(signName);
        // 必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(map.get("templateCode"));
        // 可选:模板中的变量替换JSON串，您的验证码${code}，该验证码5分钟内有效，请勿泄漏于他人！
//        if (map.get("verifyCode") != null) {
//            request.setTemplateParam("{\"code\":\"" + map.get("verifyCode") + "\"}");
//        }
        request.setTemplateParam(JSONUtil.toJsonStr(map));
        SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
        return sendSmsResponse;
    }

    public enum TypeId {
        /**
         * 通用验证码
         */
        SMS_254775596,
    }

    public static Map<String, String> getVerifyCodeMap(String code, String phone,
                                                       TypeId type) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("phone", phone);
        map.put("verifyCode", code);
        map.put("templateCode", type.toString());
        return map;
    }

}
