package com.yixun.wid.v2.controller;

import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.entity.*;
import com.yixun.wid.v2.enums.UserType;
import com.yixun.wid.v2.service.BillingInfoService;
import com.yixun.wid.v2.service.MedicalCasesLogService;
import com.yixun.wid.v2.utils.ClaimsDataUtil;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.BatchReceiveResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 待遇 工伤待遇业务相关接口
 */
@Slf4j
@RequestMapping("/v2/medical/cases")
@RestController
public class MedicalCasesController {

	@Resource
	private MongoTemplate mongoTemplate;

	@Resource
	private AdministratorService administratorService;

	@Resource
	private UserService userService;

	@Resource
	private BillingInfoService billingInfoService;

	@Resource
	private MedicalCasesLogService medicalCasesLogService;

	@Resource
	private ClaimsDataUtil claimsDataUtil;

	/**
	 * 获取当前用户名
	 *
	 * @return 当前用户名
	 */
	public String getUserName() {
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.ADMIN.equals(userType)) {
			Administrator administratorById = administratorService.getAdministratorById(userId);
			return administratorById.getRealName();
		}
		if (UserType.USER.equals(userType)) {
			User userById = userService.getUserById(userId);
			return userById.getRealName();
		}
		return null;
	}

	/**
	 * 保存单个工伤待遇业务
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 操作结果
	 */
	@PostMapping("/save")
	public CommonResult<Void> saveMedicalCases(@Validated @RequestBody MedicalCases medicalCases) {
		// 设置基本信息
		medicalCases.setId(SnGeneratorUtil.getId());
		Date now = new Date();
		medicalCases.setCreateTime(now);
		medicalCases.setUpdateTime(now);

		// 设置创建用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		medicalCases.setCreateUserId(String.valueOf(recentUserId));
		medicalCases.setCreateUserName(recentUserName);

		// 设置当前操作用户信息，初始时与创建用户相同
		medicalCases.setRecentUserId(String.valueOf(recentUserId));
		medicalCases.setRecentUserName(recentUserName);

		medicalCases.setStatus(MedicalCasesStatus.Applying);

		// 删除此处设置案件编号的代码

		// 如果包含理算信息，则设置责任项目名称
		if (medicalCases.getClaimsInformations() != null && !medicalCases.getClaimsInformations().isEmpty()) {
			claimsDataUtil.setAllResponsibilityItems(medicalCases);
		}

		// 保存工伤待遇业务
		mongoTemplate.save(medicalCases);

		// 保存关联的账单信息
		saveRelatedBillingInfos(medicalCases.getId(), medicalCases.getBillingInfos());

		// 记录操作日志
		MedicalCasesLog log = new MedicalCasesLog();
		log.setMedicalCasesId(medicalCases.getId());
		log.setStatus("新建受理");
		log.setUserId(String.valueOf(recentUserId));
		log.setUserName(recentUserName);
		log.setCreateTime(now);
		medicalCasesLogService.add(log);

		return CommonResult.successResult("操作成功");
	}

	/**
	 * 批量保存工伤待遇业务
	 *
	 * @param list 工伤待遇业务列表
	 * @return 操作结果
	 */
	@PostMapping("/batch/save")
	public CommonResult<Void> batchSaveMedicalCases(@RequestBody List<MedicalCases> list) {
		// 获取当前用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		Date now = new Date();

		// 删除按受理日期分组的代码

		for (MedicalCases mc : list) {
			// 设置ID和时间
			mc.setId(SnGeneratorUtil.getId());
			mc.setCreateTime(now);
			mc.setUpdateTime(now);

			// 设置创建用户信息
			mc.setCreateUserId(String.valueOf(recentUserId));
			mc.setCreateUserName(recentUserName);

			// 设置当前操作用户信息
			mc.setRecentUserId(String.valueOf(recentUserId));
			mc.setRecentUserName(recentUserName);

			// 删除分组代码
		}

		// 删除设置案件编号的代码

		// 批量保存工伤待遇业务
		mongoTemplate.insertAll(list);

		// 保存关联的账单信息
		for (MedicalCases mc : list) {
			saveRelatedBillingInfos(mc.getId(), mc.getBillingInfos());

			// 记录操作日志
			MedicalCasesLog log = new MedicalCasesLog();
			log.setMedicalCasesId(mc.getId());
			log.setStatus("新建受理");
			log.setUserId(String.valueOf(recentUserId));
			log.setUserName(recentUserName);
			log.setCreateTime(now);
			medicalCasesLogService.add(log);
		}

		return CommonResult.successResult("操作成功");
	}

	/**
	 * 校验初审状态所需的必填参数
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 校验失败返回错误信息，校验通过返回null
	 */
	private String validatePreReviewingParams(MedicalCases medicalCases) {
		if (medicalCases.getAcceptDate() == null) {
			return "初审时受理日期不能为空";
		}
		if (medicalCases.getAccidentDate() == null) {
			return "初审时事故日期不能为空";
		}
		if (StrUtil.isBlank(medicalCases.getTreatmentType())) {
			return "初审时治疗类型不能为空";
		}
		return null;
	}

	/**
	 * 保存关联的账单信息
	 *
	 * @param medicalCasesId 工伤待遇业务ID
	 * @param billingInfos   账单信息列表
	 */
	private void saveRelatedBillingInfos(Long medicalCasesId, List<BillingInfo> billingInfos) {
		if (billingInfos != null && !billingInfos.isEmpty()) {
			for (BillingInfo billingInfo : billingInfos) {
				// 创建账单信息输入参数
				BillingInfoIn billingInfoIn = new BillingInfoIn();
				billingInfoIn.setHospital(billingInfo.getHospital());
				billingInfoIn.setTreatmentType(billingInfo.getTreatmentType());
				billingInfoIn.setMedicalCasesId(medicalCasesId);

				// 保存账单信息
				billingInfoService.add(billingInfoIn);
			}
		}
	}

	/**
	 * 更新工伤待遇业务
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 操作结果
	 */
	@PostMapping("/update")
	public CommonResult<Void> updateMedicalCases(@Validated @RequestBody MedicalCases medicalCases) {
		if (medicalCases.getId() == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
		}
		MedicalCases existing = mongoTemplate.findById(medicalCases.getId(), MedicalCases.class);
		if (existing == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
		}
		MedicalCasesStatus oldStatus = existing.getStatus();
		// 新增：assigneeUserId为空不能更新
		if (!MedicalCasesStatus.Applying.equals(oldStatus)
			&& (existing.getAssigneeUserId() == null || existing.getAssigneeUserId().isEmpty())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "未分配任务不能更新");
		}

		// 判断是否为提交操作（由isSubmit字段决定）
		boolean isSubmit = Boolean.TRUE.equals(medicalCases.getIsSubmit());
		MedicalCasesStatus status = oldStatus;
		if (isSubmit) {
			// 状态流转到下一阶段
			status = getNextStatus(oldStatus);
			medicalCases.setStatus(status);

			// 当案件状态为Applying且isSubmit为true时，设置案件编号（表示正式受理）
			if (MedicalCasesStatus.Applying.equals(oldStatus)) {
				Date acceptDate = medicalCases.getAcceptDate();
				if (acceptDate == null) {
					acceptDate = new Date(); // 如果未设置受理日期，使用当前日期
					medicalCases.setAcceptDate(acceptDate);
				}

				// 按照格式生成案件编号：BX1014 + 受理日期 + 序号
				String dateFormat = cn.hutool.core.date.DateUtil.format(acceptDate, "yyyyMMdd");
				String prefix = "BX1014" + dateFormat;

				// 查询当天最大序号
				Query query = new Query();
				query.addCriteria(Criteria.where("caseNumber").regex("^" + prefix));
				query.with(Sort.by(Sort.Direction.DESC, "caseNumber"));
				query.limit(1);

				MedicalCases lastCase = mongoTemplate.findOne(query, MedicalCases.class);

				int sequence = 1;
				if (lastCase != null && lastCase.getCaseNumber() != null) {
					try {
						// 提取序号部分并加1
						String sequenceStr = lastCase.getCaseNumber().substring(prefix.length());
						sequence = Integer.parseInt(sequenceStr) + 1;
					} catch (Exception e) {
						log.error("解析案件编号序号失败", e);
					}
				}

				// 格式化为3位数，不足前面补0
				String sequenceFormat = String.format("%03d", sequence);
				medicalCases.setCaseNumber(prefix + sequenceFormat);
			}
		} else {
			// 仅保存，状态不变
			medicalCases.setStatus(oldStatus);
		}

		// 校验（如有需要）
		if (isSubmit) {
			String errorMessage = validatePreReviewingParams(medicalCases);
			if (errorMessage != null) {
				return CommonResult.failResult(CommonErrorInfo.code_1001, errorMessage);
			}
			// 设置提交用户信息
			Long recentUserId = ServletRequestUtils.getUserId();
			String recentUserName = getUserName();
			medicalCases.setSubmitUserId(String.valueOf(recentUserId));
			medicalCases.setSubmitUserName(recentUserName);
			// 根据新状态记录对应用户信息
			Date now = new Date();
			if (MedicalCasesStatus.PreReviewing.equals(status)) {
				medicalCases.setAcceptUserId(String.valueOf(recentUserId));
				medicalCases.setAcceptUserName(recentUserName);
				medicalCases.setAcceptTime(now);
			} else if (MedicalCasesStatus.Reviewing.equals(status)) {
				medicalCases.setPreReviewUserId(String.valueOf(recentUserId));
				medicalCases.setPreReviewUserName(recentUserName);
				medicalCases.setPreReviewTime(now);
			} else if (MedicalCasesStatus.FinalReviewing.equals(status)) {
				medicalCases.setReviewUserId(String.valueOf(recentUserId));
				medicalCases.setReviewUserName(recentUserName);
				medicalCases.setReviewTime(now);
			} else if (MedicalCasesStatus.Done.equals(status)) {
				medicalCases.setFinalReviewUserId(String.valueOf(recentUserId));
				medicalCases.setFinalReviewUserName(recentUserName);
				medicalCases.setFinalReviewTime(now);
			}
		}

		// 保存账单信息（如果有）
		List<BillingInfo> billingInfos = medicalCases.getBillingInfos();
		if (billingInfos != null && !billingInfos.isEmpty()) {
			saveRelatedBillingInfos(medicalCases.getId(), billingInfos);

			// 清空临时字段，不保存到数据库
			medicalCases.setBillingInfos(null);
		}

		// 使用属性复制，但排除createUserId、createUserName和createTime
		org.springframework.beans.BeanUtils.copyProperties(medicalCases, existing, "createUserId", "createUserName", "createTime");

		// 如果包含理算信息，则设置责任项目名称
		if (existing.getClaimsInformations() != null && !existing.getClaimsInformations().isEmpty()) {
			claimsDataUtil.setAllResponsibilityItems(existing);
		}

		// 如果是提交操作，确保assigneeUserId被清空
		if (isSubmit) {
			existing.setAssigneeUserId(null);
		}

		// 设置更新时间
		Date now = new Date();
		existing.setUpdateTime(now);

		// 设置当前操作用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		existing.setRecentUserId(String.valueOf(recentUserId));
		existing.setRecentUserName(recentUserName);

		// 保存更新后的工伤待遇业务
		mongoTemplate.save(existing);

		// 记录操作日志
		MedicalCasesLog log = new MedicalCasesLog();
		log.setMedicalCasesId(existing.getId());
		log.setStatus("受理修改");
		log.setUserId(String.valueOf(recentUserId));
		log.setUserName(recentUserName);
		log.setCreateTime(now);
		medicalCasesLogService.add(log);

		return CommonResult.successResult("操作成功");
	}

	/**
	 * 查询工伤待遇业务列表 前端暂时忽略该接口
	 *
	 * @param name         工伤待遇业务名称（模糊搜索，可选）
	 * @param organization 组织名称（模糊搜索，可选）
	 * @param idCard       身份证号（模糊搜索，可选）
	 * @param statusList   业务状态列表（可选，支持多个状态同时筛选）业务受理（Applying）待接收任务（PreReviewing、Reviewing、FinalReviewing）
	 * @param submitTime   提交时间
	 * @param commonPage   分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/list")
	public CommonResult<List<MedicalCases>> list(@RequestParam(required = false) String name, @RequestParam(required = false) String organization,
		@RequestParam(required = false) String idCard, @RequestParam(required = false) List<MedicalCasesStatus> statusList, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date submitTime, CommonPage commonPage) {
		Query query = new Query();
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("name").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);

		// 为每个工伤待遇业务加载关联的账单信息
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}

		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 根据ID获取工伤待遇业务详情，包含关联的账单信息
	 *
	 * @param id 工伤待遇业务ID
	 * @return 工伤待遇业务详情，包含关联的账单信息
	 */
	@GetMapping("/detail")
	public CommonResult<MedicalCases> getById(@RequestParam Long id) {
		MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
		if (medicalCases == null) {
			return CommonResult.failResult(10001, "工伤待遇业务不存在");
		}

		// 加载关联的账单信息
		loadBillingInfos(medicalCases);

		return CommonResult.successData(medicalCases);
	}

	/**
	 * 为工伤待遇业务加载关联的账单信息
	 *
	 * @param medicalCases 工伤待遇业务
	 */
	private void loadBillingInfos(MedicalCases medicalCases) {
		// 查询关联到该工伤待遇业务的账单信息
		Query billingInfoQuery = Query.query(Criteria.where("medicalCasesId").is(medicalCases.getId()));
		billingInfoQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));
		List<BillingInfo> billingInfoList = mongoTemplate.find(billingInfoQuery, BillingInfo.class);

		if (!billingInfoList.isEmpty()) {
			// 遍历每个账单信息，加载其关联的账单明细
			for (BillingInfo billingInfo : billingInfoList) {
				// 查询关联的账单明细
				Query detailQuery = Query.query(Criteria.where("billingInfoId").is(billingInfo.getId()));
				detailQuery.with(Sort.by(Sort.Direction.ASC, "orderNum"));
				List<BillingDetail> details = mongoTemplate.find(detailQuery, BillingDetail.class);

				// 设置账单明细到账单信息的临时字段中
				billingInfo.setBillingDetails(details);
			}

			// 设置账单信息列表到工伤待遇业务中
			medicalCases.setBillingInfos(billingInfoList);
		} else {
			medicalCases.setBillingInfos(new ArrayList<>());
		}
	}

	/**
	 * 导入工伤待遇业务数据
	 *
	 * @return 操作结果
	 */
	@PostMapping("/import")
	public CommonResult<Void> importMedicalCases() {
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 批量接收工伤待遇业务任务
	 *
	 * @param ids 工伤待遇业务ID列表
	 * @return 操作结果，包含成功接收数量和无法接收的案件列表
	 */
	@PostMapping("/batch/receive")
	public CommonResult<BatchReceiveResult> batchReceiveMedicalCases(@RequestBody List<Long> ids) {
		if (ids == null || ids.isEmpty()) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID列表不能为空");
		}
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		Date now = new Date();
		List<MedicalCases> toUpdate = new ArrayList<>();
		List<BatchReceiveResult.FailedCase> cannotReceiveList = new ArrayList<>();

		for (Long id : ids) {
			MedicalCases mc = mongoTemplate.findById(id, MedicalCases.class);
			if (mc != null) {
				// 检查案件是否已被他人接收
				if (mc.getAssigneeUserId() != null && !mc.getAssigneeUserId().isEmpty()) {
					BatchReceiveResult.FailedCase failInfo = new BatchReceiveResult.FailedCase(
						String.valueOf(mc.getId()),
						"案件已被他人接收"
					);
					cannotReceiveList.add(failInfo);
					continue;
				}

				// 检查是否为待复审案件，且当前用户是该案件的初审人员
				if (MedicalCasesStatus.Reviewing.equals(mc.getStatus())) {
					// 直接使用MedicalCases中的preReviewUserId字段判断
					if (String.valueOf(recentUserId).equals(mc.getPreReviewUserId())) {
						BatchReceiveResult.FailedCase failInfo = new BatchReceiveResult.FailedCase(
							String.valueOf(mc.getId()),
							"不能复审自己初审的案件"
						);
						cannotReceiveList.add(failInfo);
						continue;
					}
				}

				mc.setAssigneeUserId(String.valueOf(recentUserId));
				mc.setRecentUserId(String.valueOf(recentUserId));
				mc.setRecentUserName(recentUserName);
				mc.setUpdateTime(now);
				toUpdate.add(mc);
			}
		}

		// 保存成功接收的案件
		if (!toUpdate.isEmpty()) {
			for (MedicalCases mc : toUpdate) {
				mongoTemplate.save(mc);
			}
		}

		// 构建返回结果
		BatchReceiveResult result = new BatchReceiveResult(
			toUpdate.size(),
			cannotReceiveList.size(),
			cannotReceiveList
		);

		return CommonResult.successData(result);
	}

	/**
	 * 业务受理列表查询接口，只查Applying状态
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/apply-list")
	public CommonResult<List<MedicalCases>> applyList(@RequestParam(required = false) String name,
		@RequestParam(required = false) String organization,
		@RequestParam(required = false) String idCard,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
		CommonPage commonPage) {
		Query query = new Query();
		// 只查Applying状态
		query.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Applying));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 待接收任务列表查询接口，只允许筛选PreReviewing、Reviewing、FinalReviewing三种状态
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param statusList      状态筛选（可选，仅允许PreReviewing、Reviewing、FinalReviewing）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/pending-receive-list")
	public CommonResult<List<MedicalCases>> pendingReceiveList(@RequestParam(required = false) String name,
		@RequestParam(required = false) String organization,
		@RequestParam(required = false) String idCard,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
		@RequestParam(required = false) List<MedicalCasesStatus> statusList,
		CommonPage commonPage) {
		// 允许的状态
		List<MedicalCasesStatus> allowedStatus = java.util.Arrays.asList(MedicalCasesStatus.PreReviewing, MedicalCasesStatus.Reviewing, MedicalCasesStatus.FinalReviewing);
		List<MedicalCasesStatus> queryStatus;
		if (statusList == null || statusList.isEmpty()) {
			queryStatus = allowedStatus;
		} else {
			// 只保留允许的状态
			queryStatus = statusList.stream().filter(allowedStatus::contains).collect(Collectors.toList());
			if (queryStatus.isEmpty()) {
				// 如果传入的都不合法，默认查全部允许的
				queryStatus = allowedStatus;
			}
		}
		Query query = new Query();
		query.addCriteria(Criteria.where("status").in(queryStatus));
		// 只查assigneeUserId为空的数据
		query.addCriteria(new Criteria().orOperator(
			Criteria.where("assigneeUserId").is(null),
			Criteria.where("assigneeUserId").is("")
		));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 我的任务列表接口，只查assigneeUserId为当前用户ID的数据
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param statusList      状态筛选（可选）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/my-task-list")
	public CommonResult<List<MedicalCases>> myTaskList(@RequestParam(required = false) String name,
		@RequestParam(required = false) String organization,
		@RequestParam(required = false) String idCard,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
		@RequestParam(required = false) List<MedicalCasesStatus> statusList,
		CommonPage commonPage) {
		Long recentUserId = ServletRequestUtils.getUserId();
		Query query = new Query();
		query.addCriteria(Criteria.where("assigneeUserId").is(String.valueOf(recentUserId)));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 根据工伤待遇业务ID和责任树编码查询对应的理算条信息
	 * 如果不存在对应的理算信息，则自动构造一个默认的理算信息
	 *
	 * @param id   工伤待遇业务ID
	 * @param code 责任树编码
	 * @return 理算条信息
	 */
	@GetMapping("/claims-information")
	public CommonResult<MedicalCases.ClaimsInformation> getClaimsInformation(@RequestParam Long id, @RequestParam String code) {
		// 查询医疗案例
		MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
		if (medicalCases == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
		}

		// 获取理算信息Map
		Map<String, MedicalCases.ClaimsInformation> claimsInformations = medicalCases.getClaimsInformations();
		MedicalCases.ClaimsInformation claimsInformation = null;

		// 如果Map存在且包含对应编码的理算条信息，直接返回
		if (claimsInformations != null && !claimsInformations.isEmpty()) {
			claimsInformation = claimsInformations.get(code);
		}

		// 如果不存在对应编码的理算条信息，则构造一个默认的理算信息
		if (claimsInformation == null) {
			claimsInformation = createDefaultClaimsInformation(code);

			// 调用工具类设置责任项目名称
			claimsDataUtil.setResponsibilityItem(claimsInformation);

			// 如果MedicalCases的claimsInformations为空，初始化它
			if (claimsInformations == null) {
				claimsInformations = new HashMap<>();
				medicalCases.setClaimsInformations(claimsInformations);
			}

			// 将构造的理算条信息放入Map中并保存到数据库
			claimsInformations.put(code, claimsInformation);

			// 更新数据库中的理算信息
			mongoTemplate.save(medicalCases);
		}

		return CommonResult.successData(claimsInformation);
	}

	/**
	 * 创建默认的理算条信息
	 * 当code为OUTPATIENT_COST时，封装OutpatientClearing
	 * 当code为HOSPITALIZATION_COST时，封装HospitalClearing
	 *
	 * @param code 责任树编码
	 * @return 默认的理算条信息
	 */
	private MedicalCases.ClaimsInformation createDefaultClaimsInformation(String code) {
		MedicalCases.ClaimsInformation claimsInformation = new MedicalCases.ClaimsInformation();
		claimsInformation.setResponsibilityTreeCode(code);
		claimsInformation.setClaimsStatus(false); // 未理算

		// 判断是门诊还是住院
		boolean isOutpatient = "OUTPATIENT_COST".equals(code);
		boolean isHospitalization = "HOSPITALIZATION_COST".equals(code);

		// 创建理算数据
		ClaimsData claimsData = new ClaimsData();
		claimsData.setHistoryPayCount(0);
		claimsData.setHistoryPayDays(0);
		claimsData.setHistoryPayAmountInCent(0);
		claimsData.setIsHospitalFoodAllowance(isHospitalization); // 只有住院才有伙食补助
		claimsData.setFoodAllowanceStandardInCent(0);
		claimsData.setTotalPayDays(0);
		claimsData.setFoodAllowanceAmountInCent(0);
		claimsData.setInvoiceTotalAmountInCent(0);
		claimsData.setInvoiceCount(0);
		claimsData.setNonReimbursableAmountInCent(0);
		claimsData.setReimbursableAmountInCent(0);
		claimsData.setTotalPayableAmountInCent(0);

		// 根据责任树编码创建相应的核销明细
		if (isOutpatient) {
			// 创建门诊核销明细
			OutpatientClearing outpatientClearing = new OutpatientClearing();
			outpatientClearing.setClearingItems(new ArrayList<>());
			outpatientClearing.setTotalClaimAmountInCent(0);
			outpatientClearing.setTotalDeductionAmountInCent(0);
			outpatientClearing.setTotalPayableAmountInCent(0);

			// 设置门诊核销明细
			claimsData.setOutpatientClearing(outpatientClearing);
			claimsInformation.setOutpatientClearing(outpatientClearing);
		} else if (isHospitalization) {
			// 创建住院核销明细
			HospitalClearing hospitalClearing = new HospitalClearing();
			hospitalClearing.setClearingItems(new ArrayList<>());
			hospitalClearing.setTotalInvoiceAmountInCent(0);
			hospitalClearing.setTotalReimbursableAmountInCent(0);
			hospitalClearing.setTotalNonReimbursableAmountInCent(0);
			hospitalClearing.setTotalHospitalDays(0);
			hospitalClearing.setTotalHospitalFoodAllowanceInCent(0);
			hospitalClearing.setTotalPayableAmountInCent(0);

			// 设置住院核销明细
			claimsData.setHospitalClearing(hospitalClearing);
			claimsInformation.setHospitalClearing(hospitalClearing);
		} else {
			// 对于其他类型的责任树编码，可以根据需要设置相应的默认值
			// 这里可以不设置任何核销明细，或者设置空的核销明细
		}

		// 设置ClaimsData
		claimsInformation.setClaimsData(claimsData);

		// 创建理算结果
		ClaimsResult claimsResult = new ClaimsResult();
		claimsResult.setTotalClaimAmountInCent(0);
		claimsResult.setNonReimbursableAmountInCent(0);
		claimsResult.setTotalPayableAmountInCent(0);
		claimsResult.setThirdPartyPayAmountInCent(0);
		claimsResult.setReimbursementRatio(new java.math.BigDecimal("1.0")); // 默认报销比例为100%
		claimsResult.setThirdPartyActualPayAmountInCent(0);
		claimsResult.setActualPayAmountInCent(0);
		claimsResult.setRemarks("");

		// 设置ClaimsResult
		claimsInformation.setClaimsResult(claimsResult);

		return claimsInformation;
	}

	/**
	 * 历史案件查询接口
	 * 支持按姓名、身份证、单位名称、状态等多条件查询历史案件
	 *
	 * @param workerName      职工姓名（可选）
	 * @param idCard          身份证号（可选）
	 * @param organization    单位名称（可选）
	 * @param acceptDateStart 受理开始时间（可选）
	 * @param acceptDateEnd   受理结束时间（可选）
	 * @param statusList      案件状态列表（可选）
	 * @param commonPage      分页信息
	 * @return 历史案件列表（分页）
	 */
	@GetMapping("/history-cases")
	public CommonResult<List<MedicalCases>> getHistoryCases(
		@RequestParam(required = false) String workerName,
		@RequestParam(required = false) String idCard,
		@RequestParam(required = false) String organization,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
		@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
		@RequestParam(required = false) List<MedicalCasesStatus> statusList,
		CommonPage commonPage) {

		Query query = new Query();

		// 添加查询条件
		if (StrUtil.isNotBlank(workerName)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + workerName + ".*", "i"));
		}

		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}

		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}

		// 受理时间范围查询
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}

		// 状态查询
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}

		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));

		// 设置分页信息
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);

		// 执行查询
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);

		// 加载关联的账单信息
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}

		return CommonResult.successPageData(list, commonPage);
	}

	private MedicalCasesStatus getNextStatus(MedicalCasesStatus current) {
		if (current == MedicalCasesStatus.Applying) {
			return MedicalCasesStatus.PreReviewing;
		}
		if (current == MedicalCasesStatus.PreReviewing) {
			return MedicalCasesStatus.Reviewing;
		}
		if (current == MedicalCasesStatus.Reviewing) {
			return MedicalCasesStatus.FinalReviewing;
		}
		if (current == MedicalCasesStatus.FinalReviewing) {
			return MedicalCasesStatus.Done;
		}
		return current;
	}

}
