package com.yixun.wid.entity;

import com.yixun.wid.entity.em.EventStatus;
import com.yixun.wid.entity.em.EventType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@ApiModel(value = "Event对象", description = "事件表")
public class Event {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty("接收时间")
    private LocalDateTime receiveDate;

    @ApiModelProperty("完成时间")
    private LocalDateTime finishDate;

    @ApiModelProperty("业务id")
    private Long sourceId;

    @ApiModelProperty("来源名称")
    private String sourceName;

    @ApiModelProperty("事件类型")
    private EventType eventType;

    @ApiModelProperty("事件状态")
    private EventStatus eventStatus;

    @ApiModelProperty("事件状态描述")
    private String eventStatusDesc;

    @ApiModelProperty("事件内容")
    private String content;

    @ApiModelProperty("生成文件地址")
    private String fileUrl;

	@ApiModelProperty("文件名称")
	private String fileName;
}
