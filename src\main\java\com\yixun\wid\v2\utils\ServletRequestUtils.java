package com.yixun.wid.v2.utils;

import cn.hutool.core.util.ObjectUtil;
import com.yixun.wid.v2.enums.UserType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ServletRequestUtils {

	private static ServletRequestAttributes getSRA() throws IllegalStateException {
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (ObjectUtils.isEmpty(requestAttributes)) {
			throw new IllegalStateException("error get ServletRequestAttributes");
		}
		return requestAttributes;
	}

	public static HttpServletRequest getReq() throws IllegalStateException {
		return getSRA().getRequest();
	}

	public static HttpServletResponse getResp() throws IllegalStateException {
		return getSRA().getResponse();
	}

	public static Long getUserId() {
		Long userId = (Long) getReq().getAttribute("userId");
		if (ObjectUtil.isNull(userId)) {
			throw new RuntimeException("未登录或登录已过期");
		}
		return userId;
	}

	public static UserType getUserType() {
		Integer userType = (Integer) getReq().getAttribute("userType");
		if (UserType.USER.getCode().equals(userType)) {
			return UserType.USER;
		}
		if (UserType.ADMIN.getCode().equals(userType)) {
			return UserType.ADMIN;
		}
		throw new RuntimeException("未登录或登录已过期");
	}

	public static String getAuthHeader() {
		return getReq().getHeader("Authorization");
	}

}
