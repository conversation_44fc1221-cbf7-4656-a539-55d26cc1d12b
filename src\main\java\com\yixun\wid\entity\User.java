package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class User implements Serializable {

    private Long id;

    private Date createTime;

    @ApiModelProperty(value = "是否禁用")
    private Boolean isDisable;

    @ApiModelProperty(value = "微信小程序openId")
    private String weixinMiniOpenId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "生日")
    private String birthDate;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "身份证地址")
    private String idCardAddr;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "用户类型")
    private String type;

    @ApiModelProperty(value = "单位id")
    private Long organizationId;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "单位信用代码")
    private String orgCreditCode;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "家庭区域")
    private List homeRegion;

    @ApiModelProperty(value = "家庭地址")
    private String homeAddr;

    @ApiModelProperty(value = "收件地址")
    private String consigneeAddr;

    @ApiModelProperty(value = "是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty(value = "参保地")
    private String insuranceAddress;

    @ApiModelProperty(value = "企业认证类型")
    private String corpCertifiedType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    @ApiModelProperty(value = "最后登录ip")
    private String lastLoginIp;

}