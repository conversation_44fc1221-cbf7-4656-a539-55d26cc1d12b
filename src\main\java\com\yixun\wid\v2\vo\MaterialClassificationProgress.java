package com.yixun.wid.v2.vo;

import com.yixun.wid.v2.vo.ai.MaterialClassificationResponse;
import lombok.Data;

/**
 * 材料分类进度跟踪类
 */
@Data
public class MaterialClassificationProgress {
    
    /**
     * 进度状态码
     * 0: 处理中
     * 1: 成功
     * -1: 失败
     * -2: 未找到进度信息
     */
    private Integer code;
    
    /**
     * 数据内容
     * 成功时为MaterialClassificationResponse对象
     * 失败时为错误信息字符串
     * 处理中时为null或进度描述
     */
    private Object data;
    
    /**
     * 创建处理中状态的进度对象
     */
    public static MaterialClassificationProgress processing() {
        MaterialClassificationProgress progress = new MaterialClassificationProgress();
        progress.setCode(0);
        progress.setData("正在处理中...");
        return progress;
    }
    
    /**
     * 创建成功状态的进度对象
     */
    public static MaterialClassificationProgress success(MaterialClassificationResponse response) {
        MaterialClassificationProgress progress = new MaterialClassificationProgress();
        progress.setCode(1);
        progress.setData(response);
        return progress;
    }
    
    /**
     * 创建失败状态的进度对象
     */
    public static MaterialClassificationProgress failure(String errorMessage) {
        MaterialClassificationProgress progress = new MaterialClassificationProgress();
        progress.setCode(-1);
        progress.setData(errorMessage);
        return progress;
    }

    /**
     * 创建未找到状态的进度对象
     */
    public static MaterialClassificationProgress notFound() {
        MaterialClassificationProgress progress = new MaterialClassificationProgress();
        progress.setCode(-2);
        progress.setData("未找到该业务的材料分类进度信息");
        return progress;
    }
}
