package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 受理信息和临床诊断响应结果
 */
@Data
public class AcceptedInformationDiagnosisResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 数据
     */
    private DiagnosisData data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class DiagnosisData {
        /**
         * 事故时间
         */
        @JSONField(name = "accident_date")
        private String accidentDate;

        /**
         * 职工姓名
         */
        @JSONField(name = "employee_name")
        private String employeeName;

        /**
         * 性别
         */
        private String gender;

        /**
         * 身份证号码
         */
        @JSONField(name = "ID_number")
        private String idNumber;

        /**
         * 用人单位
         */
        @JSONField(name = "employer_name")
        private String employerName;

        /**
         * 临床诊断
         */
        @JSONField(name = "clinical_diagnosis")
        private List<String> clinicalDiagnosis;
    }
}
