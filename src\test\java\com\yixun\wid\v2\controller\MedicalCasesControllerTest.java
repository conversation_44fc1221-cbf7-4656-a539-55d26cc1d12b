package com.yixun.wid.v2.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.wid.v2.entity.MedicalCases;
import com.yixun.wid.v2.entity.TreatmentMaterials;
import com.yixun.wid.v2.vo.MaterialClassificationRequestVO;
import com.yixun.wid.v2.vo.UpdateMaterialsRequest;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.Collections;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * MedicalCasesController 测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class MedicalCasesControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试材料类型识别接口
     */
    @Test
    public void testMaterialClassification() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备测试数据
        MaterialClassificationRequestVO request = new MaterialClassificationRequestVO();
        request.setId(123456L);
        request.setType("medical");
        request.setFiles(Arrays.asList(
            "https://example.com/file1.jpg",
            "https://example.com/file2.pdf"
        ));

        // 执行测试
        mockMvc.perform(post("/v2/medical/cases/material-classification")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("材料分类任务已提交，正在异步处理中"));
    }

    /**
     * 测试材料类型识别接口 - 参数验证失败
     */
    @Test
    public void testMaterialClassificationWithInvalidParams() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备无效测试数据（缺少必填字段）
        MaterialClassificationRequestVO request = new MaterialClassificationRequestVO();
        // 不设置必填字段

        // 执行测试，期望参数验证失败
        mockMvc.perform(post("/v2/medical/cases/material-classification")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试材料类型识别接口 - 空文件列表
     */
    @Test
    public void testMaterialClassificationWithEmptyFiles() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 准备测试数据（空文件列表）
        MaterialClassificationRequestVO request = new MaterialClassificationRequestVO();
        request.setId(123456L);
        request.setType("medical");
        request.setFiles(Arrays.asList()); // 空列表

        // 执行测试，期望参数验证失败
        mockMvc.perform(post("/v2/medical/cases/material-classification")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试查询材料分类进度接口
     */
    @Test
    public void testGetMaterialClassificationProgress() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        Long testId = 123456L;

        // 执行测试 - 查询不存在的进度，应该返回code为-2的进度对象
        mockMvc.perform(get("/v2/medical/cases/material-classification-progress")
                .param("id", testId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.code").value(-2))
                .andExpect(jsonPath("$.data.data").value("未找到该业务的材料分类进度信息"));
    }

    /**
     * 测试取消材料分类任务接口
     */
    @Test
    public void testCancelMaterialClassification() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        Long testId = 123456L;

        // 执行测试 - 取消不存在的任务，应该返回404错误
        mockMvc.perform(delete("/v2/medical/cases/material-classification-cancel")
                .param("id", testId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("未找到该业务的材料分类任务"));
    }

    /**
     * 测试受理信息和临床诊断信息识别接口 - 案件不存在
     */
    @Test
    public void testAcceptedInformationDiagnosisWithNonExistentCase() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        Long nonExistentId = 999999L;

        // 执行测试 - 查询不存在的案件，应该返回错误
        mockMvc.perform(post("/v2/medical/cases/accepted-information-diagnosis")
                .param("id", nonExistentId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(6001))
                .andExpect(jsonPath("$.message").value("工伤待遇业务不存在"));
    }

    /**
     * 测试AcceptedInformationDiagnosisRequest数据结构
     */
    @Test
    public void testAcceptedInformationDiagnosisRequestStructure() {
        // 测试AcceptedInformationDiagnosisRequest的数据结构
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();

        // 创建申请材料
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setWorkInjuryBenefitApplicationForm(Collections.singletonList("工伤待遇申请表.pdf"));
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        application.setSocialSecurityCard(Collections.singletonList("社保卡.jpg"));
        application.setBankCard(Collections.singletonList("银行卡.jpg"));
        request.setApplication(application);

        // 创建就诊材料
        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();

        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历.pdf"));
        medicalRecord.setDischargeCertificate(Collections.singletonList("出院证明.pdf"));
        medicalRecord.setDiagnosisCertificate(Collections.singletonList("诊断证明.pdf"));
        materials.setMedicalRecord(medicalRecord);

        materials.setElectronicInvoice(Collections.singletonList("电子发票.pdf"));
        materials.setExaminationReport(Collections.singletonList("检查报告.pdf"));
        materials.setOtherConsultationReports(Collections.singletonList("其他就诊报告.pdf"));

        visit.setMaterial(materials);
        request.setMedical(Collections.singletonList(visit));

        // 验证数据结构
        assert request.getApplication() != null;
        assert request.getMedical() != null;
        assert request.getMedical().size() == 1;
        assert request.getApplication().getWorkInjuryBenefitApplicationForm() != null;
        assert request.getApplication().getIdCard() != null;
        assert request.getMedical().get(0).getVisitType().equals("住院");
        assert request.getMedical().get(0).getMaterial().getMedicalRecord() != null;

        System.out.println("AcceptedInformationDiagnosisRequest数据结构测试通过");
    }

    /**
     * 测试MedicalCases与AcceptedInformationDiagnosisRequest的关联
     */
    @Test
    public void testMedicalCasesWithAcceptedInformationDiagnosisRequest() {
        // 测试MedicalCases与AcceptedInformationDiagnosisRequest的关联
        MedicalCases medicalCases = new MedicalCases();
        medicalCases.setId(12345L);
        medicalCases.setWorkerName("张三");
        medicalCases.setOrganization("测试公司");
        medicalCases.setIdCard("123456789012345678");

        // 创建AcceptedInformationDiagnosisRequest
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();

        // 创建申请材料
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setWorkInjuryBenefitApplicationForm(Collections.singletonList("工伤待遇申请表.pdf"));
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        request.setApplication(application);

        // 创建就诊材料
        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("门诊");
        visit.setVisitDate("2024-01-15");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();

        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("门诊病历.pdf"));
        materials.setMedicalRecord(medicalRecord);

        materials.setElectronicInvoice(Collections.singletonList("门诊发票.pdf"));
        visit.setMaterial(materials);

        request.setMedical(Collections.singletonList(visit));

        // 设置到MedicalCases中
        medicalCases.setAcceptedInformationDiagnosisRequest(request);

        // 验证关联
        assert medicalCases.getAcceptedInformationDiagnosisRequest() != null;
        assert medicalCases.getAcceptedInformationDiagnosisRequest().getApplication() != null;
        assert medicalCases.getAcceptedInformationDiagnosisRequest().getMedical() != null;
        assert medicalCases.getAcceptedInformationDiagnosisRequest().getMedical().size() == 1;
        assert medicalCases.getAcceptedInformationDiagnosisRequest().getMedical().get(0).getVisitType().equals("门诊");

        System.out.println("MedicalCases与AcceptedInformationDiagnosisRequest关联测试通过");
    }

    /**
     * 测试更新材料接口 - 案件不存在
     */
    @Test
    public void testUpdateMaterialsWithNonExistentCase() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        Long nonExistentId = 999999L;

        UpdateMaterialsRequest request = new UpdateMaterialsRequest();
        request.setId(nonExistentId);

        // 创建一个简单的AcceptedInformationDiagnosisRequest用于测试
        AcceptedInformationDiagnosisRequest diagnosisRequest = new AcceptedInformationDiagnosisRequest();
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setIdCard(Collections.singletonList("test_id_card.jpg"));
        diagnosisRequest.setApplication(application);
        request.setAcceptedInformationDiagnosisRequest(diagnosisRequest);

        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = objectMapper.writeValueAsString(request);

        // 执行测试 - 更新不存在的案件，应该返回错误
        mockMvc.perform(post("/v2/medical/cases/update-materials")
                .contentType("application/json")
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(6001))
                .andExpect(jsonPath("$.message").value("工伤待遇业务不存在"));
    }

    /**
     * 测试UpdateMaterialsRequest数据结构
     */
    @Test
    public void testUpdateMaterialsRequestStructure() {
        UpdateMaterialsRequest request = new UpdateMaterialsRequest();
        request.setId(12345L);

        // 测试AcceptedInformationDiagnosisRequest设置
        AcceptedInformationDiagnosisRequest diagnosisRequest = new AcceptedInformationDiagnosisRequest();
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setWorkInjuryBenefitApplicationForm(Collections.singletonList("申请表.pdf"));
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        diagnosisRequest.setApplication(application);

        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();

        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历.pdf"));
        materials.setMedicalRecord(medicalRecord);
        visit.setMaterial(materials);

        diagnosisRequest.setMedical(Collections.singletonList(visit));
        request.setAcceptedInformationDiagnosisRequest(diagnosisRequest);

        // 验证数据结构
        assert request.getId() != null;
        assert request.getId().equals(12345L);
        assert request.getAcceptedInformationDiagnosisRequest() != null;
        assert request.getAcceptedInformationDiagnosisRequest().getApplication() != null;
        assert request.getAcceptedInformationDiagnosisRequest().getMedical() != null;
        assert request.getAcceptedInformationDiagnosisRequest().getMedical().size() == 1;
        assert request.getAcceptedInformationDiagnosisRequest().getApplication().getWorkInjuryBenefitApplicationForm() != null;
        assert request.getAcceptedInformationDiagnosisRequest().getApplication().getIdCard() != null;
        assert request.getAcceptedInformationDiagnosisRequest().getMedical().get(0).getVisitType().equals("住院");

        System.out.println("UpdateMaterialsRequest数据结构测试通过");
    }

    /**
     * 测试空请求体的处理
     */
    @Test
    public void testUpdateMaterialsWithEmptyRequest() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        Long testId = 123456L;

        // 只有ID，没有其他更新数据的请求体
        UpdateMaterialsRequest request = new UpdateMaterialsRequest();
        request.setId(testId);

        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = objectMapper.writeValueAsString(request);

        // 执行测试 - 没有更新数据应该返回错误
        mockMvc.perform(post("/v2/medical/cases/update-materials")
                .contentType("application/json")
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(1001))
                .andExpect(jsonPath("$.message").value("没有提供需要更新的数据"));
    }

    /**
     * 测试缺少ID的请求
     */
    @Test
    public void testUpdateMaterialsWithoutId() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 没有ID的请求体
        UpdateMaterialsRequest request = new UpdateMaterialsRequest();
        AcceptedInformationDiagnosisRequest diagnosisRequest = new AcceptedInformationDiagnosisRequest();
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setIdCard(Collections.singletonList("test_id_card.jpg"));
        diagnosisRequest.setApplication(application);
        request.setAcceptedInformationDiagnosisRequest(diagnosisRequest);

        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = objectMapper.writeValueAsString(request);

        // 执行测试 - 缺少ID应该返回错误
        mockMvc.perform(post("/v2/medical/cases/update-materials")
                .contentType("application/json")
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(1001))
                .andExpect(jsonPath("$.message").value("请求参数不能为空，必须提供案件ID"));
    }
}
