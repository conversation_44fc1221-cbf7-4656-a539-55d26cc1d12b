package com.yixun.wid.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * bean的工具类
 *
 * <AUTHOR>
 */
public class BeanUtils {


    private static Logger logger = LoggerFactory.getLogger(BeanUtils.class);

    /**
     * 复制一个对象到另一个对象，忽略null值字段
     *
     * @param source
     * @param target
     * @param ignoreNull
     */
    public static void copyProperties(Object source, Object target, Boolean ignoreNull) {
        if (target == null) {
            return;
        }

        if (source == null) {
            return;
        }

        if (!ignoreNull) {
            org.springframework.beans.BeanUtils.copyProperties(source, target);
        } else {
            String[] ignoreFiled = getNullField(source);
            org.springframework.beans.BeanUtils.copyProperties(source, target, ignoreFiled);
        }

    }

    public static void copyProperties(Object source, Object target) {
        copyProperties(source, target, false);
    }

    /**
     * 创建并复制一个对象
     *
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public static <T> T copyNew(Object source, Class<T> targetCls) {
        if (source == null) {
            return null;
        }

        T rt;
        try {
            rt = targetCls.newInstance();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        org.springframework.beans.BeanUtils.copyProperties(source, rt);
        return rt;
    }

    /**
     * 复制信息到outList
     *
     * @param list
     * @param cls
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> copyToOutList(List<?> list, Class<T> cls) {
        if (list == null) {
            return null;
        }
        List<T> rtList = null;
        try {
            rtList = list.getClass().newInstance();
            for (Object item : list) {
                T rtItem = cls.newInstance();
                BeanUtils.copyProperties(item, rtItem, false);
                rtList.add(rtItem);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return rtList;
    }

	/**
	 * 复制信息到outList，带page信息
	 * @param list
	 * @param cls
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> Page<T> copyToOutListPage(Page<?> list, Class<T> cls) {
		if (list == null) {
			return null;
		}
		Page<T> outList = new Page<T>();
        copyPageList(list, outList);
		try {
		    if (list.getRecords().isEmpty()) return outList;

		    List<T> outTempList = list.getRecords().getClass().newInstance();
			for (Object item : list.getRecords()) {
				T outItem = cls.newInstance();
				BeanUtils.copyProperties(item, outItem, false);
                outTempList.add(outItem);
			}
            outList.setRecords(outTempList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return outList;
	}

    /**
     * 复制分页信息
     *
     * @param source
     * @param target
     */
    private static void copyPageList(Object source, Object target) {
        String[] ignoreFiled = new String[]{"records"};
        org.springframework.beans.BeanUtils.copyProperties(source, target, ignoreFiled);
    }

    /**
     * 得到 值为null 的字段 （只找当前类，没找父类，因为我们的实体暂时没有继承关系）
     *
     * @param source
     * @return
     */
    public static String[] getNullField(Object source) {
        List<String> fieldList = new ArrayList<>();
        Field[] fields = source.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.get(source) == null) {
                    fieldList.add(field.getName());
                }
            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }

        }

        return fieldList.toArray(new String[]{});
    }

    /**
     * 得到定义的所有字段（返回数组）
     *
     * @return
     */
    public static String[] getDeclareField(Class<?> cls) {
        return getDeclareFieldAsList(cls).toArray(new String[]{});
    }


    /**
     * 得到定义的所有字段(返回list)
     *
     * @return
     */
    public static List<String> getDeclareFieldAsList(Class<?> cls) {
        List<String> fieldList = new ArrayList<>();
        Field[] fields = cls.getDeclaredFields();

        for (Field field : fields) {
            fieldList.add(field.getName());
        }

        return fieldList;
    }

    /**
     * 对象合并（多个类的属性合并到一个类上面）
     *
     * @param sourceBean
     * @param targetBean
     * @return
     */
    public static Object mergerData(Object sourceBean, Object targetBean) {
        Field[] sourceFields = sourceBean.getClass().getDeclaredFields();
        Field[] targetFields = targetBean.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < sourceFields.length; i++) {
                Field sourceField = sourceFields[i];
                //这里遍历主要是为了适应双方对象属性顺序不一致的情况
                for (int j = 0; j < targetFields.length; j++) {
                    Field targetField = targetFields[j];
                    if (sourceField.getName().equals(targetField.getName())) {
                        sourceField.setAccessible(true);
                        targetField.setAccessible(true);
                        if (!(sourceField.get(sourceBean) == null)) {
                            targetField.set(targetBean, sourceField.get(sourceBean));
                        }
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return targetBean;
    }
}
