package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账单信息实体
 */
@Data
@Document("billingInfo")
public class BillingInfo {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的工伤待遇业务ID，多对一关系
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medicalCasesId;

    /**
     * 治疗医院
     */
    private String hospital;

    /**
     * 医院等级
     */
    private Integer hospitalLevel;

    /**
     * 医院性质
     */
    private String hospitalNature;

    /**
     * 账单号
     */
    private String billId;

    /**
     * 账单数
     */
    private Integer billNum;

    /**
     * 治疗类型
     */
    private String treatmentType;

    /**
     * 门诊开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date outpatientStartTime;

    /**
     * 门诊结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date outpatientEndTime;

    /**
     * 门诊天数
     */
    private Integer outpatientDays;

    /**
     * 先期给付类型
     */
    private String advancePaymentType;

    /**
     * 是否追回
     */
    private Boolean isRecovery;

    /**
     * 是否跨统筹区就诊
     */
    private Boolean isCrossPlanningArea;

    /**
     * 电子清单列表，临时字段，不会持久化到数据库
     */
    @Transient
    private List<BillingDetail> billingDetails;

    /**
     * 电子清单分组
     */
    private List<BillingDetailsGroup> billingDetailsGroup;
    
    /**
     * 就诊日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date visitDate;
    
    /**
     * 就诊结束日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date visitEndDate;
    
    /**
     * 出院日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date dischargeDate;

    /**
     * 账单总金额（分）
     */
    private Integer amountInCent;
    
    /**
     * 可报销金额（分）
     */
    private Integer reimbursableAmountInCent;
    
    /**
     * 不可报销金额（分）
     */
    private Integer nonReimbursableAmountInCent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

}
