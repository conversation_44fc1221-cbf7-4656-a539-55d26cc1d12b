package com.yixun.wid.service.impl;

import com.yixun.wid.entity.DataDict;
import com.yixun.wid.entity.OccupationDict;
import com.yixun.wid.service.DataDictService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DataDictServiceImpl implements DataDictService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void insert(List<DataDict> dataDictList) {
        mongoTemplate.insert(dataDictList, DataDict.class);
    }

    @Override
    public void update(DataDict dataDict) {
        mongoTemplate.save(dataDict);
    }

    @Override
    public List<DataDict> getDataDictList(String datatype) {
        Query query = new Query();
        query.addCriteria(Criteria.where("datatype").is(datatype));

        return mongoTemplate.find(query, DataDict.class);
    }

    @Override
    public List<OccupationDict> getOccupationDictList(String parentcode, String datatype) {
        Query query = new Query();
        query.addCriteria(Criteria.where("parentcode").is(parentcode));
        query.addCriteria(Criteria.where("enable").is("true"));
        query.addCriteria(Criteria.where("datatype").is(datatype));

        return mongoTemplate.find(query, OccupationDict.class);
    }

    @Override
    public List<OccupationDict> getAllDataDictList(String datatype, Integer level) {
        Query query = new Query();
        query.addCriteria(Criteria.where("enable").is("true"));
        query.addCriteria(Criteria.where("datatype").is(datatype));
        if (level!=null){
            query.addCriteria(Criteria.where("level").is(level.toString()));
        }

        return mongoTemplate.find(query, OccupationDict.class);
    }
}
