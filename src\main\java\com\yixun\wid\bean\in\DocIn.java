package com.yixun.wid.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DocIn {

    @ApiModelProperty("档案名称")
    private String docName;

    @ApiModelProperty(value = "事故时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentTimeStart;

    @ApiModelProperty(value = "事故时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentTimeEnd;

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty("调查机构ID")
    private Long surveyOrgId;

    @ApiModelProperty("单位名称")
    private String organization;

}
