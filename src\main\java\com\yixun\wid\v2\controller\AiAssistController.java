package com.yixun.wid.v2.controller;

import cn.hutool.core.util.ObjectUtil;
import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.v2.entity.LegalRegulation;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.AiConclusionRequestVO;
import com.yixun.wid.v2.vo.AiConclusionResponseVO;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AI辅助审核Controller
 */
@RestController
@RequestMapping("/v2/ai/assist")
@AllArgsConstructor
public class AiAssistController {

    private final MongoTemplate mongoTemplate;
    private final AiUtils aiUtils;
    private final LegalRegulationController legalRegulationController;

    /**
     * 获取结论审核推荐
     *
     * @param caseId        案件ID（可选）
     * @param declarationId 申报ID（可选）
     * @return AI结论审核推荐结果
     */
    @GetMapping("/conclusion/recommend")
    public CommonResult<AiConclusionResponseVO> getConclusionRecommendation(
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Long declarationId) {

        // 参数验证：确保caseId和declarationId有且仅有一个不为空
        if ((ObjectUtil.isNull(caseId) && ObjectUtil.isNull(declarationId)) ||
            (ObjectUtil.isNotNull(caseId) && ObjectUtil.isNotNull(declarationId))) {
            throw new RuntimeException("caseId和declarationId必须且只能传递其中一个参数");
        }

        // 获取事故经过
        String accidentDetail = getAccidentDetail(caseId, declarationId);

        // 获取所有法规依据数据
        CommonResult<List<LegalRegulation>> legalRegulationsResult =
            legalRegulationController.listAll(null, null);

        if (ObjectUtil.isNull(legalRegulationsResult) || ObjectUtil.isNull(legalRegulationsResult.getData())) {
            throw new RuntimeException("获取法规依据数据失败");
        }

        // 封装AI请求实体
        AiConclusionRequestVO requestVO = new AiConclusionRequestVO();
        requestVO.setAccidentDetail(accidentDetail);
        requestVO.setLegalRegulations(legalRegulationsResult.getData());

        // 调用AI工具类获取推荐结果
        AiConclusionResponseVO responseVO = aiUtils.getConclusionRecommendation(requestVO);

        return CommonResult.successData(responseVO);
    }

    /**
     * 获取事故经过
     *
     * @param caseId        案件ID
     * @param declarationId 申报ID
     * @return 事故经过
     */
    private String getAccidentDetail(Long caseId, Long declarationId) {
        if (ObjectUtil.isNotNull(caseId)) {
            // 从Cases表查询
            Cases cases = mongoTemplate.findById(caseId, Cases.class);
            if (ObjectUtil.isNull(cases)) {
                throw new RuntimeException("案件记录不存在");
            }
            return cases.getAccidentDetail();
        } else {
            // 从Declaration表查询
            Declaration declaration = mongoTemplate.findById(declarationId, Declaration.class);
            if (ObjectUtil.isNull(declaration)) {
                throw new RuntimeException("申报记录不存在");
            }
            return declaration.getAccidentDetail();
        }
    }
}
