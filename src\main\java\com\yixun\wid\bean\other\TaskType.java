package com.yixun.wid.bean.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "待办类型")
@Data
public class TaskType {

    @ApiModelProperty("补充资料")
    public final static String FURTHER_INFO = "furtherInfo";

    @ApiModelProperty("认定终止")
    public final static String DEEMED_TERMINATION = "deemedTermination";

    @ApiModelProperty("开放材料递交方式")
    public final static String OPEN_MATERIAL = "openMaterial";

    @ApiModelProperty("未上传邮寄单号")
    public final static String COURIER_NUMBER= "courierNumber";

    @ApiModelProperty("邮寄——电子签收")
    public final static String POST_SIGN= "postSign";

    @ApiModelProperty("现场——电子签收")
    public final static String LIVE_SIGN= "liveSign";

    @ApiModelProperty("新增报案")
    public final static String ADD_CASE = "addCase";

    @ApiModelProperty("新增申请")
    public final static String ADD_DECLARATION = "addDeclaration";

    @ApiModelProperty("撤销申请")
    public final static String WITHDRAW_DECLARATION = "withdrawDeclaration";

    @ApiModelProperty("新增报案超10天")
    public final static String ADD_DECLARATION_OVER10 = "addDeclarationOver10";

    @ApiModelProperty("报案待认定超45天")
    public final static String IDENTIFYING_OVER45 = "identifyingOver45";

    @ApiModelProperty("补充资料超过3天")
    public final static String FURTHER_INFO_OVER3 = "furtherInfoOver3";

    @ApiModelProperty("认定中止超过3天")
    public final static String DEEMED_TERMINATION_OVER3 = "deemedTerminationOver3";

}
