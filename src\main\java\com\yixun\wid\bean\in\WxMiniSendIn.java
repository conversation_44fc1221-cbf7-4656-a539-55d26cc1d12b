package com.yixun.wid.bean.in;

import com.yixun.wid.bean.other.MessageData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "微信小程序发送订阅消息输入对象")
@Data
public class WxMiniSendIn {

    @ApiModelProperty(value = "小程序类型（developer为开发版、trial为体验版、formal为正式版）", required = true)
    @NotBlank(message = "小程序类型不能为空")
    private String appletsSubType;

    @ApiModelProperty(value = "订阅消息模板id", required = true)
    @NotBlank(message = "订阅消息模板id不能为空")
    private String appSubTempId;

    @ApiModelProperty("跳转小程序地址")
    private String appSubPagePath;

    @ApiModelProperty(value = "小程序appid", required = true)
    @NotBlank(message = "小程序appid不能为空")
    private String appletsAppid;

    @ApiModelProperty(value = "小程序secret")
    private String appletsSecret;

    @ApiModelProperty(value = "小程序消息推送鉴权token")
    private String appletsSubToken;

    @ApiModelProperty(value = "小程序EncodingAESKey")
    private String appletsSubAesKey;

    @ApiModelProperty(value = "数据格式（JSON or XML）", required = true)
    @NotBlank(message = "数据格式不能为空")
    private String appletsSubDataFormat;

    @ApiModelProperty(value = "参数信息", required = true)
    @NotNull(message = "参数信息不能为空")
    private List<MessageData> wxMaSubMsgList;

    @ApiModelProperty(value = "发送用户的openId集合", required = true)
    @NotNull(message = "发送用户不能为空")
    private List<String> openIdList;

}
