package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.DeliveryFileGetIn;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.DeliveryFile;

import java.util.List;

public interface DeliveryFileService {

    void save(DeliveryFile deliveryFile);

    DeliveryFile getById(Long deliveryFileId);

    void update(DeliveryFile deliveryFile);

    List<DeliveryFile> getList(DeliveryFileGetIn getIn, CommonPage commonPage);

    DeliveryFile getByDeclaration(Long declarationId);

    void handleDeliveryFile(Declaration declaration, String step, String fileType, String writSn);

    /**
     * 查询已办结的——PC录入运单号2个自然日后未电子签名的/现场递送：1个工作日后未电子签名的
     */
    void sendDoneMs();

}
