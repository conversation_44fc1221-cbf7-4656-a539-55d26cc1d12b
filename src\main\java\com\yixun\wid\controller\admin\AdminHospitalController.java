package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.HospitalGetIn;
import com.yixun.wid.bean.in.HospitalIn;
import com.yixun.wid.bean.out.HospitalOut;
import com.yixun.wid.entity.Hospital;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.HospitalService;
import com.yixun.wid.utils.BeanFieldCheckingUtils;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "admin医院信息")
@RestController
@RequestMapping(value = "/admin/hospital")
public class AdminHospitalController {

    @Resource
    private HospitalService hospitalService;

    @GetMapping("/getList")
    @ApiOperation("获取医院列表")
    public CommonResult<List<HospitalOut>> getList(HospitalGetIn getIn, CommonPage commonPage) {

        List<Hospital> hospitalList = hospitalService.getList(getIn, commonPage);
        List<HospitalOut> outList = BeanUtils.copyToOutList(hospitalList, HospitalOut.class);

        return CommonResult.successPageData(outList, commonPage);
    }

	@GetMapping("/getList2")
	@ApiOperation("获取医院列表 不鉴权")
	public CommonResult<List<HospitalOut>> getList2(HospitalGetIn getIn, CommonPage commonPage) {

		List<Hospital> hospitalList = hospitalService.getList(getIn, commonPage);
		List<HospitalOut> outList = BeanUtils.copyToOutList(hospitalList, HospitalOut.class);

		return CommonResult.successPageData(outList, commonPage);
	}

    @GetMapping("/get/{hospitalId}")
    @ApiOperation("获取医院详情")
    public CommonResult<HospitalOut> getDetail(@PathVariable("hospitalId") Long hospitalId) {

        Hospital hospital = hospitalService.getById(hospitalId);
        if (hospital==null){
            throw new DataErrorException("该医院信息不存在");
        }
        HospitalOut out = new HospitalOut();
        BeanUtils.copyProperties(hospital, out);

        return CommonResult.successData(out);
    }

    @PostMapping("/doSave")
    @ApiOperation("保存医院信息")
    public CommonResult<Void> doSave(@RequestBody HospitalIn hospitalIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(hospitalIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Hospital hospital = new Hospital();
        hospital.setId(SnGeneratorUtil.getId());
        hospital.setCreateTime(new Date());
        BeanUtils.copyProperties(hospitalIn, hospital);

        hospitalService.save(hospital);

        return CommonResult.successResult("保存成功");
    }

    @PostMapping("/doUpdate/{hospitalId}")
    @ApiOperation("修改医院信息")
    public CommonResult<Void> doUpdate(@PathVariable("hospitalId") Long hospitalId,
                                       @RequestBody HospitalIn hospitalIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAnyFieldNotNull(hospitalIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Hospital hospital = hospitalService.getById(hospitalId);
        if (hospital==null){
            throw new DataErrorException("未找到该医院");
        }
        BeanUtils.copyProperties(hospitalIn, hospital, true);

        hospitalService.update(hospital);

        return CommonResult.successResult("修改成功");
    }

}
