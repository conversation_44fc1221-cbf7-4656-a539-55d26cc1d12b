package com.yixun.wid.v2.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.MedicalInstitutionHierarchy;
import com.yixun.wid.v2.entity.MedicalInstitutions;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.CollectMedicalInstitutions;
import com.yixun.wid.v2.vo.RemoveSubInstitutionIn;
import com.yixun.wid.v2.vo.SimilarAiCheckOut;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 待遇 医疗机构管理接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/medical/institutions")
public class MedicalInstitutionsController {

	@Resource
	private MongoTemplate mongoTemplate;

	@Resource
	private AiUtils aiUtils;

	/**
	 * 分页查询医疗机构列表
	 *
	 * @param name                机构名称（模糊搜索）
	 * @param isAgreementHospital 是否协议机构
	 * @param regions             所在区域，格式为List，例如["省","市","区"]
	 * @param startDateBegin      开始日期的起始值
	 * @param startDateEnd        开始日期的结束值
	 * @param endDateBegin        结束日期的起始值
	 * @param endDateEnd          结束日期的结束值
	 * @param dateBegin           起止日期 查询日期范围的起始值，与dateEnd搭配使用可筛选包含在日期范围内的机构
	 * @param dateEnd             起止日期 查询日期范围的结束值，与dateBegin搭配使用可筛选包含在日期范围内的机构
	 * @param commonPage          分页参数
	 * @return 医疗机构列表（分页）
	 */
	@GetMapping("/list")
	public CommonResult<List<MedicalInstitutions>> list(
			@RequestParam(required = false) String name,
			@RequestParam(required = false) Boolean isAgreementHospital,
			@RequestParam(required = false) List<String> regions,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateBegin,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateEnd,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateBegin,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateEnd,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dateBegin,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dateEnd,
			CommonPage commonPage) {

		Query query = new Query();

		// 机构名称模糊搜索
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("name").regex(".*" + name + ".*", "i"));
		}

		// 是否协议机构筛选
		if (ObjectUtil.isNotNull(isAgreementHospital)) {
			query.addCriteria(Criteria.where("isAgreementHospital").is(isAgreementHospital));
		}

		// 区域筛选 - region是一个list，存的是[省,市,区]
		if (CollUtil.isNotEmpty(regions)) {
			// 如果提供完整的区域列表，可以直接比较整个数组是否相等
			query.addCriteria(Criteria.where("region").is(regions));
		}

		// 如果提供了查询日期范围，优先使用日期范围进行筛选
		if (ObjectUtil.isNotNull(dateBegin) || ObjectUtil.isNotNull(dateEnd)) {
			// 筛选满足以下条件的医疗机构：
			// 1. 如果有开始日期，则开始日期 <= dateEnd (如果有指定结束时间)
			// 2. 如果有结束日期，则结束日期 >= dateBegin (如果有指定开始时间)
			// 3. 如果结束日期为空，则视为一直有效
			Criteria criteria = new Criteria();
			
			if (ObjectUtil.isNotNull(dateBegin)) {
				// 如果机构endDate为空，则视为一直有效；否则endDate必须 >= dateBegin
				criteria.orOperator(
					Criteria.where("endDate").is(null),
					Criteria.where("endDate").gte(dateBegin)
				);
			}
			
			if (ObjectUtil.isNotNull(dateEnd)) {
				// 机构startDate必须 <= dateEnd
				criteria.and("startDate").lte(dateEnd);
			}
			
			query.addCriteria(criteria);
		} else {
			// 没有提供日期范围，使用原有的筛选逻辑
			// 开始日期范围筛选
			if (ObjectUtil.isNotNull(startDateBegin) || ObjectUtil.isNotNull(startDateEnd)) {
				Criteria startDateCriteria = Criteria.where("startDate");
				if (ObjectUtil.isNotNull(startDateBegin)) {
					startDateCriteria.gte(startDateBegin);
				}
				if (ObjectUtil.isNotNull(startDateEnd)) {
					startDateCriteria.lte(startDateEnd);
				}
				query.addCriteria(startDateCriteria);
			}

			// 结束日期范围筛选
			if (ObjectUtil.isNotNull(endDateBegin) || ObjectUtil.isNotNull(endDateEnd)) {
				Criteria endDateCriteria = Criteria.where("endDate");
				if (ObjectUtil.isNotNull(endDateBegin)) {
					endDateCriteria.gte(endDateBegin);
				}
				if (ObjectUtil.isNotNull(endDateEnd)) {
					endDateCriteria.lte(endDateEnd);
				}
				query.addCriteria(endDateCriteria);
			}
		}

		// 添加排序：按创建时间倒序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));

		// 设置分页信息
		MongoUtil.setPageInfo(mongoTemplate, MedicalInstitutions.class, query, commonPage);

		// 执行查询
		List<MedicalInstitutions> hospitals = mongoTemplate.find(query, MedicalInstitutions.class);

		return CommonResult.successPageData(hospitals, commonPage);
	}

	/**
	 * 根据ID获取医疗机构详情
	 *
	 * @param id 医疗机构ID
	 * @return 医疗机构详情
	 */
	@GetMapping("/detail")
	public CommonResult<MedicalInstitutions> getById(@RequestParam("id") Long id) {
		MedicalInstitutions hospital = mongoTemplate.findById(id, MedicalInstitutions.class);
		if (ObjectUtil.isNull(hospital)) {
			return CommonResult.failResult(10001, "医疗机构不存在");
		}
		return CommonResult.successData(hospital);
	}

	/**
	 * 新增医疗机构
	 *
	 * @param hospital 医疗机构信息
	 * @return 操作结果
	 */
	@PostMapping("/save")
	public CommonResult<Void> save(@Validated @RequestBody MedicalInstitutions hospital) {
		// 检查名称和别名是否重复
		DuplicateCheckResult checkResult = checkNameAndAliasesDuplicate(hospital.getName(), hospital.getAliases(), null);
		if (checkResult.isDuplicate()) {
			return CommonResult.failResult(10001, checkResult.getMessage());
		}

		// 检查开始日期是否在结束日期之前
		if (!isDateValid(hospital.getStartDate(), hospital.getEndDate())) {
			return CommonResult.failResult(10001, "开始日期不能晚于结束日期");
		}

		hospital.setId(SnGeneratorUtil.getId());
		hospital.setCreateTime(new Date());
		hospital.setUpdateTime(new Date());
		mongoTemplate.save(hospital);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 更新医疗机构
	 *
	 * @param hospital 医疗机构信息
	 * @return 操作结果
	 */
	@PostMapping("/update")
	public CommonResult<Void> update(@Validated @RequestBody MedicalInstitutions hospital) {
		if (ObjectUtil.isNull(hospital.getId())) {
			return CommonResult.failResult(10001, "ID不能为空");
		}

		MedicalInstitutions existingHospital = mongoTemplate.findById(hospital.getId(), MedicalInstitutions.class);
		if (ObjectUtil.isNull(existingHospital)) {
			return CommonResult.failResult(10001, "医疗机构不存在");
		}

		// 检查名称和别名是否重复
		DuplicateCheckResult checkResult = checkNameAndAliasesDuplicate(hospital.getName(), hospital.getAliases(), hospital.getId());
		if (checkResult.isDuplicate()) {
			return CommonResult.failResult(10001, checkResult.getMessage());
		}
		
		// 检查开始日期是否在结束日期之前
		if (!isDateValid(hospital.getStartDate(), hospital.getEndDate())) {
			return CommonResult.failResult(10001, "开始日期不能晚于结束日期");
		}

		hospital.setUpdateTime(new Date());
		mongoTemplate.save(hospital);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 批量删除医疗机构
	 *
	 * @param ids 医疗机构ID列表
	 * @return 操作结果
	 */
	@PostMapping("/batchDelete")
	public CommonResult<Void> batchDelete(@RequestBody List<Long> ids) {
		if (CollUtil.isEmpty(ids)) {
			return CommonResult.failResult(10001, "ID列表不能为空");
		}

		List<MedicalInstitutions> existingHospitals = new ArrayList<>();
		for (Long id : ids) {
			MedicalInstitutions hospital = mongoTemplate.findById(id, MedicalInstitutions.class);
			if (ObjectUtil.isNotNull(hospital)) {
				existingHospitals.add(hospital);
			}
		}

		if (!existingHospitals.isEmpty()) {
			mongoTemplate.remove(Query.query(Criteria.where("id").in(ids)), MedicalInstitutions.class);
		}

		return CommonResult.successResult("删除成功");
	}

	/**
	 * 根据名字搜索AI相似医疗机构详情列表
	 *
	 * @param name 医疗机构名
	 * @param num      搜索数量
	 * @return 相似企业详情列表
	 */
	@GetMapping(value = "/searchAiSimilarCorp")
	public CommonResult<SimilarAiCheckOut> searchAiSimilarCorp(@RequestParam String name, @RequestParam Integer num) {
		SimilarAiCheckOut similarAiCheckOut = aiUtils.checkSimilar(name, num);
		return CommonResult.successData(similarAiCheckOut);
	}

	/**
	 * 归集医疗机构
	 * 将多个医疗机构关联到一个主医疗机构下，建立层级关系
	 *
	 * @param collectMedicalInstitutions 包含主医疗机构ID和次级医疗机构ID列表
	 * @return 操作结果
	 */
	@PostMapping(value = "/doCollectMedicalInstitutions")
	public CommonResult<String> doCollectMedicalInstitutions(
			@RequestBody CollectMedicalInstitutions collectMedicalInstitutions) {
		List<Long> hospitalIds = collectMedicalInstitutions.getHospitalIds();
		Long mainHospitalId = collectMedicalInstitutions.getMainHospitalId();
		List<Long> allHospitalIds = new ArrayList<>();
		allHospitalIds.add(mainHospitalId);
		allHospitalIds.addAll(hospitalIds);

		// 删除 MedicalInstitutionHierarchy表 中 mainHospitalId 在 allHospitalIds的数据
		Query queryMain = Query.query(Criteria.where("mainHospitalId").in(allHospitalIds));
		mongoTemplate.remove(queryMain, MedicalInstitutionHierarchy.class);

		// 删除 MedicalInstitutionHierarchy表 中 hospitalId 在 allHospitalIds的数据
		Query queryHospital = Query.query(Criteria.where("hospitalId").in(allHospitalIds));
		mongoTemplate.remove(queryHospital, MedicalInstitutionHierarchy.class);

		// 基于 mainHospitalId 和 hospitalIds 创建 MedicalInstitutionHierarchy 关联关系
		List<MedicalInstitutionHierarchy> hierarchyList = new ArrayList<>();
		for (Long hospitalId : hospitalIds) {
			MedicalInstitutionHierarchy hierarchy = new MedicalInstitutionHierarchy();
			hierarchy.setId(SnGeneratorUtil.getId());
			hierarchy.setMainHospitalId(mainHospitalId);
			hierarchy.setHospitalId(hospitalId);
			hierarchyList.add(hierarchy);
		}
		if (!hierarchyList.isEmpty()) {
			mongoTemplate.insertAll(hierarchyList);
		}

		// 更新 MedicalInstitutions表中 mainHospitalId对应id的数据为主机构
		Query mainHospitalQuery = Query.query(Criteria.where("id").is(mainHospitalId));
		Update mainHospitalUpdate = Update.update("isMainHospital", true);
		mongoTemplate.updateFirst(mainHospitalQuery, mainHospitalUpdate, MedicalInstitutions.class);

		// 更新 MedicalInstitutions表中 hospitalIds对应id的数据为次机构
		if (!hospitalIds.isEmpty()) {
			Query subHospitalQuery = Query.query(Criteria.where("id").in(hospitalIds));
			Update subHospitalUpdate = Update.update("isMainHospital", false);
			mongoTemplate.updateMulti(subHospitalQuery, subHospitalUpdate, MedicalInstitutions.class);
		}

		return CommonResult.successResult("修改成功");
	}

	/**
	 * 获取主机构下的所有次级医疗机构列表
	 * 根据主机构ID查询与之关联的所有次级医疗机构信息
	 *
	 * @param mainHospitalId 主医疗机构ID
	 * @return 次级医疗机构列表
	 */
	@GetMapping("/getSubInstitutions")
	public CommonResult<List<MedicalInstitutions>> getSubInstitutions(
			@RequestParam Long mainHospitalId) {

		// 首先查询该机构的详细信息
		MedicalInstitutions mainHospital = mongoTemplate.findById(mainHospitalId, MedicalInstitutions.class);

		if (ObjectUtil.isNull(mainHospital)) {
			return CommonResult.failResult(10001, "未找到该医疗机构信息");
		}

		// 检查该机构是否为主机构
		if (ObjectUtil.isNull(mainHospital.getIsMainHospital()) || !mainHospital.getIsMainHospital()) {
			return CommonResult.failResult(10001, "该机构不是主机构，无法查询下级机构");
		}

		// 查询层级关系表获取与主机构关联的次级机构ID列表
		Query hierarchyQuery = Query.query(Criteria.where("mainHospitalId").is(mainHospitalId));
		List<MedicalInstitutionHierarchy> hierarchies = mongoTemplate.find(hierarchyQuery,
				MedicalInstitutionHierarchy.class);

		if (hierarchies.isEmpty()) {
			return CommonResult.successData(new ArrayList<>());
		}

		// 提取次级机构ID列表
		List<Long> subInstitutionIds = hierarchies.stream()
				.map(MedicalInstitutionHierarchy::getHospitalId)
				.collect(Collectors.toList());

		// 查询次级机构详细信息
		Query institutionsQuery = Query.query(Criteria.where("id").in(subInstitutionIds));
		institutionsQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));

		// 执行查询
		List<MedicalInstitutions> subInstitutions = mongoTemplate.find(institutionsQuery, MedicalInstitutions.class);

		return CommonResult.successData(subInstitutions);
	}

	/**
	 * 获取子机构所属的主机构信息
	 * 根据子机构ID查询其关联的主机构详细信息
	 *
	 * @param hospitalId 子机构ID
	 * @return 主机构信息
	 */
	@GetMapping("/getMainInstitution")
	public CommonResult<MedicalInstitutions> getMainInstitution(@RequestParam Long hospitalId) {

		// 首先查询该机构的详细信息
		MedicalInstitutions hospital = mongoTemplate.findById(hospitalId, MedicalInstitutions.class);

		if (ObjectUtil.isNull(hospital)) {
			return CommonResult.failResult(10001, "未找到该医疗机构信息");
		}

		// 检查该机构是否为主机构
		if (ObjectUtil.isNotNull(hospital.getIsMainHospital()) && hospital.getIsMainHospital()) {
			return CommonResult.failResult(10001, "该机构是主机构，不属于任何其他主机构");
		}

		// 查询层级关系表获取子机构关联的主机构ID
		Query hierarchyQuery = Query.query(Criteria.where("hospitalId").is(hospitalId));
		MedicalInstitutionHierarchy hierarchy = mongoTemplate.findOne(hierarchyQuery,
				MedicalInstitutionHierarchy.class);

		if (ObjectUtil.isNull(hierarchy)) {
			return CommonResult.failResult(10001, "该机构未关联到任何主机构");
		}

		// 获取主机构ID
		Long mainHospitalId = hierarchy.getMainHospitalId();

		// 查询主机构详细信息
		MedicalInstitutions mainInstitution = mongoTemplate.findById(mainHospitalId, MedicalInstitutions.class);

		if (ObjectUtil.isNull(mainInstitution)) {
			return CommonResult.failResult(10001, "未找到对应的主机构信息");
		}

		return CommonResult.successData(mainInstitution);
	}

	/**
	 * 移除主机构下的子机构关联关系
	 * 将子机构从主机构的层级结构中移除
	 *
	 * @param removeSubInstitutionIn 包含主机构ID和子机构ID的参数对象
	 * @return 操作结果
	 */
	@PostMapping("/removeSubInstitution")
	public CommonResult<String> removeSubInstitution(@RequestBody RemoveSubInstitutionIn removeSubInstitutionIn) {

		Long mainHospitalId = removeSubInstitutionIn.getMainHospitalId();
		Long hospitalId = removeSubInstitutionIn.getHospitalId();

		if (ObjectUtil.isNull(mainHospitalId) || ObjectUtil.isNull(hospitalId)) {
			return CommonResult.failResult(10001, "主机构ID和子机构ID不能为空");
		}

		// 首先检查主机构信息
		MedicalInstitutions mainHospital = mongoTemplate.findById(mainHospitalId, MedicalInstitutions.class);
		if (ObjectUtil.isNull(mainHospital)) {
			return CommonResult.failResult(10001, "未找到主机构信息");
		}

		// 检查是否为主机构
		if (ObjectUtil.isNull(mainHospital.getIsMainHospital()) || !mainHospital.getIsMainHospital()) {
			return CommonResult.failResult(10001, "该机构不是主机构，无法执行移除操作");
		}

		// 检查子机构信息
		MedicalInstitutions subInstitution = mongoTemplate.findById(hospitalId, MedicalInstitutions.class);
		if (ObjectUtil.isNull(subInstitution)) {
			return CommonResult.failResult(10001, "未找到子机构信息");
		}

		// 查询层级关系是否存在
		Query hierarchyQuery = Query.query(
				Criteria.where("mainHospitalId").is(mainHospitalId)
						.and("hospitalId").is(hospitalId));

		MedicalInstitutionHierarchy hierarchy = mongoTemplate.findOne(hierarchyQuery,
				MedicalInstitutionHierarchy.class);

		if (ObjectUtil.isNull(hierarchy)) {
			return CommonResult.failResult(10001, "未找到关联关系，该子机构可能不属于指定的主机构");
		}

				// 移除层级关系
		mongoTemplate.remove(hierarchyQuery, MedicalInstitutionHierarchy.class);

		// 更新子机构的isMainHospital属性为null
		Query subQuery = Query.query(Criteria.where("id").is(hospitalId));
		Update subUpdate = Update.update("isMainHospital", null);
		mongoTemplate.updateFirst(subQuery, subUpdate, MedicalInstitutions.class);

		// 检查主机构下是否还有其他子机构
		Query remainingQuery = Query.query(Criteria.where("mainHospitalId").is(mainHospitalId));
		long count = mongoTemplate.count(remainingQuery, MedicalInstitutionHierarchy.class);

		// 如果主机构下没有其他子机构，则将主机构也设置为非主机构
		if (count == 0) {
			Query mainQuery = Query.query(Criteria.where("id").is(mainHospitalId));
			Update mainUpdate = Update.update("isMainHospital", null);
			mongoTemplate.updateFirst(mainQuery, mainUpdate, MedicalInstitutions.class);
		}

		return CommonResult.successResult("移除成功");
	}

	/**
	 * 检查医疗机构名称和别名是否重复
	 *
	 * @param name 机构名称
	 * @param aliases 机构别名列表
	 * @param excludeId 排除的机构ID（更新时使用）
	 * @return 检查结果，包含是否重复以及重复信息
	 */
	private DuplicateCheckResult checkNameAndAliasesDuplicate(String name, List<String> aliases, Long excludeId) {
		DuplicateCheckResult result = new DuplicateCheckResult();
		result.setDuplicate(false);

		// 1. 首先检查传入的别名列表中是否存在重复项
		if (CollUtil.isNotEmpty(aliases)) {
			// 使用Set来检测重复项
			Set<String> uniqueAliases = new HashSet<>();
			for (String alias : aliases) {
				if (!uniqueAliases.add(alias)) {
					result.setDuplicate(true);
					result.setMessage("传入的别名列表中存在重复项: '" + alias + "'");
					return result;
				}
			}

			// 2. 检查别名中是否包含当前机构名称
			if (StrUtil.isNotBlank(name) && aliases.contains(name)) {
				result.setDuplicate(true);
				result.setMessage("医疗机构别名不能与机构名称 '" + name + "' 相同");
				return result;
			}
		}

		// 3. 检查机构名称是否与数据库中的名称重复
		if (StrUtil.isNotBlank(name)) {
			Query nameQuery = Query.query(Criteria.where("name").is(name));
			if (excludeId != null) {
				nameQuery.addCriteria(Criteria.where("_id").ne(excludeId));
			}
			MedicalInstitutions existingByName = mongoTemplate.findOne(nameQuery, MedicalInstitutions.class);
			if (existingByName != null) {
				result.setDuplicate(true);
				result.setMessage("医疗机构名称 '" + name + "' 已存在");
				return result;
			}
		}

		// 4. 检查机构别名列表与数据库中的名称和别名是否重复
		if (CollUtil.isNotEmpty(aliases)) {
			// 逐个检查每个别名
			for (String alias : aliases) {
				// 4.1 检查别名是否与其他机构的名称重复
				Query aliasNameQuery = Query.query(Criteria.where("name").is(alias));
				if (excludeId != null) {
					aliasNameQuery.addCriteria(Criteria.where("_id").ne(excludeId));
				}
				MedicalInstitutions existingByAlias = mongoTemplate.findOne(aliasNameQuery, MedicalInstitutions.class);
				if (existingByAlias != null) {
					result.setDuplicate(true);
					result.setMessage("医疗机构别名 '" + alias + "' 与已有机构名称 '" + existingByAlias.getName() + "' 重复");
					return result;
				}

				// 4.2 检查别名是否与其他机构的别名重复
				Query aliasQuery = new Query();
				aliasQuery.addCriteria(Criteria.where("aliases").is(alias));
				if (excludeId != null) {
					aliasQuery.addCriteria(Criteria.where("_id").ne(excludeId));
				}
				MedicalInstitutions existingByAliasName = mongoTemplate.findOne(aliasQuery, MedicalInstitutions.class);
				if (existingByAliasName != null) {
					result.setDuplicate(true);
					result.setMessage("医疗机构别名 '" + alias + "' 与已有机构 '" + existingByAliasName.getName() + "' 的别名重复");
					return result;
				}
			}
		}

		return result;
	}

	/**
	 * 检查日期有效性
	 * 确保开始日期在结束日期之前
	 *
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 如果日期有效返回true，否则返回false
	 */
	private boolean isDateValid(Date startDate, Date endDate) {
		// 只有当两个日期都不为空时才进行比较
		if (startDate != null && endDate != null) {
			// 如果开始日期晚于结束日期，返回false
			return !startDate.after(endDate);
		}
		// 如果有一个日期为空，则认为日期有效
		return true;
	}

	/**
	 * 检查结果对象
	 */
	@Data
	public static class DuplicateCheckResult {
		/**
		 * 是否重复
		 */
		private boolean duplicate;

		/**
		 * 重复信息提示
		 */
		private String message;
	}

	/**
	 * 检查医疗机构名称或别名是否重复
	 *
	 * @param name 机构名称
	 * @param alias 单个别名
	 * @param id 排除的机构ID（更新时使用）
	 * @return 检查结果
	 */
	@GetMapping("/checkDuplicate")
	public CommonResult<DuplicateCheckResult> checkDuplicate(
			@RequestParam(required = false) String name,
			@RequestParam(required = false) String alias,
			@RequestParam(required = false) Long id) {

		// 如果名称和别名都为空，返回错误
		if (StrUtil.isBlank(name) && StrUtil.isBlank(alias)) {
			DuplicateCheckResult result = new DuplicateCheckResult();
			result.setDuplicate(false);
			result.setMessage("请提供医疗机构名称或别名进行检查");
			return CommonResult.successData(result);
		}

		// 创建别名列表
		List<String> aliases = new ArrayList<>();
		if (StrUtil.isNotBlank(alias)) {
			aliases.add(alias);
		}

		// 调用检查方法
		DuplicateCheckResult result = checkNameAndAliasesDuplicate(name, aliases, id);
		return CommonResult.successData(result);
	}

	/**
	 * 批量检查医疗机构别名是否重复
	 *
	 * @param name 机构名称
	 * @param aliases 别名列表
	 * @param id 排除的机构ID（更新时使用）
	 * @return 检查结果
	 */
	@PostMapping("/checkDuplicateBatch")
	public CommonResult<DuplicateCheckResult> checkDuplicateBatch(
			@RequestParam(required = false) String name,
			@RequestBody(required = false) List<String> aliases,
			@RequestParam(required = false) Long id) {

		// 如果名称和别名都为空，返回错误
		if (StrUtil.isBlank(name) && CollUtil.isEmpty(aliases)) {
			DuplicateCheckResult result = new DuplicateCheckResult();
			result.setDuplicate(false);
			result.setMessage("请提供医疗机构名称或别名进行检查");
			return CommonResult.successData(result);
		}

		// 调用检查方法
		DuplicateCheckResult result = checkNameAndAliasesDuplicate(name, aliases, id);
		return CommonResult.successData(result);
	}

	/**
	 * 批量检查别名列表是否存在重复项
	 *
	 * @param aliases 需要检查的别名列表
	 * @return 检查结果
	 */
	@PostMapping("/checkAliasesDuplication")
	public CommonResult<List<AliasCheckResult>> checkAliasesDuplication(@RequestBody List<String> aliases) {
		List<AliasCheckResult> results = new ArrayList<>();

		if (CollUtil.isEmpty(aliases)) {
			return CommonResult.successData(results);
		}

		// 检查别名列表内部重复
		Set<String> uniqueAliases = new HashSet<>();
		for (int index = 0; index < aliases.size(); index++) {
			String alias = aliases.get(index);
			AliasCheckResult result = new AliasCheckResult();
			result.setIndex(index);
			result.setAlias(alias);
			result.setValid(true);

			// 检查别名是否为空
			if (StrUtil.isBlank(alias)) {
				result.setValid(false);
				result.setMessage("别名不能为空");
				results.add(result);
				continue;
			}

			// 检查别名是否重复
			if (!uniqueAliases.add(alias)) {
				result.setValid(false);
				result.setMessage("别名在列表中重复");
				results.add(result);
				continue;
			}

			// 检查别名是否与数据库中的名称或别名重复
			DuplicateCheckResult dupResult = checkNameAndAliasesDuplicate(null, Collections.singletonList(alias), null);
			if (dupResult.isDuplicate()) {
				result.setValid(false);
				result.setMessage(dupResult.getMessage());
			}

			results.add(result);
		}

		return CommonResult.successData(results);
	}

	/**
	 * 别名检查结果
	 */
	@Data
	public static class AliasCheckResult {
		/**
		 * 别名在列表中的索引
		 */
		private int index;

		/**
		 * 别名内容
		 */
		private String alias;

		/**
		 * 是否有效
		 */
		private boolean valid;

		/**
		 * 错误信息
		 */
		private String message;
	}

}
