package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 业务类型
 */
@Data
public class BizType {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 业务类型名称
	 */
	private String bizTypeName;

	/**
	 * 禁用状态（0：未禁用，1：已禁用）
	 */
	private Integer status;

	/**
	 * 排序
	 */
	private Long sort;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date updateTime;

	/**
	 * 是否删除（0：未删除，1：已删除）
	 */
	private Integer deleted;

}
