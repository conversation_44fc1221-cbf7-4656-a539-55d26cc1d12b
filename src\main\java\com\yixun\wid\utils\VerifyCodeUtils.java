package com.yixun.wid.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Random;

@Component
public class VerifyCodeUtils {

	@Value("${spring.profiles.active}")
	private String env;

	private static String profile;

	@PostConstruct
	private void init(){
		profile = env;
	}

	/**
	 * 不可实例化
	 */
	private VerifyCodeUtils() {
	}

	/**
	 * 获取验证码
	 */
	public static String getCode() {
		if (!profile.equals("product"))
			return "111111";
		Random ran = new Random();
		int bitField = 0;
		char[] chs = new char[6];
		for (int i = 0; i < 6; i++) {
			while (true) {
				int k = ran.nextInt(10);
				if ((bitField & (1 << k)) == 0) {
					bitField |= 1 << k;
					chs[i] = (char) (k + '0');
					break;
				}
			}
		}
		return (new String(chs));
	}

}
