package com.yixun.wid.exception;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(DataErrorException.class)
    public CommonResult data(DataErrorException e) {
        log.info("DataErrorException: ", e);
        return CommonResult.failResult(CommonErrorInfo.code_6001, e.getMessage());
    }

    @ExceptionHandler(UnLoginException.class)
    public CommonResult unLogin(UnLoginException e) {
        log.info("UnLoginException: ", e);
        return CommonResult.failResult(CommonErrorInfo.code_4001, e.getMessage());
    }

    @ExceptionHandler(PermissionErrorException.class)
    public CommonResult unLogin(PermissionErrorException e) {
        log.info("PermissionErrorException: ", e);
        return CommonResult.failResult(CommonErrorInfo.code_2001, e.getMessage());
    }

    @ExceptionHandler(ParameterErrorException.class)
    public CommonResult parameter(ParameterErrorException e) {
        log.info("ParameterErrorException: ", e);
        return CommonResult.failResult(CommonErrorInfo.code_1001, e.getMessage());
    }

    @ExceptionHandler(RuntimeException.class)
    public CommonResult runtime(RuntimeException e) {
        log.info("RuntimeException: ", e);
        // return CommonResult.failResult(CommonErrorInfo.code_3001, "系统异常，请联系管理员");
        return CommonResult.failResult(CommonErrorInfo.code_3001, e.getMessage());
    }

    @ExceptionHandler(ClientAbortException.class)
    public String runtime(ClientAbortException e) {
        log.info("ClientAbortException: ", e);
        return "ClientAbortException:" + e.getMessage();
    }
}
