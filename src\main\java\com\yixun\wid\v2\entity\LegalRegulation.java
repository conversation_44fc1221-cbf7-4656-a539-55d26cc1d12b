package com.yixun.wid.v2.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

/**
 * 法规依据实体类
 */
@Data
public class LegalRegulation {

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 法规名称
     */
    private String regulationName;

    /**
     * 法规状态（0：未启用，1：已启用）
     */
    private Integer status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
