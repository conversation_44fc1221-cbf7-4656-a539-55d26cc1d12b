package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CorpAgencyGetIn;
import com.yixun.wid.bean.in.CorpAuthIn;
import com.yixun.wid.bean.out.CorpAgencyOut;
import com.yixun.wid.entity.CorpAgency;
import com.yixun.wid.entity.Corporation;
import com.yixun.wid.entity.em.CorpAuthStatus;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.CorpAgencyService;
import com.yixun.wid.service.CorporationService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin托管企业")
@RestController
@RequestMapping(value = "/admin/corpAgency")
public class AdminCorpAgencyController {

    @Resource
    private CorpAgencyService corpAgencyService;

    @Resource
    private CorporationService corporationService;

    @Resource
    private CasesService casesService;

    @GetMapping("/getList")
    @ApiOperation("获取托管企业列表")
    public CommonResult<List<CorpAgencyOut>> getList(CorpAgencyGetIn corpAgencyGetIn, CommonPage commonPage) {

        List<CorpAgency> corpAgencyList = corpAgencyService.getPage(corpAgencyGetIn, commonPage);
        List<CorpAgencyOut> outList = BeanUtils.copyToOutList(corpAgencyList, CorpAgencyOut.class);

        List<Long> corpIds = outList.stream().map(CorpAgencyOut::getCorporationId).collect(Collectors.toList());
        List<Corporation> corporationList = corporationService.getByIds(corpIds);

        for (CorpAgencyOut out : outList){
            Optional<Corporation> optional = corporationList.stream().filter(c -> c.getId().equals(out.getCorporationId())).findFirst();
            if (optional.isPresent()){
                Corporation corporation = optional.get();
                Long id = out.getId(); //原id记下来
                Date createTime = out.getCreateTime();
                BeanUtils.copyProperties(corporation, out, true);
                out.setId(id);
                out.setCreateTime(createTime);
            }
        }

        return CommonResult.successPageData(outList, commonPage);
    }

    @GetMapping("/getListByAgency")
    @ApiOperation("获取指定托管企业的被托管列表")
    public CommonResult<List<CorpAgencyOut>> getListByAgency(Long agencyCorpId) {

        List<CorpAgency> corpAgencyList = corpAgencyService.getList(agencyCorpId);
        List<CorpAgencyOut> outList = BeanUtils.copyToOutList(corpAgencyList, CorpAgencyOut.class);

        return CommonResult.successData(outList);
    }

    @GetMapping("/get/{corpAgencyId}")
    @ApiOperation("获取企业信息详情")
    public CommonResult<CorpAgencyOut> getDetail(@PathVariable("corpAgencyId") Long corpAgencyId) {

        CorpAgency corpAgency = corpAgencyService.getById(corpAgencyId);
        if (corpAgency==null){
            throw new DataErrorException("托管企业信息不存在");
        }
        Corporation corporation = corporationService.getById(corpAgency.getCorporationId());
        if (corporation==null){
            throw new DataErrorException("该企业信息不存在");
        }
        CorpAgencyOut out = new CorpAgencyOut();
        BeanUtils.copyProperties(corporation, out, true);
        BeanUtils.copyProperties(corpAgency, out, true);

        return CommonResult.successData(out);
    }

    @PostMapping("/setAgencyAuth/{corpAgencyId}")
    @ApiOperation("设置托管企业认证")
    public CommonResult<Void> setCorpAuth(@PathVariable("corpAgencyId") Long corpAgencyId,
                                          @RequestBody CorpAuthIn corpAuthIn) {

        CorpAgency corpAgency = corpAgencyService.getById(corpAgencyId);
        if (corpAgency==null){
            throw new DataErrorException("托管企业信息不存在");
        }
        corpAgency.setAgencyStatus(corpAuthIn.getStatus().name());
        if (corpAuthIn.getStatus().equals(CorpAuthStatus.Rejected)){
            corpAgency.setReason(corpAuthIn.getReason());
        }else if (corpAuthIn.getStatus().equals(CorpAuthStatus.ManualAuthenticated)){
            Corporation corporation = corporationService.getById(corpAgency.getCorporationId());
            if (corporation.getUserId()==null || corporation.getStatus().equals(CorpAuthStatus.Rejected.name())){
                corporation.setStatus(CorpAuthStatus.ManualAuthenticated.name());
                corporation.setIsManaged(true);
                corporationService.update(corporation);
                casesService.updateCorpName(corporation.getCompanyName(), corporation.getId());
            }
            corpAgency.setReason("");
        }else {
            corpAgency.setReason("");
        }

        corpAgencyService.update(corpAgency);

        return CommonResult.successResult("设置成功");
    }

}