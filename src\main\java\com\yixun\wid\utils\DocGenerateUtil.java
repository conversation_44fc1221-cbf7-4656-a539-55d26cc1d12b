package com.yixun.wid.utils;

import cn.hutool.core.io.FileUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Description 文档操作工具类
 * <AUTHOR>
 * @Date 2024-08-06
 */
public class DocGenerateUtil {

    /**
     * @param dataMap        数据
     * @param templatePath   模板
     * @param outputPath     输出路径
     * @param outputFileName 输出文件名称
     * @param collectionNames 集合的名字
     */
    public static void generateWord(Map<String, Object> dataMap, String templatePath, String outputPath, String outputFileName, List<String> collectionNames) {
        // 获取 Word 模板所在路径
        ClassPathResource classPathResource = new ClassPathResource("file/" + templatePath);
        try {
            XWPFTemplate template = null;
            if (!CollectionUtils.isEmpty(collectionNames)) {
                // 给标签绑定插件，这里就绑定表格行循环的插件
                ConfigureBuilder builder = Configure.builder();
                collectionNames.forEach(e -> {
                    builder.bind(e, new LoopRowTableRenderPolicy());
                });
                Configure build = builder.build();
                template = XWPFTemplate.compile(classPathResource.getInputStream(), build).render(dataMap);
            } else {
                // 通过 XWPFTemplate 编译文件并渲染数据到模板中
                template = XWPFTemplate.compile(classPathResource.getInputStream()).render(dataMap);
            }
            if (!FileUtil.exist(outputPath)) {
                // 如果路径不存在，则创建路径
                FileUtil.mkdir(outputPath);
            }
            // 将完成数据渲染的文档写出
            template.writeAndClose(new FileOutputStream(outputPath + outputFileName));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
