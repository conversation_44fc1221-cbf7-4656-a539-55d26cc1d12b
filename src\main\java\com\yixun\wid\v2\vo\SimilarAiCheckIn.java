package com.yixun.wid.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对公司名称处理后，
 * 在进行相似度查询(数据来源:生产数据库corporation_数据返回有修改)
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimilarAiCheckIn {
    @ApiModelProperty("公司名称")
    private String company;
    @ApiModelProperty("相似度阈值")
    private Integer topn;
}
