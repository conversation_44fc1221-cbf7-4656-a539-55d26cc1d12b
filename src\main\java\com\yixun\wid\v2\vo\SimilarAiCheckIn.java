package com.yixun.wid.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 医疗机构相似度查询请求参数
 * 对医疗机构名称进行相似度查询
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimilarAiCheckIn {
    @ApiModelProperty("医疗机构名称")
    private String company;
    @ApiModelProperty("返回相似结果数量")
    private Integer topn;
}
