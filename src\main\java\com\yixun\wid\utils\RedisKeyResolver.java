package com.yixun.wid.utils;

public class RedisKeyResolver {

    public static final String USERTOKEN =  "USERTOKEN:";
    public static final String USERINFO =  "USERINFO:";
    public static final String ADMINUSER =  "ADMINUSER:";
    public static final String ADMINCODE =  "ADMINCODE:";
    public static final String MATERIAL_CLASSIFICATION_PROGRESS = "MATERIAL_CLASSIFICATION_PROGRESS:";

    public static String getUserToken(String loginToken, String loginType) {
        return USERTOKEN + loginToken + "_" + loginType;
    }

    public static String getUserInfoKey(Long userId) {
        return USERINFO + userId;
    }

    public static String getAdminUserToken(String loginToken) {
        return ADMINUSER + loginToken;
    }

    public static String getAdminCode(String adminCode) {
        return ADMINCODE + adminCode;
    }

    public static String getMaterialClassificationProgressKey(Long id) {
        return MATERIAL_CLASSIFICATION_PROGRESS + id;
    }
}
