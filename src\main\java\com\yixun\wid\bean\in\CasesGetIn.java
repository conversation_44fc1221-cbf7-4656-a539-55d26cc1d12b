package com.yixun.wid.bean.in;

import com.yixun.wid.entity.em.CasesStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CasesGetIn {

    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "伤害部位")
    private String injuredPart;

    @ApiModelProperty(value = "报案状态")
    private CasesStatus status;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("事故开始时间")
    private Date startDate;

    @ApiModelProperty("事故开始时间")
    private Date endDate;

}
