package com.yixun.wid.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.in.CasesGetIn;
import com.yixun.wid.bean.out.CasesOut;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Api(tags = "api报案信息")
@RestController
@RequestMapping(value = "/api/cases")
public class ApiCasesController {

    @Resource
    private CasesService casesService;

    @Resource
    private AdministratorService administratorService;

    @GetMapping("/getList")
    @ApiOperation("获取报案详情列表")
    public CommonResult<List<CasesOut>> get(@RequestParam List<Long> caseIds) {

        if (caseIds.isEmpty()) {
            return CommonResult.successData(new ArrayList<>());
        }
        List<Cases> casesList = casesService.getByIdList(caseIds);
        List<CasesOut> outList = BeanUtils.copyToOutList(casesList, CasesOut.class);

        return CommonResult.successData(outList);
    }

    @GetMapping("/getAllAdmin")
    @ApiOperation("获取全部用户")
    public CommonResult<List<Administrator>> getAllAdmin() {
        List<Administrator> administrators = administratorService.list(new QueryWrapper<Administrator>().eq("is_disable", 0));
        return CommonResult.successData(administrators);
    }

    @GetMapping("/getAllList")
    @ApiOperation("获取报案列表")
    public CommonResult<List<CasesOut>> getList(CasesGetIn casesGetIn, CommonPage commonPage) {
        List<Cases> casesList = casesService.getCasesList(casesGetIn, commonPage);
        List<CasesOut> outList = BeanUtils.copyToOutList(casesList, CasesOut.class);
        return CommonResult.successPageData(outList, commonPage);
    }

}
