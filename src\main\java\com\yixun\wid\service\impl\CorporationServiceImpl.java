package com.yixun.wid.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseRequest;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseResponse;
import com.aliyun.teaopenapi.models.Config;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.CorporationGetIn;
import com.yixun.wid.entity.Corporation;
import com.yixun.wid.service.CorporationService;
import com.yixun.wid.utils.MongoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CorporationServiceImpl implements CorporationService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public Corporation getByUserId(Long userId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId));

        return mongoTemplate.findOne(query, Corporation.class);
    }

    @Override
    public Corporation recognize(String businessLicenseUrl) {
        Config config = new Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId("LTAI5tDeBnX8W77gQJ2BUFcF")
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret("******************************");
        // 访问的域名
        config.endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
        Client client = null;
        try {
            client = new Client(config);
            RecognizeBusinessLicenseRequest recognizeBusinessLicenseRequest = new RecognizeBusinessLicenseRequest().setUrl(businessLicenseUrl);
            // 复制代码运行请自行打印 API 的返回值
            RecognizeBusinessLicenseResponse recognizeBusinessLicenseResponse = client.recognizeBusinessLicense(recognizeBusinessLicenseRequest);
            String data = recognizeBusinessLicenseResponse.getBody().getData();
            return JSON.parseObject(data).getObject("data", Corporation.class);
        } catch (Exception e){
            log.error("识别失败" + e.getMessage());
            throw new RuntimeException("识别失败");
        }
    }

    @Override
    public void update(Corporation corporation) {
        corporation.setUpdateTime(new Date());
        mongoTemplate.save(corporation);
    }

    @Override
    public List<Corporation> getCorporationList(CorporationGetIn corporationGetIn, CommonPage commonPage) {
        Query query = new Query();
        if (corporationGetIn.getCompanyName()!=null){
            query.addCriteria(Criteria.where("companyName").regex(corporationGetIn.getCompanyName()));
        }
        if (corporationGetIn.getStatus()!=null){
            query.addCriteria(Criteria.where("status").is(corporationGetIn.getStatus().name()));
        }
        if (corporationGetIn.getIsSelfAuth()!=null && corporationGetIn.getIsSelfAuth()){
            query.addCriteria(Criteria.where("userId").ne(null));
        }
        query.with(Sort.by(Sort.Order.asc("status"), Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, Corporation.class, query, commonPage);
        return mongoTemplate.find(query, Corporation.class);
    }

    @Override
    public Corporation getById(Long corporationId) {
        return mongoTemplate.findById(corporationId, Corporation.class);
    }

    @Override
    public List<Corporation> getByIds(List<Long> corpIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(corpIds));

        return mongoTemplate.find(query, Corporation.class);
    }
}
