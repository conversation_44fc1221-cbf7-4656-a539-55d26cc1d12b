package com.yixun.wid.utils;

import com.yixun.bean.CommonPage;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

public class MongoUtil {

    public static <T> void setPageInfo(MongoTemplate mongoTemplate, Class<T> cls, Query query, CommonPage commonPage){
        long totalCount = mongoTemplate.count(query, cls);
        int pageSize = commonPage.getPageSize();
        if (totalCount <= 0) {
            commonPage.setTotal(0);
            commonPage.setPages(0);
        }else {
            commonPage.setTotal(totalCount);
            commonPage.setPages(totalCount%pageSize==0 ? totalCount/pageSize : totalCount/pageSize + 1);
        }
        query.skip((long)(commonPage.getPageNum() - 1) * pageSize).limit(pageSize);
    }
}
