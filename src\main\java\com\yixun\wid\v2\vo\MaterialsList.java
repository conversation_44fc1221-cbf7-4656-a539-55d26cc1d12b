package com.yixun.wid.v2.vo;

import lombok.Data;

import java.util.List;

/**
 * 材料清单
 */
@Data
public class MaterialsList {

	/**
	 * 是否具备材料要求
	 */
	private Integer materialRequirements;

	/**
	 *  告知类文书材料
	 */
	private Integer notifyDoc;

	/**
	 * 材料名称列表
	 */
	private List<String>  materialNameList;

	/**
	 * 清单生成条件
	 */
	private Integer ListGenConditions;

	/**
	 * 生成条件列表
	 */
	private List<GenConditions> conditions;

	/**
	 * 材料清单
	 */
	private List<Materials> materials;

	/**
	 * 材料
	 */
	@Data
	public static class Materials {

		/**
		 *  材料名称
		 */
		private String name;

		/**
		 * 材料来源 0政府部门核发 1申请人自备
		 */
		private Integer from;

		/**
		 * 是否必要 0否 1是
		 */
		private Integer necessary;

		/**
		 * 文件类型 0普通电子文件 1纸质或电子
		 */
		private Integer fileType;

		/**
		 * 文件模板
		 */
		private String docTemplates;

		/**
		 * 样表模板
		 */
		private String sampleFormTemplate;

		/**
		 * 条件配置
		 */
		private List<GenConditionsOptions> genConditionsOptions;

	}

	@Data
	public static class GenConditionsOptions {

		private String conditionName;

		private String optionName;

	}


}
