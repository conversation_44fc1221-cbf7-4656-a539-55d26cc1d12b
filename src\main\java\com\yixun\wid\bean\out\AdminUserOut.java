package com.yixun.wid.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdminUserOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    private String username;

    private String realName;

    private String phone;

    private String email;

    private String avatar;

    private String type;

    private String government;

    @JsonSerialize(using = LongJsonSerializer.class)
    @ApiModelProperty(value = "部门id")
    private Long groupId;

    @ApiModelProperty(value = "部门名称")
    private String groupName;

    private List<String> roles;

    private List<String> permissions;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date lastLoginTime;

    private String lastLoginIp;

    public void setRoles(String roles) {
        this.roles = JSON.parseArray(roles, String.class);
    }

}