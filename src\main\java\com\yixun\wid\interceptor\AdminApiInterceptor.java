package com.yixun.wid.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.util.DateUtil;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.utils.AesCbcUtil;
import com.yixun.wid.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * ApiInterceptor - 前端Api调用权限
 */
@Component
@Slf4j
public class AdminApiInterceptor extends HandlerInterceptorAdapter {

    private static final String TOKEN_NAME = "ApiToken";

    @Value("${api.surveyApi}")
    private String surveyApi;

    @Value("${api.token}")
    private String apiToken;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) {
        log.info("开始执行，时间：{}", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss SSS"));
        // 获取请求的URL地址
        String url = request.getRequestURI();
        //1、解析token，获取参数appId
        String tokenValue = request.getHeader(TOKEN_NAME);
        if (!StringUtils.hasLength(tokenValue)) {
            throw new RuntimeException("ApiToken未传！");
        }
        //查询接口是否配置
        String serverName = httpServletRequest.getServerName(); // 获取域名
        int serverPort = httpServletRequest.getServerPort(); // 获取端口
        if (serverPort != 80 && serverPort != 443) {
            serverName = serverName + ":" + serverPort;
        }
        log.info("serverName为：{}", serverName);
        Boolean result = this.checkToken(serverName, tokenValue);
        if (!result) {
            throw new RuntimeException("暂无此接口权限，请联系管理员！");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        log.info("结束执行，时间：{}", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss SSS"));
        super.afterCompletion(request, response, handler, ex);
    }

    public Boolean checkToken(String apiPath, String token) {
        Map<String, Object> params = new HashMap<>();
        params.put("apiPath", apiPath);
        params.put("token", token);
        try {
            JSONObject connGet = OkHttpKit.apiPostWithToken(surveyApi + "/api/orgApi/checkToken",
                    JSON.toJSONString(params), apiToken);
            log.info(JSON.toJSONString(connGet));
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                Boolean result = (Boolean) connGet.get("success");
                return result;
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

}