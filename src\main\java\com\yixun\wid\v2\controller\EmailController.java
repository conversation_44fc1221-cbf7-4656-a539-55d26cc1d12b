package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonResult;
import com.yixun.wid.service.ShortMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件发送控制器
 */
@Slf4j
@Api(tags = "邮件发送")
@RestController
@RequestMapping("/v2/email")
public class EmailController {

	@Resource
	private ShortMsgService shortMsgService;

	/**
	 * 发送短信通知
	 *
	 * @return 发送结果
	 */
	@GetMapping("/sendSms")
	@ApiOperation("发送短信通知")
	public CommonResult<String> sendSms() {
		//发送短信
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("phone", "13890742729");
		paramMap.put("name", "杨佳豪");
		shortMsgService.sendSMS("SMS_470570107", paramMap);
		return CommonResult.successResult("短信发送成功");
	}

}
