package com.yixun.wid.bean.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.entity.*;
import com.yixun.wid.utils.DateJsonSerializer;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeclarationOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @ApiModelProperty(value = "报案状态")
    private String status;

    @ApiModelProperty(value = "申报子状态")
    private String subStatus;

    @ApiModelProperty(value = "是否锁定")
    private Boolean isLocked;

    @ApiModelProperty(value = "是否admin参与了")
    private Boolean isAdminInvolved;

    @ApiModelProperty(value = "撤销前状态")
    private String statusBeforeCancel;

    @ApiModelProperty(value = "撤销前子状态")
    private String subStatusBeforeCancel;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "上报人的用户id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "来源案件id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long casesId;

    @ApiModelProperty(value = "案件号")
    private String caseSn;

    @ApiModelProperty(value = "上报方式")
    private String submitWay;

    //1、职工信息
    //报案人信息
    @ApiModelProperty(value = "与当事人关系")
    private String relationship;

    @ApiModelProperty(value = "与当事人关系详情")
    private String relationshipDetail;

    @ApiModelProperty(value = "申报人/经办人姓名")
    private String applicantName;

    @ApiModelProperty(value = "申报人/经办人手机")
    private String applicantPhone;

    //工伤当事人参保信息
    @ApiModelProperty(value = "是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty(value = "参保地")
    private String insuranceAddress;

    //工伤当事人信息
    @ApiModelProperty(value = "职工姓名")
    private String name;

    @ApiModelProperty(value = "职工性别")
    private String gender;

    @ApiModelProperty(value = "出生年月日")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date birthdayDate;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "身份证地址")
    private String idCardAddr;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "是否退休")
    private Boolean isRetired;

    @ApiModelProperty(value = "家庭区域")
    private List homeRegion;

    @ApiModelProperty(value = "家庭详细地址")
    private String homeAddr;

    @ApiModelProperty(value = "工作岗位")
    private List position;

    @ApiModelProperty(value = "参加工作时间")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date firstWorkDate;

    //2、单位信息
    @ApiModelProperty(value = "单位id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long organizationId;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "单位区域")
    private List organizationRegion;

    @ApiModelProperty(value = "单位地址")
    private String organizationAddr;

    @ApiModelProperty("单位地址经度")
    private Double organizationLongitude;

    @ApiModelProperty("单位地址纬度")
    private Double organizationLatitude;

    @ApiModelProperty(value = "单位邮编")
    private String zipCode;

    @ApiModelProperty(value = "单位法人")
    private String legalPerson;

    @ApiModelProperty(value = "法人电话")
    private String legalPersonPhone;

    //3.事故信息
    //是否职业病相关
    @ApiModelProperty(value = "是否职业病相关")
    private Boolean isOccupDiseaseRelated;

    @ApiModelProperty(value = "职业病名称")
    private String occupDiseaseName;

    @ApiModelProperty(value = "接触职业病危害岗位")
    private String occupDiseasePosition;

    @ApiModelProperty(value = "接触职业病危害时间")
    private String occupDiseaseDate;

    //事故信息
    @ApiModelProperty(value = "伤害部位")
    private List injuredPart;

    @ApiModelProperty(value = "事故时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentTime;

    @ApiModelProperty(value = "事故区域")
    private List accidentRegion;

    @ApiModelProperty(value = "事故地点")
    private String accidentAddress;

    @ApiModelProperty(value = "事故地点经度")
    private Double longitude;

    @ApiModelProperty(value = "事故地点纬度")
    private Double latitude;

    @ApiModelProperty(value = "事故经过")
    private String accidentDetail;

    @ApiModelProperty(value = "事故原因")
    private String accidentCause;

    @ApiModelProperty(value = "事发时从事的工作")
    private String workingOfAccident;

    @ApiModelProperty(value = "补充说明")
    private String supplementary;

    //事故相关材料
    @ApiModelProperty(value = "现场照片")
    private List pictures;

    @ApiModelProperty(value = "现场视频")
    private List videos;

    @ApiModelProperty(value = "受伤害部位图片")
    private List injuredPartPics;

    //诊断信息
    @ApiModelProperty(value = "就诊医院id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long hospitalId;

    @ApiModelProperty(value = "就诊医院")
    private String hospital;

    @ApiModelProperty(value = "初诊日期")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date firstClinicDate;

    @ApiModelProperty(value = "诊断结果")
    private List diagnoses;

    @ApiModelProperty(value = "住院科室")
    private String department;

    @ApiModelProperty(value = "床位号")
    private String bedNumber;

    @ApiModelProperty(value = "事故者状态")
    private String injuredStatus;

    //4.申报材料
    @ApiModelProperty(value = "工伤(亡)认定申请表")
    private List injureDeclaration;

    @ApiModelProperty(value = "受伤职工有效身份证明")
    private List idCardPics;

    @ApiModelProperty(value = "与用人单位存在劳动关系的证明材料")
    private List laborRelation;

    @ApiModelProperty(value = "用人单位事故调查报告书")
    private List orgSurveyReport;

    @ApiModelProperty(value = "超退休年龄未领取养老金证明")
    private List unclaimedPensionProof;

    @ApiModelProperty(value = "有无证人")
    private Boolean hasWitness;

    @ApiModelProperty(value = "无证人申明")
    private List noWitnessDeclare;

    @ApiModelProperty(value = "证人证言1")
    private List witnessTestimony1;

    @ApiModelProperty(value = "证人证言2")
    private List witnessTestimony2;

    @ApiModelProperty(value = "证人证言3")
    private List witnessTestimony3;

    @ApiModelProperty(value = "证人证言4")
    private List witnessTestimony4;

    @ApiModelProperty(value = "医疗就诊材料")
    private List medicalDiagnose;

    @ApiModelProperty(value = "职业病诊断证明或鉴定书")
    private List occupDiseaseDiagnose;

    @ApiModelProperty(value = "授权委托书")
    private List authorizationLetter;

    @ApiModelProperty(value = "经办人身份证明")
    private List applicantCert;

    @ApiModelProperty(value = "用人单位的营业证明")
    private List businessCertificate;

    @ApiModelProperty(value = "工会介绍信")
    private List unionReference;

    @ApiModelProperty(value = "医学死亡证明或火化证明")
    private List deathCremationCert;

    @ApiModelProperty(value = "特殊认定情况")
    private String specialCondition;

    @ApiModelProperty(value = "特殊认定")
    private SpecialIdentify specialIdentify;

    @ApiModelProperty(value = "实物提交方式")
    private String materialSubmitWay;

    @ApiModelProperty(value = "实物提交运单号")
    private List materialSubmitMailSn;

    @ApiModelProperty(value = "通知领取方式")
    private String informReceiveWay;

	@ApiModelProperty("送达确认书")
	private List deliveryConfirmationFile;

    @ApiModelProperty(value = "通知收件方式")
    private UserAddressOut informReceiveAddr;

    @ApiModelProperty(value = "受理通知书签名")
    private String acceptInformSignature;

    @ApiModelProperty(value = "通知确认收到签字")
    private String informReceiveSignature;

    @ApiModelProperty(value = "通知领取运单号")
    private List informReceiveMailSn;

    @ApiModelProperty(value = "认定工伤决定书")
    private String injureIdentification;

    @ApiModelProperty(value = "不予认定工伤决定书")
    private String injureNotIdentify;

    @ApiModelProperty(value = "伤害类型")
    private String injureType;

    @ApiModelProperty(value = "伤害表现")
    private String injureExpression;

    @ApiModelProperty(value = "伤残等级")
    private String injureLevel;

    @ApiModelProperty(value = "撤销申请书")
    private List cancelApplication;

    @ApiModelProperty(value = "撤销申请时间")
    private Date cancelApplyTime;

    @ApiModelProperty(value = "撤销成功时间")
    private Date canceledTime;

    @ApiModelProperty(value = "是否已经发起调查")
    private Boolean hasIssuedInvestigate;

    @ApiModelProperty(value = "是否材料递交方式开放")
    private Boolean isSubmitWayOpen;

    //各种结论
    @ApiModelProperty(value = "受理结论")
    private AcceptConclusion acceptConclusion;

    @ApiModelProperty(value = "认定结论")
    private IdentifyConclusion identifyConclusion;

    @ApiModelProperty(value = "退回结论")
    private RejectConclusion rejectConclusion;

    @ApiModelProperty(value = "认定中止结论")
    private SuspendedConclusion suspendedConclusion;

    @ApiModelProperty(value = "撤销结论")
    private CancelConclusion cancelConclusion;

    @ApiModelProperty("认定中止材料")
    private List suspendFileList;

    @ApiModelProperty("申请工伤认定提交材料目录 文件名称")
    private String materialCatalogUrl;

    @ApiModelProperty("工伤认定材料清单 文件名称")
    private String materialListUrl;

    @ApiModelProperty("工伤认定申请表 文件名称")
    private String applyUrl;

	@ApiModelProperty("诊断信息")
	private List<DiagnosticInfo> diagnosticInfoList;

	@ApiModelProperty("单位注册住所-单位区域")
	private List organizationRegionV2;

	@ApiModelProperty("单位注册住所-单位地址")
	private String organizationAddrV2;

	@ApiModelProperty("单位注册住所-单位地址经度")
	private Double organizationLongitudeV2;

	@ApiModelProperty("单位注册住所-单位地址纬度")
	private Double organizationLatitudeV2;

	@ApiModelProperty("工伤认定申请材料")
	private List declarationMaterials;

    @ApiModelProperty("伤者授权委托书")
    private List authorizationLetterV2;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherMaterials;


	@ApiModelProperty("事故责任认定书")
	private List acciRespIdentify;

	@ApiModelProperty("路线图")
	private List routeMap;

	@ApiModelProperty("居住证明")
	private List residenceProof;

//	@ApiModelProperty("医学死亡证明或火化证明")
//	private List deathCremationCert;

	@ApiModelProperty("法院判决书，公安机关证明或其他证明")
	private List courtPoliceCert;

	@ApiModelProperty("民政部门或其他部门的证明")
	private List civilAffairsCert;

	@ApiModelProperty("伤残军人证")
	private List disabledMilitaryCert;

	@ApiModelProperty("旧伤复发鉴定证明")
	private List recurrenceCert;

	@ApiModelProperty("公安机关证明或者其它有效证明")
	private List publicSecurityCert;

	@ApiModelProperty("因工外出证明")
	private List workTravelCert;

	@ApiModelProperty("人民法院宣告死亡的结论")
	private List courtDeathCert;

//	@ApiModelProperty("因工外出证明")
//	private List workTravelCert;

	@ApiModelProperty("其他补充说明")
	private String otherSupplementary;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherCert;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = DateTimeJsonSerializer.class)
    @ApiModelProperty("案件申请时间")
    private Date submitTime;

}
