package com.yixun.wid.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SpecialIdentify {

	@ApiModelProperty("道路交通事故")
	public TrafficAccident trafficAccident;

	@Data
	@ApiModel("道路交通事故")
	static class TrafficAccident {

		@ApiModelProperty("事故责任认定书")
		private List acciRespIdentify;

		@ApiModelProperty("路线图")
		private List routeMap;

		@ApiModelProperty("居住证明")
		private List residenceProof;

	}

	@ApiModelProperty("因公死亡")
	public DeathInDuty deathInDuty;

	@Data
	@ApiModel("因公死亡")
	static class DeathInDuty {

		@ApiModelProperty("医学死亡证明或火化证明")
		private List deathCremationCert;

	}

	@ApiModelProperty("暴力伤害")
	public ViolentInjury violentInjury;

	@Data
	@ApiModel("暴力伤害")
	static class ViolentInjury {
		@ApiModelProperty("法院判决书，公安机关证明或其他证明")
		private List courtPoliceCert;
	}

	@ApiModelProperty("抢险救灾")
	public Rescue rescue;

	@Data
	@ApiModel("抢险救灾")
	static class Rescue {
		@ApiModelProperty("民政部门或其他部门的证明")
		private List civilAffairsCert;
	}

	@ApiModelProperty("军人旧伤复发")
	public SoldierOldWounds soldierOldWounds;

	@Data
	@ApiModel("军人旧伤复发")
	static class SoldierOldWounds {
		@ApiModelProperty("伤残军人证")
		private List disabledMilitaryCert;

		@ApiModelProperty("旧伤复发鉴定证明")
		private List recurrenceCert;
	}

	@ApiModelProperty("因工外出受伤")
	public BusinessTripInjured businessTripInjured;

	@Data
	@ApiModel("因工外出受伤")
	static class BusinessTripInjured {
		@ApiModelProperty("公安机关证明或者其它有效证明")
		private List publicSecurityCert;

		@ApiModelProperty("因工外出证明")
		private List workTravelCert;
	}

	@ApiModelProperty("因工外出失踪")
	public BusinessTripMissing businessTripMissing;

	@Data
	@ApiModel("因工外出失踪")
	static class BusinessTripMissing {
		@ApiModelProperty("人民法院宣告死亡的结论")
		private List courtDeathCert;

		@ApiModelProperty("因工外出证明")
		private List workTravelCert;
	}

	@ApiModelProperty("其他")
	public Other other;

	@Data
	@ApiModel("其他")
	static class Other {
		@ApiModelProperty("补充说明")
		private String supplementary;

		@ApiModelProperty("证明工伤事件的其他材料")
		private List otherCert;
	}
}
