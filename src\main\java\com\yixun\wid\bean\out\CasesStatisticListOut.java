package com.yixun.wid.bean.out;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CasesStatisticListOut {

    @ApiModelProperty(value = "单位id")
    private Long organizationId;

    @ApiModelProperty(value = "单位名称")
    private String organization;

    @ApiModelProperty(value = "是否已认证")
    private Boolean isAuthenticated;

    @ApiModelProperty(value = "已报备数量")
    private Integer submittedCount;

    @ApiModelProperty(value = "无需申报数量")
    private Integer noNeedApplyCount;

    @ApiModelProperty(value = "已申报数量")
    private Integer appliedCount;

}
