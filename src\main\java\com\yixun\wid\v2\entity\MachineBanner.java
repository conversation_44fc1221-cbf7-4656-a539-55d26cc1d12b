package com.yixun.wid.v2.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.validation.constraints.NotNull;

@Data
public class MachineBanner {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 名称
	 */
	@NotNull(message = "名称不能为空")
	private String name;

	/**
	 * 图片地址
	 */
	@NotNull(message = "图片地址不能为空")
	private String imageUrl;

	/**
	 * 链接地址
	 */
	private String linkUrl;

	/**
	 * 是否启用
	 */
	@NotNull(message  = "启用状态不能为空")
	private Boolean enabled;

}
