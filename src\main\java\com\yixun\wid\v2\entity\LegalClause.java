package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 法规条款实体类
 */
@Data
public class LegalClause {

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 法规依据ID（关联LegalRegulation）
     */
    private Long regulationId;

    /**
     * 法规名称（冗余字段，用于显示）
     */
    private String regulationName;

    /**
     * 条款序号
     */
    private Integer clauseNumber;

    /**
     * 条款主要内容
     */
    private String clauseContent;

    /**
     * 条款分项序号
     */
    private Integer subClauseNumber;

    /**
     * 分项内容
     */
    private String subClauseContent;

    /**
     * 条款状态（0：未启用，1：已启用）
     */
    private Integer status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
}
