package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.entity.CorpInvestigate;

import java.util.List;

public interface CorpInvestigateService {

    void save(CorpInvestigate corpInvestigate);

    CorpInvestigate getById(Long corpInvestigateId);

    void update(CorpInvestigate corpInvestigate);

    List<CorpInvestigate> getList(Long userId, String companyName, CommonPage commonPage);
}
