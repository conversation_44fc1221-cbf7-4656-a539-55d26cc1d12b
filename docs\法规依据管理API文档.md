# 法规依据管理API文档

## 概述

基于需求文档4.4法规依据配置章节，实现了法规依据和法规条款的完整管理功能，包括CRUD操作、搜索、关联查询等。

## 功能模块

### 1. 法规依据管理 (LegalRegulationController)

#### 1.1 新增法规依据
- **接口**: `POST /v2/legal/regulation/save`
- **功能**: 新增法规依据
- **参数**: LegalRegulation对象

#### 1.2 查询法规依据列表（分页）
- **接口**: `GET /v2/legal/regulation/list`
- **功能**: 分页查询法规依据列表
- **参数**: 
  - status: 状态
  - deleted: 是否删除
  - search: 搜索关键词
  - 分页参数

#### 1.3 查询法规依据列表（不分页）
- **接口**: `GET /v2/legal/regulation/listAll`
- **功能**: 查询所有法规依据
- **参数**: status, deleted, search

#### 1.4 根据ID查询法规依据
- **接口**: `GET /v2/legal/regulation`
- **功能**: 根据ID查询单个法规依据
- **参数**: id

#### 1.5 更新法规依据
- **接口**: `POST /v2/legal/regulation/update`
- **功能**: 更新法规依据信息
- **参数**: LegalRegulation对象

#### 1.6 批量删除法规依据
- **接口**: `POST /v2/legal/regulation/batchDelete`
- **功能**: 批量逻辑删除法规依据
- **参数**: ID列表

#### 1.7 更新排序
- **接口**: `POST /v2/legal/regulation/update/sort`
- **功能**: 更新法规依据排序
- **参数**: SortVO列表

#### 1.8 关键词搜索（用于关联搜索）
- **接口**: `GET /v2/legal/regulation/search`
- **功能**: 根据关键词搜索法规依据，支持联想功能
- **参数**: 
  - keyword: 搜索关键词
  - limit: 返回数量限制（默认10）

### 2. 法规条款管理 (LegalClauseController)

#### 2.1 新增法规条款
- **接口**: `POST /v2/legal/clause/save`
- **功能**: 新增法规条款
- **参数**: LegalClause对象

#### 2.2 查询法规条款列表（分页）
- **接口**: `GET /v2/legal/clause/list`
- **功能**: 分页查询法规条款列表
- **参数**: 
  - status: 状态
  - deleted: 是否删除
  - search: 搜索关键词
  - regulationId: 法规依据ID
  - clauseType: 条款类型
  - 分页参数

#### 2.3 根据法规依据ID查询条款列表
- **接口**: `GET /v2/legal/clause/listByRegulation`
- **功能**: 根据法规依据ID查询所有条款
- **参数**: 
  - regulationId: 法规依据ID
  - status: 状态
  - clauseType: 条款类型

#### 2.4 根据ID查询法规条款
- **接口**: `GET /v2/legal/clause`
- **功能**: 根据ID查询单个法规条款
- **参数**: id

#### 2.5 更新法规条款
- **接口**: `POST /v2/legal/clause/update`
- **功能**: 更新法规条款信息
- **参数**: LegalClause对象

#### 2.6 批量删除法规条款
- **接口**: `POST /v2/legal/clause/batchDelete`
- **功能**: 批量逻辑删除法规条款
- **参数**: ID列表

#### 2.7 更新排序
- **接口**: `POST /v2/legal/clause/update/sort`
- **功能**: 更新法规条款排序
- **参数**: SortVO列表

#### 2.8 根据条款序号查询
- **接口**: `GET /v2/legal/clause/listByClauseNumber`
- **功能**: 根据法规依据ID和条款序号查询条款列表
- **参数**: 
  - regulationId: 法规依据ID
  - clauseNumber: 条款序号

#### 2.9 获取条款序号列表
- **接口**: `GET /v2/legal/clause/clauseNumbers`
- **功能**: 获取指定法规依据下的所有条款序号
- **参数**: regulationId: 法规依据ID

## 数据模型

### LegalRegulation（法规依据）
- id: 主键ID
- regulationName: 法规名称
- regulationDesc: 法规描述
- regulationType: 法规类型
- publishOrgan: 发布机关
- publishDate: 发布日期
- effectiveDate: 生效日期
- status: 状态（0：未启用，1：已启用）
- sort: 排序
- createTime: 创建时间
- updateTime: 更新时间
- deleted: 是否删除（0：未删除，1：已删除）

### LegalClause（法规条款）
- id: 主键ID
- regulationId: 法规依据ID
- regulationName: 法规名称（冗余字段）
- clauseNumber: 条款序号
- clauseContent: 条款主要内容
- subClauseNumber: 条款分项序号
- subClauseContent: 分项内容
- clauseType: 条款类型
- applicableScene: 适用场景
- status: 状态（0：未启用，1：已启用）
- sort: 排序
- createTime: 创建时间
- updateTime: 更新时间
- deleted: 是否删除（0：未删除，1：已删除）

## 业务规则

1. **父子关系**: 法规条款通过regulationId关联法规依据
2. **条款序号**: 同一法规下条款序号可重复，用于支持同一条款的不同分项
3. **分项序号**: 在条款序号、条款主要内容相同时，分项序号不可重复
4. **自动填充**: 新增/更新条款时自动填充法规名称
5. **级联查询**: 查询条款时自动关联法规依据信息
6. **搜索功能**: 支持法规名称、内容、类型等多字段模糊搜索
7. **排序规则**: 条款按条款序号、分项序号升序排列

## 使用场景

1. **法规依据配置**: 管理员维护法规依据库
2. **条款内容管理**: 维护具体的法规条款内容
3. **关联搜索**: 在业务流程中通过关键词搜索法规依据
4. **条款选择**: 根据法规依据选择具体的条款序号和内容
5. **AI辅助审核**: 为AI提供法规依据数据支持
