package com.yixun.wid.v2.entity;

import org.springframework.data.annotation.Transient;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 理算结果
 */
@Data
public class ClaimsResult {
    /**
     * 申报总金额（分，数据库存储，自动计算=发票总额+住院伙食补助金额）
     * 通用汇总计算字段，各理算条理算的发票总额+住院伙食补助金额汇总合计
     */
    private Integer totalClaimAmountInCent;
    /**
     * 申报总金额（元，接口交互）
     */
    @Transient
    private BigDecimal totalClaimAmount;

    /**
     * 不可报销金额（分，数据库存储）
     * 通用汇总计算字段，各理算条理算的不可报销金额的汇总合计
     */
    private Integer nonReimbursableAmountInCent;
    /**
     * 不可报销金额（元，接口交互）
     */
    @Transient
    private BigDecimal nonReimbursableAmount;

    /**
     * 应付总金额（分，数据库存储）
     * 通用汇总计算字段，各理算条理算的应付总金额的汇总合计
     */
    private Integer totalPayableAmountInCent;
    /**
     * 应付总金额（元，接口交互）
     */
    @Transient
    private BigDecimal totalPayableAmount;

    /**
     * 第三方赔付金额（分，数据库存储，输入数字）
     */
    private Integer thirdPartyPayAmountInCent;
    /**
     * 第三方赔付金额（元，接口交互，输入数字）
     */
    @Transient
    private BigDecimal thirdPartyPayAmount;
    
    /**
     * 报销比例（百分比，如0.7表示70%）
     */
    private BigDecimal reimbursementRatio;
    
    /**
     * 第三方实际赔付金额（分，数据库存储，自动生成=第三方赔付金额*报销比例）
     */
    private Integer thirdPartyActualPayAmountInCent;
    /**
     * 第三方实际赔付金额（元，接口交互，自动生成=第三方赔付金额*报销比例）
     */
    @Transient
    private BigDecimal thirdPartyActualPayAmount;
    
    /**
     * 实际支付金额（分，数据库存储，自动生成=申报总金额-不可报销金额-第三方实际赔付金额）
     */
    private Integer actualPayAmountInCent;
    /**
     * 实际支付金额（元，接口交互，自动生成=申报总金额-不可报销金额-第三方实际赔付金额）
     */
    @Transient
    private BigDecimal actualPayAmount;
    
    /**
     * 备注（字符串，输入）
     */
    private String remarks;

    // 元分转换方法
    public BigDecimal getTotalClaimAmount() {
        if (totalClaimAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalClaimAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalClaimAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalClaimAmountInCent = null;
        } else {
            this.totalClaimAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setNonReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.nonReimbursableAmountInCent = null;
        } else {
            this.nonReimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getTotalPayableAmount() {
        if (totalPayableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalPayableAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setTotalPayableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalPayableAmountInCent = null;
        } else {
            this.totalPayableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getThirdPartyPayAmount() {
        if (thirdPartyPayAmountInCent == null) {
            return null;
        }
        return new BigDecimal(thirdPartyPayAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setThirdPartyPayAmount(BigDecimal amount) {
        if (amount == null) {
            this.thirdPartyPayAmountInCent = null;
        } else {
            this.thirdPartyPayAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getThirdPartyActualPayAmount() {
        if (thirdPartyActualPayAmountInCent == null) {
            return null;
        }
        return new BigDecimal(thirdPartyActualPayAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setThirdPartyActualPayAmount(BigDecimal amount) {
        if (amount == null) {
            this.thirdPartyActualPayAmountInCent = null;
        } else {
            this.thirdPartyActualPayAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getActualPayAmount() {
        if (actualPayAmountInCent == null) {
            return null;
        }
        return new BigDecimal(actualPayAmountInCent).divide(new BigDecimal(100));
    }
    
    public void setActualPayAmount(BigDecimal amount) {
        if (amount == null) {
            this.actualPayAmountInCent = null;
        } else {
            this.actualPayAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
} 