package com.yixun.wid.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.entity.em.SurveyUserRoleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 调查任务人员表
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2024-08-22
 */
@Getter
@Setter
@TableName(value = "survey_user", autoResultMap = true)
@ApiModel(value = "SurveyUser对象", description = "调查任务人员表")
public class SurveyUser {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("调查id")
    private Long surveyId;

    @ApiModelProperty("查勘员id")
    private Long userId;

    @ApiModelProperty("查勘员姓名")
    private String userName;

    @ApiModelProperty("查勘员角色")
    private SurveyUserRoleEnum userRole;

    @ApiModelProperty("签到记录")
    private List<CheckIn> checkIns;

    @ApiModelProperty("签名确认链接")
    private String signUrl;

    @ApiModelProperty("签名确认时间")
    private LocalDateTime signTime;

    @Data
    public static class CheckIn {

        @ApiModelProperty("签名地点")
        private String address;

        @ApiModelProperty("签名时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime signTime;
    }

}
