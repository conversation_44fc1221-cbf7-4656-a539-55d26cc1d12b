package com.yixun.wid.controller.common;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.out.OccupationDictOut;
import com.yixun.wid.bean.out.RegionValueOut;
import com.yixun.wid.entity.*;
import com.yixun.wid.entity.em.DictType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AreaDictService;
import com.yixun.wid.service.DataDictService;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.TreeListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "常用字典信息")
@RestController
@RequestMapping(value = "/dict")
public class DictController {

    @Resource
    private AreaDictService areaDictService;

    @Resource
    private DataDictService dataDictService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @GetMapping("/get-region-list")
    @ApiOperation(value = "获取地区列表")
    public CommonResult<List<AreaDict>> getRegionList(@RequestParam Long pCode) {

        if (pCode == null) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        List<AreaDict> regionList = areaDictService.getAreaList(pCode);

        return CommonResult.successData(regionList);
    }

    @GetMapping("/get-region-all-list")
    @ApiOperation(value = "获取所有地区列表")
    public CommonResult<List<AreaDictMap>> getAllRegionList() {

        List<AreaDictMap> mapList = redisTemplate.opsForList().range("AllAreaDict", 0, -1);
        if (mapList == null || mapList.isEmpty()) {
            List<AreaDict> regionList = areaDictService.getAllAreaList();
            List<AreaDictMap> areaDictMapList = BeanUtils.copyToOutList(regionList, AreaDictMap.class);
            mapList = TreeListUtil.areaDictTreeList(areaDictMapList, 0L);
            redisTemplate.opsForList().rightPushAll("AllAreaDict", mapList);
        }

        return CommonResult.successData(mapList);
    }

    @GetMapping("/get-dict-list")
    @ApiOperation(value = "获取各种数据字典列表")
    public CommonResult<List<DataDict>> getDataDictList(@RequestParam DictType datatype) {

        if (datatype == null) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        List<DataDict> dataDictList = dataDictService.getDataDictList(datatype.name());

        return CommonResult.successData(dataDictList);
    }

    @GetMapping("/get-occupation-dict-list")
    @ApiOperation(value = "获取职业字典列表")
    public CommonResult<List<OccupationDict>> getDataDictList(@RequestParam String parentcode) {

        if (parentcode == null) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        List<OccupationDict> dataDictList = dataDictService.getOccupationDictList(parentcode, "occupationType");

        return CommonResult.successData(dataDictList);
    }

    @GetMapping("/get-all-occupation-dict-list")
    @ApiOperation(value = "获取职业字典列表")
    public CommonResult<List<DataDictMap>> getAllOccupationDictList() {

        List<DataDictMap> mapList = redisTemplate.opsForList().range("OccupationDict", 0, -1);
        if (mapList == null || mapList.isEmpty()) {
            List<OccupationDict> dataDictList = dataDictService.getAllDataDictList("occupationType", null);
            List<DataDictMap> dataDictMapList = BeanUtils.copyToOutList(dataDictList, DataDictMap.class);
            mapList = TreeListUtil.dataDictTreeList(dataDictMapList, "0");
            redisTemplate.opsForList().rightPushAll("OccupationDict", mapList);
        }

        return CommonResult.successData(mapList);
    }

    @GetMapping("/getRegionByAdCode")
    @ApiOperation(value = "用地图code获取地区信息")
    public CommonResult<List<RegionValueOut>> getRegionByAdCode(@RequestParam String adCode) {

        if (adCode == null) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        String code = adCode + "000000";

        AreaDict region3 = areaDictService.getByCode(Long.valueOf(code));
        if (region3 == null) {
            throw new DataErrorException("区域信息错误--3");
        }
        AreaDict region2 = areaDictService.getByCode(region3.getPcode());
        if (region2 == null) {
            throw new DataErrorException("区域信息错误--2");
        }
        AreaDict region1 = areaDictService.getByCode(region2.getPcode());
        if (region1 == null) {
            throw new DataErrorException("区域信息错误--1");
        }

        List<RegionValueOut> regionValueOutList = new ArrayList<>();
        RegionValueOut r1 = new RegionValueOut();
        r1.setText(region1.getName());
        r1.setValue(region1.getCode().toString());
        RegionValueOut r2 = new RegionValueOut();
        r2.setText(region2.getName());
        r2.setValue(region2.getCode().toString());
        RegionValueOut r3 = new RegionValueOut();
        r3.setText(region3.getName());
        r3.setValue(region3.getCode().toString());

        regionValueOutList.add(r1);
        regionValueOutList.add(r2);
        regionValueOutList.add(r3);

        return CommonResult.successData(regionValueOutList);
    }

    @GetMapping("/search-occupation-dict-list")
    @ApiOperation(value = "搜索职业字典列表（第三级）")
    public CommonResult<List<OccupationDictOut>> searchOccupationDictList(String key) {

        List<OccupationDict> dataDictL3 = dataDictService.getAllDataDictList("occupationType", 3);
        List<OccupationDict> dataDictL2 = dataDictService.getAllDataDictList("occupationType", 2);
        List<OccupationDict> dataDictL1 = dataDictService.getAllDataDictList("occupationType", 1);
        List<OccupationDict> resultList = dataDictL3.stream().filter(o -> o.getLabel().contains(key)).collect(Collectors.toList());
        List<OccupationDictOut> outList = new ArrayList<>();
        try {
            for (OccupationDict occupationDict : resultList) {
                OccupationDict oL2 = dataDictL2.stream().filter(l2 -> occupationDict.getParentcode().equals(l2.getNodecode())).findFirst().get();
                OccupationDict oL1 = dataDictL1.stream().filter(l1 -> oL2.getParentcode().equals(l1.getNodecode())).findFirst().get();
                OccupationDictOut out = new OccupationDictOut();
                String label = oL1.getLabel() + "-" + oL2.getLabel() + "-" + occupationDict.getLabel();
                String code = oL1.getNodecode() + "-" + oL2.getNodecode() + "-" + occupationDict.getNodecode();
                out.setLabel(label);
                out.setCode(code);
                outList.add(out);
            }
        } catch (Exception e) {
            throw new DataErrorException("字典错误");
        }

        return CommonResult.successData(outList);
    }

}
