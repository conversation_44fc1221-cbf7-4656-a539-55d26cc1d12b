package com.yixun.wid.utils;

import com.yixun.wid.entity.em.CaptchaType;
import com.yixun.wid.exception.DataErrorException;
import org.springframework.data.redis.core.StringRedisTemplate;

public class VerifyCodeCheckUtil {

    public static  void checkVerifyCode(StringRedisTemplate stringRedisTemplate, String phone, String verifyCode, CaptchaType captchaType) {
        String code = stringRedisTemplate.opsForValue().get("CAPTCHA:" + captchaType.name() + "_" + phone);
        if (code==null){
            throw new DataErrorException("未获取验证码或验证码已过期");
        }
        if (!code.equals(verifyCode)){
            throw new DataErrorException("验证码错误");
        }
        stringRedisTemplate.delete("CAPTCHA:" + captchaType.name() + "_" + phone);
    }
}
