package com.yixun.wid.interceptor;

import cn.hutool.json.JSONUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class TokenRequestInterceptor implements RequestInterceptor {

    @Value("${message.appId}")
    private String msgAppId;

    private final static String apiTokenKey = "AppToken";

    private String appToken;

    @PostConstruct
    public void init() {
        Map<String, String> map = new HashMap<>(3);
        map.put("appId", msgAppId);
        map.put("appSecretEncrypt", "ed236bc63fa110be59cec159974c4530");
        String jsonString = JSONUtil.toJsonStr(map);
        appToken = Base64.getEncoder().encodeToString(jsonString.getBytes());
    }

    @Override
    public void apply(RequestTemplate template) {
        template.header(apiTokenKey, appToken);
    }

}
