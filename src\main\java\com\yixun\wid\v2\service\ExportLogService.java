package com.yixun.wid.v2.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.ExportLog;
import com.yixun.wid.v2.enums.UserType;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.ApplyExportRequestVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出日志服务
 */
@Slf4j
@Service
@AllArgsConstructor
public class ExportLogService {

    private final MongoTemplate mongoTemplate;
    private final UserService userService;
    private final AdministratorService administratorService;

    /**
     * 记录申请记录导出日志
     */
    public void recordApplyExportLog(ApplyExportRequestVO requestVO, String fileName,
                                   Integer dataCount, String status, String errorMessage) {
        try {
            ExportLog exportLog = new ExportLog();
            
            // 基本信息
            exportLog.setId(SnGeneratorUtil.getId());
            exportLog.setExportTime(new Date());
            exportLog.setExportModule("导出申请记录");
            exportLog.setFileName(fileName);
            exportLog.setDataCount(dataCount);
            exportLog.setStatus(status);
            exportLog.setErrorMessage(errorMessage);
            
            // 导出人信息
            try {
                Long currentUserId = ServletRequestUtils.getUserId();
                UserType userType = ServletRequestUtils.getUserType();

                exportLog.setExportUserId(currentUserId);
                exportLog.setExportUserType(userType.getMsg());

                if (UserType.ADMIN.equals(userType)) {
                    Administrator administrator = administratorService.getAdministratorById(currentUserId);
                    if (administrator != null) {
                        exportLog.setExportUserName(administrator.getRealName());
                    }
                } else if (UserType.USER.equals(userType)) {
                    User user = userService.getUserById(currentUserId);
                    if (user != null) {
                        exportLog.setExportUserName(user.getRealName());
                    }
                }
            } catch (Exception e) {
                log.warn("获取导出人信息失败", e);
            }
            
            // 导出条件
            exportLog.setOrganizationId(requestVO.getOrganizationId());
            exportLog.setBusinessType(requestVO.getBusinessType());
            exportLog.setTimeRange(requestVO.getTimeRange());

            // 获取单位名称
            String organizationName = getOrganizationNameById(requestVO.getOrganizationId());
            exportLog.setOrganizationName(organizationName);
            
            // 构建导出条件描述
            StringBuilder conditions = new StringBuilder();
            conditions.append("单位ID: ").append(requestVO.getOrganizationId());
            conditions.append(", 业务类型: ").append(requestVO.getBusinessType());
            conditions.append(", 时间范围: 近").append(requestVO.getTimeRange()).append("个月");
            exportLog.setExportConditions(conditions.toString());
            
            // 请求信息
            HttpServletRequest request = ServletRequestUtils.getReq();
            if (request != null) {
                exportLog.setIpAddress(getClientIpAddress(request));
                exportLog.setUserAgent(request.getHeader("User-Agent"));
            }
            
            // 扩展信息
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("requestParams", requestVO);
            extraInfo.put("exportDate", new Date());
            exportLog.setExtraInfo(extraInfo);
            
            // 时间戳
            Date now = new Date();
            exportLog.setCreateTime(now);
            exportLog.setUpdateTime(now);
            
            // 保存到数据库
            mongoTemplate.save(exportLog);
            
            log.info("导出日志记录成功，导出模块: {}, 文件名: {}, 数据量: {}, 状态: {}", 
                exportLog.getExportModule(), fileName, dataCount, status);
                
        } catch (Exception e) {
            log.error("记录导出日志失败", e);
        }
    }

    /**
     * 通用导出日志记录方法
     */
    public void recordExportLog(String exportModule, String fileName, Integer dataCount, 
                              String exportConditions, String status, String errorMessage,
                              Map<String, Object> extraInfo) {
        try {
            ExportLog exportLog = new ExportLog();
            
            // 基本信息
            exportLog.setId(SnGeneratorUtil.getId());
            exportLog.setExportTime(new Date());
            exportLog.setExportModule(exportModule);
            exportLog.setFileName(fileName);
            exportLog.setDataCount(dataCount);
            exportLog.setExportConditions(exportConditions);
            exportLog.setStatus(status);
            exportLog.setErrorMessage(errorMessage);
            
            // 导出人信息
            try {
                Long currentUserId = ServletRequestUtils.getUserId();
                UserType userType = ServletRequestUtils.getUserType();

                exportLog.setExportUserId(currentUserId);
                exportLog.setExportUserType(userType.getMsg());

                if (UserType.ADMIN.equals(userType)) {
                    Administrator administrator = administratorService.getAdministratorById(currentUserId);
                    if (administrator != null) {
                        exportLog.setExportUserName(administrator.getRealName());
                    }
                } else if (UserType.USER.equals(userType)) {
                    User user = userService.getUserById(currentUserId);
                    if (user != null) {
                        exportLog.setExportUserName(user.getRealName());
                    }
                }
            } catch (Exception e) {
                log.warn("获取导出人信息失败", e);
            }
            
            // 请求信息
            HttpServletRequest request = ServletRequestUtils.getReq();
            if (request != null) {
                exportLog.setIpAddress(getClientIpAddress(request));
                exportLog.setUserAgent(request.getHeader("User-Agent"));
            }
            
            // 扩展信息
            if (extraInfo != null) {
                exportLog.setExtraInfo(extraInfo);
            }
            
            // 时间戳
            Date now = new Date();
            exportLog.setCreateTime(now);
            exportLog.setUpdateTime(now);
            
            // 保存到数据库
            mongoTemplate.save(exportLog);
            
            log.info("导出日志记录成功，导出模块: {}, 文件名: {}, 数据量: {}, 状态: {}", 
                exportModule, fileName, dataCount, status);
                
        } catch (Exception e) {
            log.error("记录导出日志失败", e);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (StrUtil.isNotBlank(proxyClientIp) && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }
        
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (StrUtil.isNotBlank(wlProxyClientIp) && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 根据单位ID获取单位名称
     */
    private String getOrganizationNameById(Long organizationId) {
        if (organizationId == null) {
            return "未知单位";
        }

        try {
            // 先尝试从Declaration表查询
            Query query = new Query();
            query.addCriteria(Criteria.where("organizationId").is(organizationId));
            query.limit(1);
            Declaration declaration = mongoTemplate.findOne(query, Declaration.class);

            if (declaration != null && StrUtil.isNotBlank(declaration.getOrganization())) {
                return declaration.getOrganization();
            }

            // 如果Declaration表没有，再尝试从Cases表查询
            Cases cases = mongoTemplate.findOne(query, Cases.class);
            if (cases != null && StrUtil.isNotBlank(cases.getOrganization())) {
                return cases.getOrganization();
            }

        } catch (Exception e) {
            log.warn("查询单位名称失败，organizationId: {}", organizationId, e);
        }

        return "未知单位";
    }
}
