package com.yixun.wid.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StaffGroup {

    private Long id;
    private Date createDate;
    private Date updateDate;

    @ApiModelProperty("是否删除")
    private Boolean isDel;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("显示顺序")
    private Integer showOrder;

    @ApiModelProperty("禁用标志")
    private Boolean isDisable;

    @ApiModelProperty("类型(1人社部门 2辅助调查)")
    private Integer type;

}
