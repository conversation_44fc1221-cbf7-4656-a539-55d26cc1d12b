package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.BizType;
import com.yixun.wid.v2.enums.GeneralStatusEnum;
import com.yixun.wid.v2.vo.SortVO;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 业务类型相关接口
 */
@RequestMapping("/v2/biz/type")
@RestController
@AllArgsConstructor
public class BizTypeController {

	private final MongoTemplate mongoTemplate;

	/**
	 * 新增业务类型
	 *
	 * @param bizType 业务类型
	 */
	@PostMapping("/save")
	public CommonResult<Void> save(@RequestBody BizType bizType) {
		bizType.setId(SnGeneratorUtil.getId());
		bizType.setDeleted(GeneralStatusEnum.OFF.getCode());
		bizType.setSort(SnGeneratorUtil.getId());
		if (ObjectUtil.isNull(bizType.getStatus())) {
			bizType.setStatus(GeneralStatusEnum.ON.getCode());
		}
		bizType.setCreateTime(new Date());
		bizType.setUpdateTime(new Date());
		mongoTemplate.save(bizType);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 查询业务类型列表
	 *
	 * @return 业务类型列表
	 */
	@GetMapping("/list")
	public CommonResult<List<BizType>> list(@RequestParam(required = false) Integer status, @RequestParam(defaultValue = "0", required = false) Integer deleted,
		@RequestParam(required = false) String search) {
		Query query =
			Query.query(Criteria.where("deleted").is(deleted))
				.with(Sort.by(Sort.Direction.DESC, "sort"))
				.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("bizTypeName").regex(".*" + search + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(status)) {
			query.addCriteria(Criteria.where("status").is(status));
		}
		List<BizType> bizTypes = mongoTemplate.find(query, BizType.class);
		return CommonResult.successData(bizTypes);
	}

	/**
	 * 查询业务类型列表 不鉴权
	 *
	 * @return 业务类型列表
	 */
	@GetMapping("/list2")
	public CommonResult<List<BizType>> list2(@RequestParam(required = false) Integer status, @RequestParam(defaultValue = "0", required = false) Integer deleted,
		@RequestParam(required = false) String search) {
		Query query =
			Query.query(Criteria.where("deleted").is(deleted))
				.with(Sort.by(Sort.Direction.DESC, "sort"))
				.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		if (StrUtil.isNotBlank(search)) {
			query.addCriteria(Criteria.where("bizTypeName").regex(".*" + search + ".*", "i"));
		}
//		if (ObjectUtil.isNotNull(status)) {
			query.addCriteria(Criteria.where("status").is(1));
//		}
		List<BizType> bizTypes = mongoTemplate.find(query, BizType.class);
		return CommonResult.successData(bizTypes);
	}

	/**
	 * 根据id查询
	 *
	 * @param id 主键
	 * @return 业务类型
	 */
	@GetMapping
	public CommonResult<BizType> getById(@RequestParam("id") Long id) {
		BizType byId = mongoTemplate.findById(id, BizType.class);
		if (ObjectUtil.isNull(byId) ||  GeneralStatusEnum.ON.getValue().equals(byId.getDeleted())) {
			throw new RuntimeException("业务类型不存在");
		}
		return CommonResult.successData(byId);
	}

	/**
	 * 更新业务类型
	 *
	 * @param bizType 业务类型
	 * @return 更新结果
	 */
	@PostMapping("/update")
	public CommonResult<Void> update(@RequestBody BizType bizType) {
		Long id = bizType.getId();
		if (ObjectUtil.isNull(id)) {
			throw new RuntimeException("id不能为空");
		}
		BizType byId = mongoTemplate.findById(id, BizType.class);
		if (ObjectUtil.isNull(byId)) {
			throw new RuntimeException("业务类型不存在");
		}
		BeanUtil.copyProperties(bizType, byId, CopyOptions.create().ignoreNullValue());
		byId.setUpdateTime(new Date());
		mongoTemplate.save(byId);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 更新业务类型列表排序
	 *
	 * @param sort 列表排序
	 * @return 更新结果
	 */
	@PostMapping("/update/sort")
	public CommonResult<Void> updateSort(@RequestBody List<SortVO> sort) {
		BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, BizType.class);
		for (SortVO sortVO : sort) {
			Update update = Update.update("id", sortVO.getId())
				.set("sort", sortVO.getSort())
				.set("updateTime", new Date())
				;
			operations.updateOne(Query.query(Criteria.where("id").is(sortVO.getId())), update);
		}
		operations.execute();
		return CommonResult.successResult("操作成功");
	}

}
