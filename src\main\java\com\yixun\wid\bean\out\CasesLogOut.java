package com.yixun.wid.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "案件操作日志输出对象")
@Data
public class CasesLogOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long casesId;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "姓名")
    private String name;
}
