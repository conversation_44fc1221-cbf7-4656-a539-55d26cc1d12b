package com.yixun.wid.v2.vo;

import com.yixun.wid.entity.em.LoginType;
import com.yixun.wid.entity.em.UserType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class IdCardLoginVO {

	@ApiModelProperty(value="登录类型")
	private LoginType loginType;

	@ApiModelProperty(value="机器码")
	private String clientId;

	@NotNull(message = "用户类型不能为空")
	@ApiModelProperty(value="用户类型")
	private UserType userType;

	/**
	 * rsa加密后的数据
	 * 原始数据格式 {"idCard":"xxx"}
	 */
	@NotEmpty(message = "加密数据不能为空")
	private String encryptedStr;

	@Data
	public static class DecryptedData {

		private String idCard;

	}

}
