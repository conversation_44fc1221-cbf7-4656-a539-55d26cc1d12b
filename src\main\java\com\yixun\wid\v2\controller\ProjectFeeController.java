package com.yixun.wid.v2.controller;

import cn.hutool.core.util.StrUtil;
import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.ProjectFeeBatchDeleteIn;
import com.yixun.wid.v2.bean.in.ProjectFeeIn;
import com.yixun.wid.v2.entity.ProjectFee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 待遇 项目费用管理相关接口
 */
@RestController
@RequestMapping("/v2/projectfee")
public class ProjectFeeController {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 分页查询项目费用列表
     * @param projectName 项目名称（模糊查询）
     * @param feeType 费用类型
     * @param hospitalId 医疗机构ID
     * @param commonPage 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<ProjectFee>> list(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String feeType,
            @RequestParam(required = false) Long hospitalId,
            CommonPage commonPage) {
        // 构建查询条件
        Query query = new Query();

        // 项目名称模糊查询
        if (StrUtil.isNotBlank(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 费用类型精确匹配
        if (StrUtil.isNotBlank(feeType)) {
            query.addCriteria(Criteria.where("feeType").is(feeType));
        }
        
        // 医疗机构ID精确匹配
        if (hospitalId != null) {
            query.addCriteria(Criteria.where("hospitalId").is(hospitalId));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, ProjectFee.class, query, commonPage);

        // 执行查询
        List<ProjectFee> projectFees = mongoTemplate.find(query, ProjectFee.class);

        return CommonResult.successPageData(projectFees, commonPage);
    }

    /**
     * 根据ID获取项目费用详情
     * @param id 项目费用ID
     * @return 项目费用详情
     */
    @GetMapping("/detail")
    public CommonResult<ProjectFee> getDetail(@RequestParam Long id) {
        ProjectFee projectFee = mongoTemplate.findById(id, ProjectFee.class);
        if (projectFee == null) {
            return CommonResult.failResult(10001, "项目费用不存在");
        }
        return CommonResult.successData(projectFee);
    }

    /**
     * 新增项目费用
     * @param projectFee 项目费用信息
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<ProjectFee> add(@RequestBody ProjectFee projectFee) {
        // 生成唯一ID
        projectFee.setId(SnGeneratorUtil.getId());

        // 设置创建时间和更新时间
        Date now = new Date();
        projectFee.setCreateTime(now);
        projectFee.setUpdateTime(now);

        // 保存到数据库
        mongoTemplate.save(projectFee);

        return CommonResult.successData(projectFee);
    }

    /**
     * 更新项目费用
     * @param projectFee 项目费用信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<ProjectFee> update(@RequestBody ProjectFee projectFee) {
        // 检查ID是否存在
        if (projectFee.getId() == null) {
            return CommonResult.failResult(10001, "项目费用ID不能为空");
        }

        // 查询原有数据
        ProjectFee existingFee = mongoTemplate.findById(projectFee.getId(), ProjectFee.class);
        if (existingFee == null) {
            return CommonResult.failResult(10001, "项目费用不存在");
        }

        // 设置更新时间
        projectFee.setUpdateTime(new Date());
        // 保留创建时间
        projectFee.setCreateTime(existingFee.getCreateTime());

        // 更新到数据库
        mongoTemplate.save(projectFee);

        return CommonResult.successData(projectFee);
    }

    /**
     * 批量删除项目费用
     * @param in 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody ProjectFeeBatchDeleteIn in) {
        // 检查ID列表是否为空
        if (in.getIds() == null || in.getIds().isEmpty()) {
            return CommonResult.failResult(10001, "项目费用ID列表不能为空");
        }

        // 构建查询条件
        Query query = new Query(Criteria.where("_id").in(in.getIds()));

        // 执行删除
        DeleteResult result = mongoTemplate.remove(query, ProjectFee.class);

        return CommonResult.successData(result.getDeletedCount());
    }
}
