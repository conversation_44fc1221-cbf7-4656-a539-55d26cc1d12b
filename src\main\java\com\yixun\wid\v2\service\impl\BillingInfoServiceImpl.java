package com.yixun.wid.v2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.BillingInfoBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.entity.BillingInfo;
import com.yixun.wid.v2.entity.MedicalCases;
import com.yixun.wid.v2.service.BillingInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账单信息业务逻辑实现类
 */
@Slf4j
@Service
public class BillingInfoServiceImpl implements BillingInfoService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public List<BillingInfo> list(Long medicalCasesId, String hospital, String treatmentType, CommonPage commonPage) {
        Query query = new Query();
        
        // 工伤待遇业务ID查询
        if (medicalCasesId != null) {
            query.addCriteria(Criteria.where("medicalCasesId").is(medicalCasesId));
        }
        
        // 医院名称模糊查询
        if (StrUtil.isNotBlank(hospital)) {
            query.addCriteria(Criteria.where("hospital").regex(".*" + hospital + ".*", "i"));
        }
        
        // 治疗类型精确匹配
        if (StrUtil.isNotBlank(treatmentType)) {
            query.addCriteria(Criteria.where("treatmentType").is(treatmentType));
        }
        
        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        
        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, BillingInfo.class, query, commonPage);
        
        // 执行查询
        return mongoTemplate.find(query, BillingInfo.class);
    }

    @Override
    public BillingInfo getDetail(Long id) {
        BillingInfo billingInfo = mongoTemplate.findById(id, BillingInfo.class);
        if (billingInfo == null) {
            throw new RuntimeException("账单信息不存在");
        }
        return billingInfo;
    }

    @Override
    public BillingInfo add(BillingInfoIn billingInfoIn) {
        // 验证关联的工伤待遇业务是否存在
        Long medicalCasesId = billingInfoIn.getMedicalCasesId();
        MedicalCases medicalCases = mongoTemplate.findById(medicalCasesId, MedicalCases.class);
        if (medicalCases == null) {
            throw new RuntimeException("关联的工伤待遇业务不存在");
        }
        
        // 创建新的账单信息对象
        BillingInfo billingInfo = new BillingInfo();
        BeanUtils.copyProperties(billingInfoIn, billingInfo);
        
        // 生成唯一ID
        billingInfo.setId(SnGeneratorUtil.getId());
        
        // 设置创建时间和更新时间
        Date now = new Date();
        billingInfo.setCreateTime(now);
        billingInfo.setUpdateTime(now);
        
        // 保存到数据库
        mongoTemplate.save(billingInfo);
        
        return billingInfo;
    }

    @Override
    public BillingInfo update(BillingInfo billingInfo) {
        // 检查ID是否存在
        if (billingInfo.getId() == null) {
            throw new RuntimeException("账单信息ID不能为空");
        }

        // 查询原有数据
        BillingInfo existingInfo = mongoTemplate.findById(billingInfo.getId(), BillingInfo.class);
        if (existingInfo == null) {
            throw new RuntimeException("账单信息不存在");
        }

        // 设置更新时间，保留创建时间
        billingInfo.setUpdateTime(new Date());
        billingInfo.setCreateTime(existingInfo.getCreateTime());
        
        // 保留关联的工伤待遇业务ID
        if (billingInfo.getMedicalCasesId() == null) {
            billingInfo.setMedicalCasesId(existingInfo.getMedicalCasesId());
        }
        
        // 更新到数据库
        mongoTemplate.save(billingInfo);

        return billingInfo;
    }

    @Override
    public Long batchDelete(BillingInfoBatchDeleteIn batchDeleteIn) {
        // 检查ID列表是否为空
        List<Long> ids = batchDeleteIn.getIds();
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("账单信息ID列表不能为空");
        }

        // 删除关联的账单明细
        for (Long billingInfoId : ids) {
            // 查询关联的账单明细
            Query detailQuery = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
            mongoTemplate.remove(detailQuery, "billingDetail");
        }
        
        // 执行删除账单信息
        Query deleteQuery = Query.query(Criteria.where("_id").in(ids));
        DeleteResult result = mongoTemplate.remove(deleteQuery, BillingInfo.class);

        return result.getDeletedCount();
    }

    @Override
    public List<BillingInfo> listByMedicalCases(Long medicalCasesId) {
        // 验证工伤待遇业务是否存在
        MedicalCases medicalCases = mongoTemplate.findById(medicalCasesId, MedicalCases.class);
        if (medicalCases == null) {
            throw new RuntimeException("工伤待遇业务不存在");
        }
        
        // 查询关联的账单信息
        Query query = Query.query(Criteria.where("medicalCasesId").is(medicalCasesId));
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        
        return mongoTemplate.find(query, BillingInfo.class);
    }
} 