package com.yixun.wid.utils;

import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.exception.UnLoginException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 获取当前用户的 辅助类
 */
@Component
public class UserHelper {

	/**
	 * 得到当前用户Id
	 * 
	 * @return Long
	 */
	public static Long getCurrentUserId() {
        String env = SpringContextHolder.getContext().getEnvironment().getProperty("spring.profiles.active");
        String secret = SpringContextHolder.getContext().getEnvironment().getProperty("jwt.secret.account");
        if (env.equals("dev")){
            return 1L;
        }

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tokenValue = request.getHeader("Authorization");
        long uid = 0;
        if(tokenValue != null && tokenValue.startsWith("Bearer ")){
            try {
                String authToken = tokenValue.substring("Bearer ".length());
                Claims claims = Jwts.parser().setSigningKey(secret).parseClaimsJws(authToken).getBody();
                Object userIdObj = claims.get("userId");
                if (userIdObj != null) {
                    String userId = userIdObj.toString();
                    uid= Long.parseLong(userId);
                }
            }catch (Exception e){
                throw new UnLoginException(ErrorMessage.not_logged_in+1);
            }
        }else {
            throw new UnLoginException(ErrorMessage.not_logged_in+2);
        }

        return uid;
	}
	
}
