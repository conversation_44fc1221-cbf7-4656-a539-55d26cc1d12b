package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.support.ExcelTypeEnum;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.fill.FillConfig;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.AdminUserHelper;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.BizItem;
import com.yixun.wid.v2.entity.BizReviews;
import com.yixun.wid.v2.entity.ReviewRecommendation;
import com.yixun.wid.v2.enums.GeneralStatusEnum;
import com.yixun.wid.v2.enums.UserType;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.*;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 评价相关接口
 */
@SuppressWarnings("DuplicatedCode")
@RequestMapping("/v2/biz/reviews")
@RestController
@AllArgsConstructor
public class BizReviewsController {

	private final MongoTemplate mongoTemplate;

	private final AdministratorService administratorService;

	private final UserService userService;

	private final BizItemController bizItemController;

	private final ObjectMapper objectMapper;

	public static final ClassPathResource CLASS_PATH_RESOURCE = new ClassPathResource("file/评价导出表.xlsx");

	/**
	 * 获取评价推荐列表
	 * @param reviewsLevel 评价级别
	 * @return 评价推荐列表
	 */
	@GetMapping("/recommendations")
	public CommonResult<List<ReviewRecommendation>> getRecommendationList(@RequestParam Integer reviewsLevel) {
		Query query = new Query();
		query.addCriteria(Criteria.where("reviewsLevel").is(reviewsLevel));
		List<ReviewRecommendation> reviewRecommendations = mongoTemplate.find(query, ReviewRecommendation.class);
		return CommonResult.successData(reviewRecommendations);
	}

	/**
	 * 评价导出
	 */
	@PostMapping("/export")
	public void export() throws UnsupportedEncodingException {
		Long userId = AdminUserHelper.getCurrentUserId();
		Query query = Query.query(Criteria.where("evaluatedUserId").is(userId))
			.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		List<BizReviews> bizReviews = mongoTemplate.find(query, BizReviews.class);
		List<SimpleBizReviews> simpleBizReviews = BeanUtil.copyToList(bizReviews, SimpleBizReviews.class);

		List<SimpleBizReviewsV2> deserializedList;
		try {
			String jsonString = objectMapper.writeValueAsString(simpleBizReviews);
			deserializedList = objectMapper.readValue(jsonString, new TypeReference<List<SimpleBizReviewsV2>>() {});
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		HttpServletResponse response = ServletRequestUtils.getResp();
		response.setContentType(ExcelUtil.XLSX_CONTENT_TYPE);
		response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" +
			URLEncoder.encode("评价导出表.xlsx", StandardCharsets.UTF_8.toString()));

		WriteCellStyle headStyle = new WriteCellStyle();
		headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headStyle.setWrapped(true);

		WriteCellStyle contentStyle = new WriteCellStyle();
		contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentStyle.setWrapped(true);

		HorizontalCellStyleStrategy styleStrategy =
			new HorizontalCellStyleStrategy(headStyle, contentStyle);

		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), MaterialsFileVO.class)
			.needHead(false)
			.registerWriteHandler(styleStrategy)
			.withTemplate(CLASS_PATH_RESOURCE.getInputStream())
			.excelType(ExcelTypeEnum.XLSX).build()) {
			WriteSheet writeSheet = EasyExcel.writerSheet().build();
			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			excelWriter.fill(deserializedList, fillConfig, writeSheet);
			excelWriter.finish();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

	}

	/**
	 * 分页查询评价
	 *
	 * @param commonPage 分页参数
	 * @return 评价列表
	 */
	@GetMapping("/page")
	public CommonResult<List<SimpleBizReviews>> page(CommonPage commonPage) {
		Long userId = AdminUserHelper.getCurrentUserId();
		Query query = Query.query(Criteria.where("evaluatedUserId").is(userId))
			.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		MongoUtil.setPageInfo(mongoTemplate, BizReviews.class, query, commonPage);
		List<BizReviews> bizReviews = mongoTemplate.find(query, BizReviews.class);
		List<SimpleBizReviews> simpleBizReviews = BeanUtil.copyToList(bizReviews, SimpleBizReviews.class);
		return CommonResult.successData(simpleBizReviews);
	}

	/**
	 * 获取评价二维码
	 *
	 * @return 评价二维码信息
	 */
	@GetMapping("/qrcode")
	public CommonResult<BizReviewsQrCode> qrcode() {
		Long userId = AdminUserHelper.getCurrentUserId();
		Administrator administrator = administratorService.getAdministratorById(userId);
		QrConfig qrConfig = QrConfig.create();
		qrConfig.setHeight(300);
		qrConfig.setWidth(300);
		QrCodeData qrCodeData = new QrCodeData();
		qrCodeData.setUserId(userId);
		String base64 = QrCodeUtil.generateAsBase64(JSONUtil.toJsonStr(qrCodeData), qrConfig, ImgUtil.IMAGE_TYPE_JPEG);
		BizReviewsQrCode bizReviewsQrCode = new BizReviewsQrCode();
		bizReviewsQrCode.setBase64(base64);
		return CommonResult.successData(bizReviewsQrCode);
	}

	public String getUserName() {
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.ADMIN.equals(userType)) {
			Administrator administratorById = administratorService.getAdministratorById(userId);
			return administratorById.getRealName();
		}
		if (UserType.USER.equals(userType)) {
			User userById = userService.getUserById(userId);
			return userById.getRealName();
		}
		return null;
	}

	/**
	 * 新增评价
	 *
	 * @param bizReviews 评价
	 * @return 操作结果
	 */
	@PostMapping("/save")
	public CommonResult<Void> save(@Validated @RequestBody BizReviews bizReviews) {
		bizReviews.setId(SnGeneratorUtil.getId());
		bizReviews.setDeleted(GeneralStatusEnum.OFF.getCode());
		bizReviews.setStatus(GeneralStatusEnum.OFF.getCode());

		Date current = new Date();
		bizReviews.setCreateTime(current);
		bizReviews.setUpdateTime(current);
		bizReviews.setReviewsTime(current);
		bizReviews.setReviewsUserId(ServletRequestUtils.getUserId());
		bizReviews.setReviewsUserName(this.getUserName());

		Long itemId = bizReviews.getBizItemId();
		CommonResult<BizItem> byId = bizItemController.getById(itemId);
		BizItem data = byId.getData();
		bizReviews.setBizItemName(data.getBizItemName());

		mongoTemplate.save(bizReviews);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 更新评价
	 *
	 * @param bizReviews 评价
	 * @return 操作结果
	 */
	@PostMapping("/update")
	public CommonResult<Void> update(@RequestBody BizReviews bizReviews) {
		Long id = bizReviews.getId();
		if (ObjectUtil.isNull(id)) {
			throw new RuntimeException("id不能为空");
		}
		BizItem byId = mongoTemplate.findById(id, BizItem.class);
		if (ObjectUtil.isNull(byId)) {
			throw new RuntimeException("评价不存在");
		}
		BeanUtil.copyProperties(bizReviews, byId, CopyOptions.create().ignoreNullValue());
		byId.setUpdateTime(new Date());
		mongoTemplate.save(byId);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 回复评价
	 *
	 * @param bizReviews 评价
	 * @return 操作结果
	 */
	@PostMapping("/reply")
	public CommonResult<Void> reply(@RequestBody BizReviews bizReviews) {
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.USER.equals(userType)) {
			throw new RuntimeException("用户不能回复评价");
		}
		Long id = bizReviews.getId();
		if (ObjectUtil.isNull(id)) {
			throw new RuntimeException("id不能为空");
		}
		Query query = Query.query(Criteria.where("id").is(id));
		Update update = Update.update("id", id)
			.set("status", GeneralStatusEnum.ON.getValue())
			.set("reviewsRespContent", bizReviews.getReviewsContent())
			.set("reviewsRespTime", new Date())
			.set("reviewsRespUserId", userId)
			.set("reviewsRespUserName", this.getUserName())
			.set("updateTime", new Date());
		mongoTemplate.updateFirst(query, update, BizReviews.class);
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 通过办事项目查询评价
	 *
	 * @param bizItemId 办事项目id
	 * @return 评价
	 */
	@GetMapping("/bizItem")
	public CommonResult<BizReviews> getByBizItemId(@RequestParam Long bizItemId) {
		Query query = Query.query(Criteria.where("bizItemId").is(bizItemId))
			.addCriteria(Criteria.where("deleted").is(0));
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.USER.equals(userType)) {
			query.addCriteria(Criteria.where("reviewsUserId").is(userId));
		}
		List<BizReviews> bizReviews = mongoTemplate.find(query, BizReviews.class);
		BizReviews reviews = bizReviews.get(0);
		if (ObjectUtil.isNull(reviews)) {
			throw new RuntimeException("评价不存在");
		}
		return CommonResult.successData(reviews);
	}

	/**
	 * 通过办事项目查询评价
	 *
	 * @param id 评价id
	 * @return 评价
	 */
	@GetMapping("/id")
	public CommonResult<BizReviews> getById(@RequestParam Long id) {
		Query query = Query.query(Criteria.where("id").is(id))
			.addCriteria(Criteria.where("deleted").is(0));
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.USER.equals(userType)) {
			query.addCriteria(Criteria.where("reviewsUserId").is(userId));
		}
		List<BizReviews> bizReviews = mongoTemplate.find(query, BizReviews.class);
		BizReviews reviews = bizReviews.get(0);
		if (ObjectUtil.isNull(reviews)) {
			throw new RuntimeException("评价不存在");
		}
		return CommonResult.successData(reviews);
	}

	/**
	 * 查询评价列表
	 *
	 * @param bizItemName  办事项目
	 * @param status       回复状态
	 * @param reviewsLevel 评价等级
	 * @return 评价列表
	 */
	@GetMapping("/list")
	public CommonResult<List<BizReviews>> list(@RequestParam(required = false) String bizItemName,
		@RequestParam(required = false) Integer status,
		@RequestParam(required = false) Integer reviewsLevel) {
		Query query =
			Query.query(Criteria.where("deleted").is(GeneralStatusEnum.OFF.getValue()))
				.with(Sort.by(Sort.Direction.DESC, "createTime", "updateTime"));
		if (StrUtil.isNotBlank(bizItemName)) {
			query.addCriteria(Criteria.where("bizItemName").regex(".*" + bizItemName + ".*", "i"));
		}
		if (ObjectUtil.isNotNull(status)) {
			query.addCriteria(Criteria.where("status").is(status));
		}
		if (ObjectUtil.isNotNull(reviewsLevel)) {
			query.addCriteria(Criteria.where("reviewsLevel").is(reviewsLevel));
		}
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.USER.equals(userType)) {
			query.addCriteria(Criteria.where("reviewsUserId").is(userId));
		}
		List<BizReviews> reviews = mongoTemplate.find(query, BizReviews.class);
		return CommonResult.successData(reviews);
	}


}
