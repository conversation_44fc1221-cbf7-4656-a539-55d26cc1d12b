package com.yixun.wid.v2.utils;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

public class ReviewsLevelDeserializer extends JsonDeserializer<String> {
	@Override
	public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
		Integer integer = p.readValueAs(Integer.class);
		if (integer != null) {
			switch (integer) {
				case 1:
					return "很差";
				case 2:
					return "较差";
				case 3:
					return "一般";
				case 4:
					return "满意";
				case 5:
					return "非常满意";
				default:
					break;
			}
		}
		return "一般";
	}
}
