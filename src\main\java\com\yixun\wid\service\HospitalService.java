package com.yixun.wid.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.HospitalGetIn;
import com.yixun.wid.entity.Hospital;

import java.util.List;

public interface HospitalService {

    void save(Hospital hospital);

    Hospital getById(Long hospitalId);

    void update(Hospital hospital);

    void delete(Hospital hospital);

    List<Hospital> getList(HospitalGetIn getIn, CommonPage commonPage);
}
