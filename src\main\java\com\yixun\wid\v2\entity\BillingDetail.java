package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子清单实体
 */
@Data
@Document("billingDetail")
public class BillingDetail {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    /**
     * 关联的账单信息ID，多对一关系
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billingInfoId;
    
    /**
     * 明细在当前账单中的序号
     */
    private Integer orderNum;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 项目编码
     */
    private String projectCode;
    
    /**
     * 费用类别
     */
    private String feeType;
    
    /**
     * 费用分类
     */
    private String feeCategory;
    
    /**
     * 费用等级
     */
    private String feeLevel;
    
    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 单价(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer unitPriceInCent;
    
    /**
     * 单价(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal unitPrice;
    
    /**
     * 金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer amountInCent;
    
    /**
     * 金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal amount;
    
    /**
     * 是否工伤
     */
    private Boolean isWorkInjury;
    
    /**
     * 不可报销金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer nonReimbursableAmountInCent;
    
    /**
     * 不可报销金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal nonReimbursableAmount;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
    
    /**
     * 获取单价(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getUnitPrice() {
        if (unitPriceInCent == null) {
            return null;
        }
        return new BigDecimal(unitPriceInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置单价(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        if (unitPrice == null) {
            this.unitPriceInCent = null;
            return;
        }
        this.unitPriceInCent = unitPrice.multiply(new BigDecimal(100)).intValue();
    }
    
    /**
     * 获取金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getAmount() {
        if (amountInCent == null) {
            return null;
        }
        return new BigDecimal(amountInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setAmount(BigDecimal amount) {
        if (amount == null) {
            this.amountInCent = null;
            return;
        }
        this.amountInCent = amount.multiply(new BigDecimal(100)).intValue();
    }
    
    /**
     * 获取不可报销金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置不可报销金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setNonReimbursableAmount(BigDecimal nonReimbursableAmount) {
        if (nonReimbursableAmount == null) {
            this.nonReimbursableAmountInCent = null;
            return;
        }
        this.nonReimbursableAmountInCent = nonReimbursableAmount.multiply(new BigDecimal(100)).intValue();
    }
} 