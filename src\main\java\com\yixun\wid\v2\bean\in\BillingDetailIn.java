package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 账单明细输入参数
 */
@Data
public class BillingDetailIn {
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 项目编码
     */
    private String projectCode;
    
    /**
     * 费用类别
     */
    private String feeType;
    
    /**
     * 是否工伤
     */
    private Boolean isWorkInjury;
    
    /**
     * 关联的账单信息ID
     */
    @NotNull(message = "账单信息ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billingInfoId;
    
    /**
     * 明细在当前账单中的序号
     */
    private Integer orderNum;
    
    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 单价(元)
     */
    private BigDecimal unitPrice;
    
    /**
     * 金额(元)
     */
    private BigDecimal amount;
    
    /**
     * 不可报销金额(元)
     */
    private BigDecimal nonReimbursableAmount;
} 