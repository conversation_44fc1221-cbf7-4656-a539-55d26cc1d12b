package com.yixun.wid.v2.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量接收工伤待遇业务任务的结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchReceiveResult {
    /**
     * 成功接收的案件数量
     */
    private Integer successCount;
    
    /**
     * 失败的案件数量
     */
    private Integer failCount;
    
    /**
     * 失败的案件列表
     */
    private List<FailedCase> failList;
    
    /**
     * 失败的案件信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FailedCase {
        /**
         * 案件ID
         */
        private String id;
        
        /**
         * 失败原因
         */
        private String reason;
    }
} 