package com.yixun.wid.v2.vo.ai;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Date;

/**
 * AcceptedInformationDiagnosisVO测试类
 */
public class AcceptedInformationDiagnosisVOTest {

    @Test
    public void testAcceptedInformationDiagnosisVOCreation() {
        // 测试创建AcceptedInformationDiagnosisVO对象
        AcceptedInformationDiagnosisVO vo = new AcceptedInformationDiagnosisVO();
        
        // 设置测试数据
        vo.setId(12345L);
        vo.setAccidentDate(new Date());
        vo.setWorkerName("张三");
        vo.setGender("男");
        vo.setIdCard("110101199001011234");
        vo.setOrganization("测试公司有限责任公司");
        vo.setInjuryDiagnoses(Arrays.asList("左腿骨折", "软组织损伤"));

        // 验证对象创建成功
        assert vo.getId().equals(12345L);
        assert "张三".equals(vo.getWorkerName());
        assert "男".equals(vo.getGender());
        assert "110101199001011234".equals(vo.getIdCard());
        assert "测试公司有限责任公司".equals(vo.getOrganization());
        assert vo.getInjuryDiagnoses().size() == 2;
        assert vo.getInjuryDiagnoses().contains("左腿骨折");
        assert vo.getInjuryDiagnoses().contains("软组织损伤");
        
        System.out.println("AcceptedInformationDiagnosisVO对象创建测试通过");
    }

    @Test
    public void testAcceptedInformationDiagnosisVOWithNullValues() {
        // 测试创建空的AcceptedInformationDiagnosisVO对象
        AcceptedInformationDiagnosisVO vo = new AcceptedInformationDiagnosisVO();
        
        // 验证默认值
        assert vo.getId() == null;
        assert vo.getAccidentDate() == null;
        assert vo.getWorkerName() == null;
        assert vo.getGender() == null;
        assert vo.getIdCard() == null;
        assert vo.getOrganization() == null;
        assert vo.getInjuryDiagnoses() == null;
        
        System.out.println("AcceptedInformationDiagnosisVO空值测试通过");
    }

    @Test
    public void testAcceptedInformationDiagnosisVOPartialData() {
        // 测试部分数据设置
        AcceptedInformationDiagnosisVO vo = new AcceptedInformationDiagnosisVO();
        
        // 只设置部分字段
        vo.setWorkerName("李四");
        vo.setGender("女");
        
        // 验证设置的字段
        assert "李四".equals(vo.getWorkerName());
        assert "女".equals(vo.getGender());
        
        // 验证未设置的字段为null
        assert vo.getId() == null;
        assert vo.getAccidentDate() == null;
        assert vo.getIdCard() == null;
        assert vo.getOrganization() == null;
        assert vo.getInjuryDiagnoses() == null;
        
        System.out.println("AcceptedInformationDiagnosisVO部分数据测试通过");
    }
}
