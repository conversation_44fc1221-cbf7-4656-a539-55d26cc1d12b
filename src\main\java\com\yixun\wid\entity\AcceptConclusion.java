package com.yixun.wid.entity;

import com.yixun.wid.v2.entity.ConclusionReview;
import com.yixun.wid.v2.entity.LegalClause;
import lombok.Data;

import java.util.List;

@Data
public class AcceptConclusion {

    private String conclusion;
    private String reason;
    private String writSn;

	// 受理时间 v2.0.1新增
	private String acceptDateTime;

	// v2.4.0新增 AI辅助审核

	/**
	 * 法规依据
	 */
	private List<LegalClause> legalClauses;

	/**
	 * AI结论审核
	 */
	private ConclusionReview conclusionReview;

}
