package com.yixun.wid.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yixun.wid.entity.AreaDict;
import com.yixun.wid.mapper.AreaDictMapper;
import com.yixun.wid.service.AreaDictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AreaDictServiceImpl implements AreaDictService {

    @Resource
    private AreaDictMapper areaDictMapper;

    @Override
    public List<AreaDict> getAreaList(Long pcode) {
        QueryWrapper<AreaDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pcode", pcode);
        return areaDictMapper.selectList(queryWrapper);
    }

    @Override
    public List<AreaDict> getAllAreaList() {
        return areaDictMapper.selectList(null);
    }

    @Override
    public AreaDict getByCode(Long code) {
        QueryWrapper<AreaDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return areaDictMapper.selectOne(queryWrapper);
    }
}
