package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@Document("materialsList")
public class MaterialsListPO {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 文件名称
	 */
	private String name;

	/**
	 * 区域
	 */
	private List<String> area;

	/**
	 * 清单类型
	 */
	private String materialsListType;

	/**
	 * 开始时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date startTime;

	/**
	 * 结束时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date endTime;

	/**
	 * 文件
	 */
	private String file;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 文件是否过期删除
	 */
	private Boolean fileExpired;

}
