package com.yixun.wid.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.AdministratorGetIn;
import com.yixun.wid.bean.out.AdministratorOut;
import com.yixun.wid.entity.Administrator;

import java.util.List;

public interface AdministratorService extends IService<Administrator> {

    Administrator getAdministratorById(Long id);

    void insert(Administrator administrator);

    void update(Administrator administrator);

    Administrator getAdministratorByName(String name);

    List<Administrator> getUserList();

    Page<Administrator> getAdminPageList(AdministratorGetIn administratorGetIn, CommonPage page);

    void delete(Long administratorId);

    Page<AdministratorOut> getAdministratorPageList(AdministratorGetIn administratorGetIn, CommonPage page);
}
