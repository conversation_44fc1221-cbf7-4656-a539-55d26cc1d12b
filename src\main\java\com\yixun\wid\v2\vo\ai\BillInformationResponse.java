package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账单信息响应结果
 */
@Data
public class BillInformationResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 数据
     */
    private BillData data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class BillData {
        /**
         * 账单信息列表
         */
        @JSONField(name = "bill_Information")
        private List<BillInformation> billInformation;
    }

    @Data
    public static class BillInformation {
        /**
         * 发票号码
         */
        @JSONField(name = "bill_number")
        private String billNumber;

        /**
         * 医院
         */
        private String hospital;

        /**
         * 治疗类型：门诊/住院
         */
        @JSONField(name = "treatment_type")
        private String treatmentType;

        /**
         * 发票总金额
         */
        @JSONField(name = "bill_amount")
        private BigDecimal billAmount;

        /**
         * 医疗统筹基金支付金额
         */
        @JSONField(name = "medical_co-ordination_fund")
        private Integer medicalCoOrdinationFund;

        /**
         * 入院时间（住院用）
         */
        @JSONField(name = "admission_time")
        private String admissionTime;

        /**
         * 出院时间（住院用）
         */
        @JSONField(name = "discharge_time")
        private String dischargeTime;

        /**
         * 住院天数
         */
        @JSONField(name = "stay_days")
        private Integer stayDays;

        /**
         * 门诊时间（门诊用）
         */
        @JSONField(name = "clinic_time")
        private String clinicTime;

        /**
         * 费用明细
         */
        @JSONField(name = "expense_details")
        private List<ExpenseDetail> expenseDetails;
    }

    @Data
    public static class ExpenseDetail {
        /**
         * 费用项目(治疗费，检查治疗费，药品费，材料费，血费，床位费)
         */
        @JSONField(name = "expense_item")
        private String expenseItem;

        /**
         * 账单金额
         */
        private BigDecimal amount;

	    /**
	     * 金额明细，由哪些金额求和
	     */
	    @JSONField(name = "amount_list")
		private List<BigDecimal> amountList;

        /**
         * 清单
         */
        @JSONField(name = "expense_list")
        private List<ExpenseList> expenseList;
    }

    @Data
    public static class ExpenseList {
        /**
         * 费用名称
         */
        private String name;

        /**
         * 项目编码
         */
        private String code;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 单价
         */
        @JSONField(name = "unit_price")
        private BigDecimal unitPrice;

        /**
         * 金额
         */
        private BigDecimal amount;
    }
}
