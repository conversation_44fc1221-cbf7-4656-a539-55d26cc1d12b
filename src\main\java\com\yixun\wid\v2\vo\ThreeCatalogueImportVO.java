package com.yixun.wid.v2.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 三目录导入实体类
 */
@Data
public class ThreeCatalogueImportVO {

    /**
     * 目录编码
     */
    @ExcelProperty(value = "目录编码", index = 0)
    private String sn;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "*项目名称", index = 1)
    private String projectName;

    /**
     * 费用等级 (甲/乙/丙)
     */
    @ExcelProperty(value = "*费用等级", index = 2)
    private String level;

    /**
     * 目录类别
     */
    @ExcelProperty(value = "*目录类别", index = 3)
    private String type;

    /**
     * 开始日期 (年/月/日格式)
     */
    @ExcelProperty(value = "开始日期", index = 4)
    private String startDate;

    /**
     * 结束日期 (年/月/日格式)
     */
    @ExcelProperty(value = "结束日期", index = 5)
    private String endDate;

    /**
     * 限价医院等级
     */
    @ExcelProperty(value = "限价医院等级", index = 6)
    private String priceLimitLevel;

    /**
     * 定价上限金额(元)
     */
    @ExcelProperty(value = "定价上限金额", index = 7)
    private String maxPrice;

    /**
     * 错误信息（导入失败时使用）
     */
    @ExcelProperty(value = "错误信息", index = 8)
    private String errorMsg;
}
