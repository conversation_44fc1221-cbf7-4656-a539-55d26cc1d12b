package com.yixun.wid.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.entity.DiagnosticInfo;
import com.yixun.wid.entity.em.CasesStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CasesIn {

    private String submitWay;
    private String relationship;

    @ApiModelProperty(value = "与当事人关系详情")
    private String relationshipDetail;

    private String reportName;
    private String reportPhone;
    private CasesStatus status;
    private Boolean hasInsurance;
    private String insuranceAddress;
    private String name;
    private String gender;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthdayDate;
    private String idCard;
    private String idCardAddr;
    private String phone;
    private Long organizationId;
    private String organization;
    private String creditCode;
    private List position;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstWorkDate;
    private List organizationRegion;
    private String organizationAddr;
    @ApiModelProperty("单位地址经度")
    private Double organizationLongitude;

    @ApiModelProperty("单位地址纬度")
    private Double organizationLatitude;
    private String zipCode;
    private List injuredPart;
    private String diseaseName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentTime;
    private List accidentRegion;
    private String accidentAddress;
    private Double longitude;
    private Double latitude;
    private List reportRegion;
    private String reportAddress;
    private Double reportLongitude;
    private Double reportLatitude;
    private String accidentDetail;
    private String accidentCause;
    private String workingOfAccident;
    private String supplementary;
    private String involvedNumber;
    private Boolean isOccupDiseaseRelated;
    private String occupDiseaseName;
    private String occupDiseasePosition;
    private String occupDiseaseDate;
    private String materialSubmitWay;
    private Boolean isNoNeedApply;
    private String noNeedReason;
    private String noNeedReasonOther;

    //事故相关材料
    @ApiModelProperty(value = "现场照片")
    private List pictures;

    @ApiModelProperty(value = "现场视频")
    private List videos;

    @ApiModelProperty(value = "受伤害部位图片")
    private List injuredPartPics;

    @ApiModelProperty(value = "职工工伤事故情况快报表")
    private List reportFormPics;

    @ApiModelProperty("案件申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

	@ApiModelProperty("诊断信息")
	private List<DiagnosticInfo> diagnosticInfoList;


}
