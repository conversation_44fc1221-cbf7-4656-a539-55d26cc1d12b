package com.yixun.wid.v2.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.bean.CommonPage;
import com.yixun.wid.entity.em.DictType;
import com.yixun.wid.v2.entity.DataDict;

import java.util.List;

public interface DataDictService {

	void insert(DataDict dataDict);

	void update(DataDict dataDict);

	DataDict getOne(String nodecode, String datatype);

	List<DataDict> getDataDictList(String parentcode, String datatype);

	List<DataDict> getAllDataDictList(String datatype);

	Page<DataDict> getAdminDataDictList(String parentcode, DictType datatype, String label, CommonPage page);

	void deleteByType(String datatype);

	List<DataDict> getDataDictListByNodes(List<String> nodes, String datatype);

	List<DataDict> getByLabelList(List<String> labelList, String datatype);

//	/**
//	 * 获取行业列表
//	 *
//	 * @param industry  一级行业
//	 * @param industry2 二级行业
//	 * @param industry3 三级行业
//	 * @return List<CodeName>
//	 */
//	List<CodeName> getIndustryListByName(String industry, String industry2, String industry3);

//	/**
//	 * 获取重点行业编码
//	 *
//	 * @param importantIndustry 重点行业名
//	 * @return 行业编码
//	 */
//	String getIndustry(String importantIndustry);
//
//	/**
//	 * 获取行业层级列表。
//	 * 该方法基于传入的基础行业列表，递归查询其所有子行业，并返回所有行业路径的列表。
//	 *
//	 * @param baseIndustryList 基础行业列表
//	 * @return 所有行业路径的列表
//	 */
//	List<List<CodeName>> getIndustryHierarchy(List<CodeName> baseIndustryList);
}
