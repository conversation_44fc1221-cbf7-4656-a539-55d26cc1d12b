package com.yixun.wid.service.impl;

import com.yixun.bean.CommonPage;
import com.yixun.wid.entity.CorpInvestigate;
import com.yixun.wid.service.CorpInvestigateService;
import com.yixun.wid.utils.MongoUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CorpInvestigateServiceImpl implements CorpInvestigateService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void save(CorpInvestigate corpInvestigate) {
        mongoTemplate.save(corpInvestigate);
    }

    @Override
    public CorpInvestigate getById(Long corpInvestigateId) {
        return mongoTemplate.findById(corpInvestigateId, CorpInvestigate.class);
    }

    @Override
    public void update(CorpInvestigate corpInvestigate) {
        corpInvestigate.setUpdateTime(new Date());
        mongoTemplate.save(corpInvestigate);
    }

    @Override
    public List<CorpInvestigate> getList(Long userId, String companyName, CommonPage commonPage) {
        Query query = new Query();
        if (userId!=null){
            query.addCriteria(Criteria.where("userId").is(userId));
        }
        if (companyName!=null){
            query.addCriteria(Criteria.where("companyName").is(companyName));
        }

        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, CorpInvestigate.class, query, commonPage);
        return mongoTemplate.find(query, CorpInvestigate.class);
    }
}
