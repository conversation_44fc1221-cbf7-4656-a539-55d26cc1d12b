spring:
  data:
    mongodb:
      # 使用内存MongoDB进行测试
      uri: mongodb://localhost:27017/test
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mongo.embedded.EmbeddedMongoAutoConfiguration

# 禁用Nacos配置，避免测试时连接外部服务
nacos:
  config:
    enabled: false

# 禁用第三方服务调用
feign:
  client:
    enabled: false

# 禁用XXL-Job
xxl:
  job:
    enabled: false

# 测试环境Redis配置
spring.redis:
  database: 0
  host: localhost
  port: 6379
  password:
  timeout: 5000
  
# 测试环境日志级别
logging:
  level:
    com.yixun.wid: DEBUG
    org.springframework.data.mongodb: DEBUG 