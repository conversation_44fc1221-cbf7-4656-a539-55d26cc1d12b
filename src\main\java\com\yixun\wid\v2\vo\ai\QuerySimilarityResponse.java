package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 相似查找响应结果
 */
@Data
public class QuerySimilarityResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 数据列表
     */
    private List<SimilarityData> data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class SimilarityData {
        /**
         * 查询内容
         */
        private String query;

        /**
         * 目标列表 如果是rerank=true，返回的就是按相似度排序的targets
         */
        private List<String> targets;

        /**
         * 相似度列表
         */
        private List<Double> similarity;

        /**
         * 索引列表 如果是rerank=true，返回的就是targets排序后在原输入中的的id
         */
        private List<Integer> indices;

        /**
         * 重排序分数
         */
        @JSONField(name = "rerank_score")
        private List<Double> rerankScore;
    }
}
