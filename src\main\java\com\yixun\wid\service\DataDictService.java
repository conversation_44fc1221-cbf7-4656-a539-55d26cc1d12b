package com.yixun.wid.service;

import com.yixun.wid.entity.DataDict;
import com.yixun.wid.entity.OccupationDict;

import java.util.List;

public interface DataDictService {

    void insert(List<DataDict> dataDictList);

    void update(DataDict dataDict);

    List<DataDict> getDataDictList(String datatype);

    List<OccupationDict> getOccupationDictList(String parentcode, String occupationType);

    List<OccupationDict> getAllDataDictList(String datatype, Integer level);

}
