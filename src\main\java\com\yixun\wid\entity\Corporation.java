package com.yixun.wid.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.wid.utils.DateTimeJsonSerializer;
import com.yixun.wid.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Corporation {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateTime;

    @ApiModelProperty(value = "认证状态 参考CorpAuthStatus")
    private String status;

    @ApiModelProperty(value = "认证主账号用户id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "营业执照地址")
    private String businessLicense;

    @ApiModelProperty(value = "社会统一信用代码")
    private String creditCode;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司类型")
    private String companyType;

    @ApiModelProperty(value = "公司地址")
    private String businessAddress;

    @ApiModelProperty(value = "法人姓名")
    private String legalPerson;

    @ApiModelProperty(value = "注册资本金额")
    private String registeredCapital;

    @ApiModelProperty(value = "注册日期")
    private String registrationDate;

    @ApiModelProperty(value = "有效期")
    private String validPeriod;

    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    @ApiModelProperty(value = "法人身份证正面")
    private String legalPersonIdCardFront;

    @ApiModelProperty(value = "法人身份证反面")
    private String legalPersonIdCardBack;

    @ApiModelProperty(value = "法人电话")
    private String legalPersonPhone;

    @ApiModelProperty(value = "是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty(value = "参保地址")
    private String insuranceAddress;

    @ApiModelProperty(value = "是否工会组织")
    private Boolean isLaborUnion;

    @ApiModelProperty(value = "认证申请驳回原因")
    private String reason;

    @ApiModelProperty(value = "是否需要邮寄材料")
    private Boolean isMailable;

    @ApiModelProperty(value = "是否被托管")
    private Boolean isManaged;

	/**
	 * 经度
	 */
	private Double longitude;

	/**
	 * 纬度
	 */
	private Double latitude;

	/**
	 * 区域
	 */
	private List region;

}
