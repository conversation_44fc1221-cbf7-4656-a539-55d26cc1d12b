package com.yixun.wid.bean.common;

public class CommonErrorInfo {

    public static final Integer code_1001 = 1001; //参数错误
    public static final Integer code_2001 = 2001; //没有权限
    public static final Integer code_2101 = 2101; //没有token
    public static final Integer code_2102 = 2102; //token已过期
    public static final Integer code_3001 = 3001; //其他错误
    public static final Integer code_4001 = 4001; //未登录
    public static final Integer code_5001 = 5001; //未注册
    public static final Integer code_6001 = 6001; //数据错误
    public static final Integer code_7001 = 7001; //微信错误
    public static final Integer code_8001 = 8001; //访问频率过高
    public static final Integer code_9001 = 9001; //api调用错误
}
