package com.yixun.wid.v2.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OcrResp {

	private List<Result> result;

	@JsonProperty("success_status")
	private String successStatus;

	@JsonProperty("error_message")
	private String errorMessage;

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class Result {

		private String hospital;

		@JsonProperty("consultation_time")
		private String consultationTime;

		@JsonProperty("diagnostic_conclusion")
		private List<String> diagnosticConclusion;

	}

}
