<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="work-injury-declare-admin-api" type="DEPLOY_HOST_RUN_CONFIGURATION" factoryName="Deploy to Host">
    <option name="accountModel" />
    <option name="accountModels" />
    <option name="address" />
    <option name="afterCommand" value="/opt/server/work-injury-declare-admin-api/restart-work-injury-declare-admin-api.sh" />
    <option name="alreadyReset" value="true" />
    <option name="autoOpen" value="false" />
    <option name="beforeCommand" value="" />
    <option name="defaultTabIdx" value="0" />
    <option name="ecsInstance">
      <EcsInstance>
        <option name="OSType" />
        <option name="instanceId" />
        <option name="instanceName" />
        <option name="netType" />
        <option name="privateIps" />
        <option name="publicIps" />
        <option name="regionId" />
        <option name="tags" />
      </EcsInstance>
    </option>
    <option name="ecsInstances" />
    <option name="hostIds">
      <list>
        <option value="1" />
      </list>
    </option>
    <option name="hostTagId" value="0" />
    <option name="location" value="/opt/server/work-injury-declare-admin-api/" />
    <option name="pathOrUrl" value="C:\Users\<USER>\source\repos\yixun\work-injury-declare-admin-api\target\work-injury-declare-admin-api.jar" />
    <option name="tagId" value="0" />
    <option name="terminalCommand" />
    <option name="type" value="HOST" />
    <option name="uploadType" value="MAVEN" />
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/../work-injury-declare-admin-api\pom.xml" goal="clean install" />
    </method>
  </configuration>
</component>