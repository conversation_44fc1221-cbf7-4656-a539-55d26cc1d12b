package com.yixun.wid.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.DocIn;
import com.yixun.wid.entity.SurveyDocInfo;
import com.yixun.wid.service.SurveyDocInfoService;
import com.yixun.wid.utils.MongoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SurveyDocInfoServiceImpl implements SurveyDocInfoService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void saveSurveyDocInfo(SurveyDocInfo surveyDocInfo) {
        surveyDocInfo.setPublishTime(new Date());
        mongoTemplate.save(surveyDocInfo);
    }

    @Override
    public void batchSaveSurveyDocInfo(List<SurveyDocInfo> surveyDocInfo) {
        surveyDocInfo.forEach(e -> {
            this.saveSurveyDocInfo(e);
        });
    }

    @Override
    public List<SurveyDocInfo> getSurveyDocList(DocIn docIn, CommonPage commonPage) {
        Query query = new Query();
        if (StringUtils.hasLength(docIn.getDocName())) {
            query.addCriteria(Criteria.where("docName").regex(docIn.getDocName()));
        }
        if (docIn.getAccidentTimeStart() != null || docIn.getAccidentTimeEnd() != null) {
            Criteria accidentTimeCriteria = Criteria.where("caseSnapshot.accidentTime");
            Date accidentTimeStart = docIn.getAccidentTimeStart();
            if (accidentTimeStart != null) {
                String start = DateUtil.format(accidentTimeStart, DatePattern.NORM_DATE_PATTERN);
                DateTime startTime = DateUtil.parse(start + " 00:00:00", DatePattern.NORM_DATETIME_FORMAT);
                accidentTimeCriteria.gte(startTime);
            }
            Date accidentTimeEnd = docIn.getAccidentTimeEnd();
            if (accidentTimeEnd != null) {
                String end = DateUtil.format(accidentTimeEnd, DatePattern.NORM_DATE_PATTERN);
                DateTime endTime = DateUtil.parse(end + " 23:59:59", DatePattern.NORM_DATETIME_FORMAT);
                accidentTimeCriteria.lte(endTime);
            }
            query.addCriteria(accidentTimeCriteria);
        }
        if (StringUtils.hasLength(docIn.getName())) {
            query.addCriteria(Criteria.where("caseSnapshot.name").regex(docIn.getName()));
        }
        if (StringUtils.hasLength(docIn.getOrganization())) {
            query.addCriteria(Criteria.where("caseSnapshot.organization").regex(docIn.getOrganization()));
        }
        if (docIn.getSurveyOrgId() != null) {
            query.addCriteria(Criteria.where("surveyOrgId").is(docIn.getSurveyOrgId()));
        }
        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, SurveyDocInfo.class, query, commonPage);
        return mongoTemplate.find(query, SurveyDocInfo.class);
    }
}




