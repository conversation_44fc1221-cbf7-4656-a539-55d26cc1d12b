package com.yixun.wid.bean.weixin;

/**
 * Created by Ad<PERSON> on 2018/1/9.
 */

public class MiniUserInfo {

    /**
     * phoneNumber : 13398405099
     * purePhoneNumber : 13398405099
     * countryCode : 86
     * watermark : {"timestamp":1666339885,"appid":"wxfd2778094f23e14f"}
     */

    private String phoneNumber;
    private String purePhoneNumber;
    private String countryCode;
    private WatermarkBean watermark;

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPurePhoneNumber() {
        return purePhoneNumber;
    }

    public void setPurePhoneNumber(String purePhoneNumber) {
        this.purePhoneNumber = purePhoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public WatermarkBean getWatermark() {
        return watermark;
    }

    public void setWatermark(WatermarkBean watermark) {
        this.watermark = watermark;
    }

    public static class WatermarkBean {
        /**
         * timestamp : 1666339885
         * appid : wxfd2778094f23e14f
         */

        private int timestamp;
        private String appid;

        public int getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(int timestamp) {
            this.timestamp = timestamp;
        }

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }
    }
}
