package com.yixun.wid.v2.utils;

import com.alibaba.fastjson.JSON;
import com.yixun.wid.v2.vo.ai.*;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

/**
 * JSON序列化测试，验证字段命名修复是否正确
 */
public class JsonSerializationTest {

    @Test
    public void testQuerySimilarityRequestSerialization() {
        QuerySimilarityRequest request = new QuerySimilarityRequest();
        request.setQuerys(Collections.singletonList("test"));
        request.setTargets(Arrays.asList("target1", "target2"));
        request.setTopK(5);
        request.setRerank(true);
        
        String json = JSON.toJSONString(request);
        System.out.println("QuerySimilarityRequest JSON: " + json);
        
        // 验证JSON中包含下划线字段名
        assert json.contains("top_k");
        assert json.contains("\"rerank\"");
    }

    @Test
    public void testQuerySimilarityResponseDeserialization() {
        String json = "{\"status\":\"success\",\"data\":[{\"query\":\"test\",\"targets\":[\"target1\"],\"similarity\":[0.9],\"indices\":[0],\"rerank_score\":[1.5]}],\"message\":null}";
        
        QuerySimilarityResponse response = JSON.parseObject(json, QuerySimilarityResponse.class);
        System.out.println("QuerySimilarityResponse: " + response);
        
        assert response != null;
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().size() == 1;
        assert response.getData().get(0).getRerankScore() != null;
        assert response.getData().get(0).getRerankScore().get(0) == 1.5;
    }

    @Test
    public void testAcceptedInformationDiagnosisRequestSerialization() {
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();
        
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application = 
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setWorkInjuryBenefitApplicationForm(Collections.singletonList("form1"));
        application.setIdCard(Collections.singletonList("id1"));
        request.setApplication(application);
        
        AcceptedInformationDiagnosisRequest.MedicalVisit visit = 
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");
        
        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();

        // 创建病历对象
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("record1"));
        materials.setMedicalRecord(medicalRecord);

        materials.setElectronicInvoice(Collections.singletonList("invoice1"));
        visit.setMaterial(materials);
        
        request.setMedical(Collections.singletonList(visit));
        
        String json = JSON.toJSONString(request);
        System.out.println("AcceptedInformationDiagnosisRequest JSON: " + json);
        
        // 验证JSON中包含下划线字段名
        assert json.contains("work_injury_benefit_application_form");
        assert json.contains("id_card");
        assert json.contains("visit_type");
        assert json.contains("visit_date");
        assert json.contains("medical_record");
        assert json.contains("electronic_invoice");
    }

    @Test
    public void testAcceptedInformationDiagnosisResponseDeserialization() {
        String json = "{\"status\":\"success\",\"data\":{\"accident_date\":\"2024-01-01\",\"employee_name\":\"张三\",\"gender\":\"男\",\"ID_number\":\"123456789\",\"employer_name\":\"测试公司\",\"clinical_diagnosis\":[\"诊断1\"]},\"message\":null}";
        
        AcceptedInformationDiagnosisResponse response = JSON.parseObject(json, AcceptedInformationDiagnosisResponse.class);
        System.out.println("AcceptedInformationDiagnosisResponse: " + response);
        
        assert response != null;
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert "2024-01-01".equals(response.getData().getAccidentDate());
        assert "张三".equals(response.getData().getEmployeeName());
        assert "123456789".equals(response.getData().getIdNumber());
        assert "测试公司".equals(response.getData().getEmployerName());
        assert response.getData().getClinicalDiagnosis() != null;
        assert response.getData().getClinicalDiagnosis().contains("诊断1");
    }

    @Test
    public void testSurgicalInformationResponseDeserialization() {
        String json = "{\"status\":\"success\",\"data\":{\"surgical_name\":[\"手术1\",\"手术2\"]},\"message\":null}";
        
        SurgicalInformationResponse response = JSON.parseObject(json, SurgicalInformationResponse.class);
        System.out.println("SurgicalInformationResponse: " + response);
        
        assert response != null;
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().getSurgicalName() != null;
        assert response.getData().getSurgicalName().contains("手术1");
        assert response.getData().getSurgicalName().contains("手术2");
    }

    @Test
    public void testBillInformationResponseDeserialization() {
        String json = "{\"status\":\"success\",\"data\":{\"bill_Information\":[{\"bill_number\":\"123\",\"hospital\":\"医院\",\"treatment_type\":\"住院\",\"bill_amount\":100.0,\"medical_co-ordination_fund\":50,\"admission_time\":\"2024-01-01\",\"discharge_time\":\"2024-01-03\",\"stay_days\":2,\"clinic_time\":null,\"expense_details\":[{\"expense_item\":\"治疗费\",\"amount\":50.0,\"expense_list\":[{\"name\":\"项目1\",\"code\":\"001\",\"quantity\":1,\"unit_price\":50.0,\"amount\":50.0}]}]}]},\"message\":null}";
        
        BillInformationResponse response = JSON.parseObject(json, BillInformationResponse.class);
        System.out.println("BillInformationResponse: " + response);
        
        assert response != null;
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().getBillInformation() != null;
        assert response.getData().getBillInformation().size() == 1;
        
        BillInformationResponse.BillInformation bill = response.getData().getBillInformation().get(0);
        assert "123".equals(bill.getBillNumber());
        assert "住院".equals(bill.getTreatmentType());
        assert bill.getBillAmount().doubleValue() == 100.0;
        assert bill.getMedicalCoOrdinationFund() == 50;
        assert "2024-01-01".equals(bill.getAdmissionTime());
        assert "2024-01-03".equals(bill.getDischargeTime());
        assert bill.getStayDays() == 2;
        
        assert bill.getExpenseDetails() != null;
        assert bill.getExpenseDetails().size() == 1;
        
        BillInformationResponse.ExpenseDetail detail = bill.getExpenseDetails().get(0);
        assert "治疗费".equals(detail.getExpenseItem());
        assert detail.getAmount().doubleValue() == 50.0;
        
        assert detail.getExpenseList() != null;
        assert detail.getExpenseList().size() == 1;
        
        BillInformationResponse.ExpenseList item = detail.getExpenseList().get(0);
        assert "项目1".equals(item.getName());
        assert "001".equals(item.getCode());
        assert item.getQuantity() == 1;
        assert item.getUnitPrice().doubleValue() == 50.0;
        assert item.getAmount().doubleValue() == 50.0;
    }

    @Test
    public void testListOcrResponseDeserialization() {
        String json = "{\"status\":\"success\",\"data\":{\"expense_list\":[{\"name\":\"项目1\",\"expense_item\":\"治疗费\",\"code\":\"001\",\"quantity\":1,\"unit_price\":50.0,\"amount\":50.0}]},\"message\":null}";
        
        ListOcrResponse response = JSON.parseObject(json, ListOcrResponse.class);
        System.out.println("ListOcrResponse: " + response);
        
        assert response != null;
        assert "success".equals(response.getStatus());
        assert response.getData() != null;
        assert response.getData().getExpenseList() != null;
        assert response.getData().getExpenseList().size() == 1;
        
        ListOcrResponse.ExpenseListItem item = response.getData().getExpenseList().get(0);
        assert "项目1".equals(item.getName());
        assert "治疗费".equals(item.getExpenseItem());
        assert "001".equals(item.getCode());
        assert item.getQuantity() == 1;
        assert item.getUnitPrice().doubleValue() == 50.0;
        assert item.getAmount().doubleValue() == 50.0;
    }
}
