package com.yixun.wid.utils;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class AesCbcUtil {

    private static final String ALGORITHM_CBC = "AES/CBC/PKCS5Padding";

    private static final String ALGORITHM_TYPE = "AES";

    public static final String KEY = "AwJ&87sRi!iIRr1M";

    private static final String IV = "AwJ&87sRi!iIRr1M";

    private static final String UTF8 = StandardCharsets.UTF_8.name();

    /**
     * 加密
     *
     * @param data 加密的数据
     * @return
     */
    public static String encrypt(String data) throws Exception {
        //获取实例
        Cipher cilper = Cipher.getInstance(ALGORITHM_CBC);
        //创建 加密的规则
        SecretKey secretKey = new SecretKeySpec(KEY.getBytes(UTF8), ALGORITHM_TYPE);
        //初始化加解密对象，设置key，和加密规则/解密规则
        IvParameterSpec ivParameterSpec = new IvParameterSpec(IV.getBytes(UTF8));
        cilper.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
        byte[] doFinal = cilper.doFinal(data.getBytes(UTF8));
        return Hex.encodeHexString(doFinal);
    }

    /**
     * 解密
     *
     * @param encrypted 需要解密的参数
     * @return
     * @throws Exception
     */
    public static String decrypt(String encrypted) throws Exception {
        //获取实例
        Cipher cilper = Cipher.getInstance(ALGORITHM_CBC);
        //创建 加密的规则
        SecretKey secretKey = new SecretKeySpec(KEY.getBytes(UTF8), ALGORITHM_TYPE);
        //初始化加解密对象，设置key，和加密规则/解密规则
        IvParameterSpec ivParameterSpec = new IvParameterSpec(IV.getBytes(UTF8));
        cilper.init(Cipher.DECRYPT_MODE,secretKey,ivParameterSpec);
        byte[] doFinal = cilper.doFinal( Hex.decodeHex(encrypted));
        return new String(doFinal,UTF8);
    }

    public static void main(String[] args) throws Exception {
        System.out.println(AesCbcUtil.encrypt("fsd3egd#gf34"));
    }

}
