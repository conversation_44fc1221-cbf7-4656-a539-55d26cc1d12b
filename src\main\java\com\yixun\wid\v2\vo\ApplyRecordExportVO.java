package com.yixun.wid.v2.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 申请记录导出数据
 */
@Data
public class ApplyRecordExportVO {

    @ApiModelProperty("序号")
    private Integer serialNumber;

    @ApiModelProperty("用人单位名称")
    private String organizationName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("申请时间")
    private Date submitTime;

    @ApiModelProperty("经办人姓名")
    private String operatorName;

    @ApiModelProperty("经办人手机号码")
    private String operatorPhone;

    @ApiModelProperty("与受伤害职工关系")
    private String relationship;

    @ApiModelProperty("受伤害职工姓名")
    private String injuredWorkerName;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty("受伤害职工手机号码")
    private String injuredWorkerPhone;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("参加工作时间")
    private Date firstWorkDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("事故时间")
    private Date accidentTime;

    @ApiModelProperty("事故地点")
    private String accidentLocation;

    @ApiModelProperty("受伤害部位")
    private String injuredPart;

    @ApiModelProperty("受伤害经过简述")
    private String accidentDescription;

    @ApiModelProperty("就诊信息")
    private String medicalInfo;

    @ApiModelProperty("当前进度状态")
    private String currentStatus;
}
