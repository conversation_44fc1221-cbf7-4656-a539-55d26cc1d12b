# ********************admin-api

工伤申报与调查管理端API

## 项目简介

本项目是工伤申报与调查管理端的后端API服务，提供工伤待遇业务管理、材料分类、账单处理等核心功能。

## 技术栈

- **框架**: Spring Boot 2.7.9
- **数据库**: MySQL + MongoDB + Redis
- **构建工具**: Maven
- **Java版本**: JDK 8+

## 快速开始

### 环境要求

- JDK 8 或更高版本
- Maven 3.6+
- MySQL 5.7+
- MongoDB 4.0+
- Redis 5.0+

### 本地开发

1. 克隆项目
```bash
git clone http://gitlab.yxgsyf.com:8800/widgrp/********************admin-api.git
cd ********************admin-api
```

2. 配置数据库连接
   - 修改 `application-dev.yml` 中的数据库配置

3. 启动项目
```bash
mvn spring-boot:run
```

### 构建部署

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包
mvn clean package -DskipTests
```

## 核心功能

### 工伤待遇管理
- 工伤待遇业务的创建、查询、更新
- 材料分类和识别
- 账单信息处理
- 受理信息和临床诊断

### AI集成服务
- 材料智能分类
- 相似度查找
- OCR文字识别
- 账单和清单识别

### 数据管理
- 医疗机构管理
- 项目费用管理
- 账单明细管理

---

## API文档更新记录

### 2025-07-21 待遇支付API更新

**更新概述**
- API文档时间更新：`2025-07-17` → `2025-07-21`
- 主要涉及材料分类相关API的数据结构优化

**重要变更**

#### 1. 材料分类API响应结构变化
**影响类**: `MaterialClassificationResponse.MedicalMaterials`

- `medical_record` 字段从 `List<String>` 改为复杂的 `MedicalRecord` 对象
- 新增 `MedicalRecord` 内部类，包含11个详细分类字段：
  - `medical_record_subcategory` - 病历小类
  - `medical_condition_certificate` - 病情证明书
  - `discharge_certificate` - 出院证明书
  - `discharge_record` - 出院记录
  - `inpatient_medical_record_first_page` - 住院病案首页
  - `diagnosis_certificate` - 诊断证明书
  - `admission_certificate` - 入院证
  - `admission_record` - 入院记录
  - `prescription_slip` - 处方签(笺)
  - `temperature_sheet` - 体温单
  - `progress_note` - 病程记录

#### 2. 受理信息和临床诊断请求结构变化
**影响类**: `AcceptedInformationDiagnosisRequest.MedicalMaterials`

- `medical_record` 字段结构与上述相同
- `other_consultation_reports` 字段简化为 `List<String>`

**修改文件清单**
- `MaterialClassificationResponse.java` - 核心响应类更新
- `AcceptedInformationDiagnosisRequest.java` - 请求类更新
- `MedicalCasesControllerTest.java` - 测试代码适配
- `JsonSerializationTest.java` - 序列化测试更新
- `ApiStructureTest.java` - 新增结构验证测试

**验证状态**
- ✅ 编译验证通过
- ✅ 结构测试通过
- ✅ JSON序列化包含新字段

**注意事项**
- ⚠️ **破坏性变更**：需要更新所有相关调用代码
- 建议在测试环境充分验证后再部署
- 如有数据库存储旧格式数据，需考虑迁移方案

### 2025-07-21 新增AI接口

**新增接口**
为前端提供了两个新的POST接口，暴露AiUtils中对应的方法：

#### 1. 手术信息识别接口
- **路径**: `POST /v2/medical/cases/surgical-information`
- **参数**: 支持两种方式（二选一）
  - 方式1：`id` (Long) - MedicalCases的ID
  - 方式2：`request` (AcceptedInformationDiagnosisRequest) - 直接传入请求对象
- **功能**: 调用AI接口进行手术信息识别
- **返回**: `SurgicalInformationResponse` - 手术信息识别结果

#### 2. 受理信息和临床诊断接口（已存在，增强功能）
- **路径**: `POST /v2/medical/cases/accepted-information-diagnosis`
- **参数**: 支持两种方式（二选一）
  - 方式1：`id` (Long) - MedicalCases的ID
  - 方式2：`request` (AcceptedInformationDiagnosisRequest) - 直接传入请求对象
- **功能**: 调用AI接口进行受理信息和临床诊断识别
- **返回**: `AcceptedInformationDiagnosisResponse` - 受理信息和临床诊断识别结果

#### 3. 账单信息识别接口（新增）
- **路径**: `POST /v2/medical/cases/bill-information`
- **参数**: 支持两种方式（二选一）
  - 方式1：`id` (Long) - MedicalCases的ID
  - 方式2：`request` (AcceptedInformationDiagnosisRequest) - 直接传入请求对象
- **功能**: 调用AI接口进行账单信息识别
- **返回**: `BillInformationResponse` - 账单信息识别结果

**修改文件清单**
- `MedicalCasesController.java` - 新增手术信息识别接口和账单信息识别接口，增强受理信息和临床诊断接口
- `AiInterfaceTest.java` - 新增接口测试验证，包含参数选项测试和账单信息测试
- `ApiStructureTest.java` - 新增BillInformationResponse结构测试
- `docs/AI_INTERFACES.md` - 更新接口文档，说明三个接口的两种参数方式

**验证状态**
- ✅ 编译验证通过
- ✅ 接口结构测试通过（6个测试用例）
- ✅ VO对象创建测试通过（4个测试用例）
- ✅ 参数选项验证通过
- ✅ 账单信息接口测试通过

---

## 项目结构

```
src/
├── main/java/com/yixun/wid/
│   ├── v2/                          # V2版本API
│   │   ├── controller/              # 控制器层
│   │   │   ├── MedicalCasesController.java    # 工伤待遇业务
│   │   │   ├── BillingInfoController.java     # 账单信息
│   │   │   └── MedicalInstitutionsController.java # 医疗机构
│   │   ├── entity/                  # 实体类
│   │   ├── service/                 # 服务层
│   │   ├── utils/                   # 工具类
│   │   │   └── AiUtils.java         # AI服务工具类
│   │   └── vo/                      # 数据传输对象
│   │       └── ai/                  # AI相关VO
│   │           ├── MaterialClassificationResponse.java
│   │           ├── AcceptedInformationDiagnosisRequest.java
│   │           └── ...
│   ├── config/                      # 配置类
│   ├── interceptor/                 # 拦截器
│   └── utils/                       # 通用工具类
└── test/                           # 测试代码
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok减少样板代码
- 统一使用FastJSON进行JSON处理

### API设计原则
- RESTful API设计
- 统一的响应格式 `CommonResult<T>`
- 完善的参数验证和异常处理

### 测试要求
- 单元测试覆盖率 > 70%
- 集成测试覆盖核心业务流程
- API结构变更必须有对应的测试验证

### 部署注意事项
1. **环境配置**
   - 确保所有外部依赖服务正常运行
   - 检查配置文件中的连接信息

2. **数据库迁移**
   - 执行必要的数据库脚本
   - 备份重要数据

3. **API兼容性**
   - 重大API变更需要版本控制
   - 提供过渡期支持或迁移指南

## 常见问题

### Q: 如何添加新的AI服务接口？
A: 在 `AiUtils.java` 中添加对应的方法，并在 `vo/ai/` 目录下创建相应的请求和响应类。

### Q: 如何处理API结构变更？
A:
1. 更新相关的VO类
2. 修改对应的测试代码
3. 在README.md中记录变更详情
4. 通知相关调用方

### Q: 测试环境如何配置？
A: 使用 `application-test.yml` 配置文件，确保测试数据库与生产环境隔离。

## 联系方式

- 项目负责人：开发团队
- 技术支持：通过GitLab Issues提交问题
- 文档更新：请及时更新README.md中的相关信息

## 更新日志

项目的重要更新和API变更都会记录在本README.md的"API文档更新记录"部分，请定期查看以了解最新变化。
