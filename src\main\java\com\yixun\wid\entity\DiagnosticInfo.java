package com.yixun.wid.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 诊断信息
 */
@Data
public class DiagnosticInfo {

	@ApiModelProperty("就诊医院")
	private String hospital;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("初诊日期")
	private Date firstClinicDate;

	@ApiModelProperty("诊断结果")
	private List<String> diagnoses;

    @ApiModelProperty("就诊方式 0住院 1门诊")
    private Integer type;

    @ApiModelProperty("就诊科室")
    private String diagnosDepartment;

}
